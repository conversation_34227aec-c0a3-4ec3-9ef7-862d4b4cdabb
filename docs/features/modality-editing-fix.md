# Correção do Carregamento de Requisitos de Graduação

## Problema Identificado

Os requisitos de graduação (sessions/aulas) não estavam sendo carregados corretamente nos inputs do formulário de edição de modalidades, apesar de existirem no banco de dados.

## Causa Raiz

A tabela `tenant_belt_level_requirements` possui Row Level Security (RLS) ativo com políticas que dependem da variável `app.current_tenant_id`. A função `getRankRequirements` estava usando `createClient()` em vez de `createAdminClient()`, causando falha na autenticação/autorização.

## Solução Implementada

### 1. Correção da Função de Requirements
- **Arquivo**: `src/services/belts/requirements.ts`
- **Mudança**: Alteração de `createClient()` para `createAdminClient()` nas funções:
  - `getRankRequirements()`
  - `getSingleRankRequirements()`

### 2. Melhoria no Formulário
- **Arquivo**: `src/app/(dashboard)/academia/configuracoes/modalidades/[slug]/editar/components/ModalityEditForm.tsx`
- **Mudanças**:
  - Adicionado `useEffect` para resetar formulário quando dados são atualizados
  - Implementado `useMemo` para otimizar re-renders
  - Adicionada key dinâmica no `RanksFieldArray` para forçar re-renderização

### 3. Correção nos Inputs
- **Arquivo**: `src/app/(dashboard)/academia/configuracoes/modalidades/[slug]/editar/components/RanksFieldArray.tsx`
- **Mudanças**:
  - Adicionado `setValueAs` para tratamento correto de valores numéricos
  - Adicionado `value` prop para garantir sincronização
  - Aplicado para inputs de `sessions` e `minimum_age`

## Verificação no Banco de Dados

```sql
-- Consulta para verificar dados existentes
SELECT 
  tblr.*,
  bl.label as belt_label
FROM tenant_belt_level_requirements tblr
JOIN belt_levels bl ON tblr.belt_level_id = bl.id
WHERE bl.id = '06db67eb-7e0c-4e92-8541-2df2c1ef13e9';
```

**Resultado**: Confirma que o registro existe com `sessions: 50`.

## Políticas RLS Identificadas

```sql
-- Verificar políticas RLS
SELECT policyname, cmd, qual
FROM pg_policies
WHERE tablename = 'tenant_belt_level_requirements';
```

**Políticas encontradas**:
- `Tenant access rank requirements`: Filtra por `app.current_tenant_id`
- `tenant_belt_level_requirements_isolation`: Isolamento por tenant

## Logs Adicionados para Debug

- Logs detalhados no carregamento de requirements
- Acompanhamento de valores no formulário
- Verificação de re-renderizações do `RanksFieldArray`

## Resultado Esperado

Após as correções:
1. ✅ Requirements são carregados corretamente do banco
2. ✅ Inputs mostram valores salvos (ex: 50 aulas)
3. ✅ Formulário atualiza automaticamente após save
4. ✅ Validação funciona corretamente

## Próximos Passos

1. Testar a correção em ambiente de desenvolvimento
2. Verificar se outros serviços têm problemas similares com RLS
3. Considerar padronização do uso de `createAdminClient()` para operações internas
4. Remover logs de debug quando confirmado funcionamento

## Impacto

- **Segurança**: Mantida (RLS continua ativo)
- **Performance**: Melhorada (menos re-renders desnecessários)
- **UX**: Significativamente melhorada (dados carregam corretamente)
- **Manutenibilidade**: Melhorada (logs e estrutura mais clara) 