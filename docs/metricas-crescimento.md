# Documentação das Métricas de Crescimento

## Visão Geral

O sistema de métricas de crescimento do ApexSaaS calcula e projeta receitas baseado em dados históricos de pagamentos e planos ativos. O sistema oferece duas visualizações principais:

1. **Ano-a-data (Year-to-Date)**: Mostra dados reais de receita do início do ano até o mês atual
2. **Projeção**: Combina dados reais com projeções baseadas na média mensal para o ano completo

## Estrutura de Dados

### Interface GrowthMetrics

```typescript
interface GrowthMetrics {
  yearToDateRevenue: number;           // Receita total do ano até agora
  projectedYearRevenue: number;        // Projeção de receita para o ano completo
  totalToReceiveUntilYearEnd: number;  // Total a receber baseado em planos ativos
  monthlyRevenueData: Array<{          // Dados mensais de receita
    month: string;
    revenue: number;
    isProjected?: boolean;
  }>;
  monthlyToReceiveData: Array<{        // Dados mensais de valores a receber
    month: string;
    toReceive: number;
  }>;
}
```

### Interface ActiveMembership

```typescript
interface ActiveMembership {
  id: string;
  status: string;
  start_date: string;
  end_date: string | null;
  next_billing_date: string | null;
  plan: {
    title: string;
    pricing_config: {
      type: 'recurring' | 'one-time' | 'per-session' | 'trial';
      amount: number;
      currency: string;
      frequency?: 'month' | 'week' | 'year';
      frequency_number?: number;
    };
    duration_config: {
      type: 'ongoing' | 'limited' | 'specific';
      end_date?: string;
    };
  };
}
```

## Lógica de Cálculo

### 1. Receita Ano-a-Data (Year-to-Date Revenue)

**Fonte de dados**: Tabela `payments` com status `'paid'`

**Query SQL equivalente**:
```sql
SELECT SUM(amount) 
FROM payments 
WHERE status = 'paid' 
  AND due_date >= '2024-01-01' 
  AND due_date <= '2024-12-31'
```

**Exemplo prático**:
- Ano atual: 2024
- Mês atual: Julho (7)
- Pagamentos realizados:
  - Janeiro: R$ 5.000
  - Fevereiro: R$ 4.500
  - Março: R$ 6.200
  - Abril: R$ 5.800
  - Maio: R$ 6.500
  - Junho: R$ 7.100
  - Julho: R$ 6.900

**Resultado**: `yearToDateRevenue = R$ 42.000`

### 2. Dados Mensais de Receita

**Para meses já passados** (dados reais):
```typescript
// Para cada mês de 1 até currentMonth
const { data: monthData } = await supabase
  .from('payments')
  .select('amount')
  .eq('status', 'paid')
  .gte('due_date', startDate)
  .lte('due_date', endDate);
```

**Para meses futuros** (projeção):
```typescript
// Calcula média dos meses anteriores
const averageRevenue = monthlyData.length > 0 
  ? monthlyData.reduce((sum, m) => sum + m.revenue, 0) / monthlyData.length
  : 0;
```

**Exemplo prático**:
- Meses reais (Jan-Jul): [5000, 4500, 6200, 5800, 6500, 7100, 6900]
- Média mensal: (5000 + 4500 + 6200 + 5800 + 6500 + 7100 + 6900) ÷ 7 = R$ 6.000
- Projeção para meses futuros (Ago-Dez): [6000, 6000, 6000, 6000, 6000]

### 3. Projeção de Receita Anual

**Cálculo**:
```typescript
const projectedYearRevenue = monthlyData.reduce((sum, m) => sum + m.revenue, 0);
```

**Exemplo prático**:
- Receita real (Jan-Jul): R$ 42.000
- Receita projetada (Ago-Dez): R$ 30.000 (5 × R$ 6.000)
- **Total projetado para o ano**: R$ 72.000

### 4. Total a Receber até Final do Ano

Esta métrica calcula quanto a academia ainda vai receber baseado nos planos ativos recorrentes.

**Lógica**:
1. Busca todas as memberships com status `'active'`
2. Para cada membership, verifica o tipo de plano
3. Calcula pagamentos futuros baseado na frequência do plano

**Exemplo prático**:

**Planos ativos em julho de 2024**:
- **Plano A**: R$ 150/mês (mensal) - 10 alunos ativos
- **Plano B**: R$ 1.200/ano (anual, renovação em dezembro) - 5 alunos ativos
- **Plano C**: R$ 300/mês (mensal) - 3 alunos ativos

**Cálculo para planos mensais**:
- Plano A: R$ 150 × 10 alunos × 5 meses restantes = R$ 7.500
- Plano C: R$ 300 × 3 alunos × 5 meses restantes = R$ 4.500

**Cálculo para planos anuais**:
- Plano B: R$ 1.200 × 5 alunos × 1 cobrança (dezembro) = R$ 6.000

**Total a receber**: R$ 18.000

### 5. Dados Mensais de "A Receber"

**Lógica por frequência**:

```typescript
if (frequency === 'month') {
  // Para planos mensais, cobra todo mês
  shouldCharge = true;
} else if (frequency === 'year') {
  // Para planos anuais, cobra apenas no mês de início
  const startDate = new Date(membership.start_date);
  const startMonth = startDate.getMonth() + 1;
  shouldCharge = month === startMonth;
}
```

**Exemplo prático para os meses restantes de 2024**:

| Mês | Plano A (Mensal) | Plano C (Mensal) | Plano B (Anual) | Total Mensal |
|-----|------------------|------------------|-----------------|--------------|
| Ago | R$ 1.500 | R$ 900 | R$ 0 | R$ 2.400 |
| Set | R$ 1.500 | R$ 900 | R$ 0 | R$ 2.400 |
| Out | R$ 1.500 | R$ 900 | R$ 0 | R$ 2.400 |
| Nov | R$ 1.500 | R$ 900 | R$ 0 | R$ 2.400 |
| Dez | R$ 1.500 | R$ 900 | R$ 6.000 | R$ 8.400 |

## Componentes de Visualização

### GrowthMetrics Component

**Métricas exibidas**:

1. **Receita do Período**:
   - Ano-a-data: `metrics.yearToDateRevenue`
   - Projeção: `metrics.projectedYearRevenue`

2. **Receita Média Mensal**:
   - Ano-a-data: `metrics.yearToDateRevenue / currentMonth`
   - Projeção: `metrics.projectedYearRevenue / 12`

3. **Total a Receber** (apenas no modo projeção):
   - Valor: `metrics.totalToReceiveUntilYearEnd`

### GrowthChart Component

**Dados do gráfico**:
- Combina `monthlyRevenueData` com `monthlyToReceiveData`
- Diferencia dados reais (verde) de projeções (laranja)
- Mostra valores "a receber" em roxo

**Filtros por modo**:
- **Ano-a-data**: Mostra apenas meses até o atual
- **Projeção**: Mostra todos os 12 meses do ano

## Considerações Técnicas

### Timezone
- Todos os cálculos usam timezone do Brasil
- Conversões são feitas no código da aplicação

### Performance
- Queries otimizadas com filtros de data
- Cálculos realizados no servidor (server actions)

### Tratamento de Erros
- Validação de tenant
- Fallbacks para dados ausentes
- Logs de erro detalhados

## Casos de Uso

### Cenário 1: Academia Nova (Poucos Dados)
- **Situação**: Academia com apenas 2 meses de operação
- **Comportamento**: Projeção baseada na média dos 2 meses existentes
- **Limitação**: Projeção pode ser imprecisa com poucos dados históricos

### Cenário 2: Academia Sazonal
- **Situação**: Receita varia significativamente por época do ano
- **Comportamento**: Projeção linear pode não refletir sazonalidade
- **Melhoria futura**: Considerar padrões sazonais históricos

### Cenário 3: Mudanças de Preço
- **Situação**: Academia aumentou preços no meio do ano
- **Comportamento**: Projeção considera apenas a média atual
- **Resultado**: Pode subestimar receita futura se preços aumentaram recentemente

## Fluxo de Execução

### 1. Fluxo Principal da Função getGrowthMetrics()

```mermaid
graph TD
    A[Início: getGrowthMetrics] --> B[Extrair Tenant ID]
    B --> C[Criar Cliente Supabase]
    C --> D[Obter Data Atual]
    D --> E[Buscar Receita Ano-a-Data]
    E --> F[Calcular Dados Mensais]
    F --> G[Calcular Projeções]
    G --> H[Calcular Total a Receber]
    H --> I[Retornar Métricas]
```

### 2. Fluxo de Cálculo de Dados Mensais

```mermaid
graph TD
    A[Para cada mês 1-12] --> B{Mês <= Atual?}
    B -->|Sim| C[Buscar Pagamentos Reais]
    B -->|Não| D[Calcular Média Histórica]
    C --> E[Adicionar aos Dados Mensais]
    D --> F[Adicionar como Projeção]
    E --> G[Próximo Mês]
    F --> G
    G --> H{Último Mês?}
    H -->|Não| A
    H -->|Sim| I[Finalizar]
```

### 3. Fluxo de Cálculo "A Receber"

```mermaid
graph TD
    A[Buscar Memberships Ativas] --> B[Para cada Membership]
    B --> C{Plano Recorrente?}
    C -->|Não| D[Pular]
    C -->|Sim| E[Verificar Frequência]
    E --> F{Frequência Mensal?}
    F -->|Sim| G[Adicionar a Todos os Meses]
    F -->|Não| H{Frequência Anual?}
    H -->|Sim| I[Adicionar ao Mês de Renovação]
    H -->|Não| D
    G --> J[Próxima Membership]
    I --> J
    D --> J
    J --> K{Última Membership?}
    K -->|Não| B
    K -->|Sim| L[Retornar Totais]
```

## Exemplos de Código

### Exemplo 1: Cálculo de Receita Mensal

```typescript
// Função para calcular receita de um mês específico
async function calculateMonthRevenue(
  supabase: any,
  year: number,
  month: number
): Promise<number> {
  const monthStr = month.toString().padStart(2, '0');
  const startDate = `${year}-${monthStr}-01`;
  const endDate = new Date(year, month, 0).toISOString().split('T')[0];

  const { data: monthData } = await supabase
    .from('payments')
    .select('amount')
    .eq('status', 'paid')
    .gte('due_date', startDate)
    .lte('due_date', endDate);

  return (monthData || []).reduce(
    (sum, payment) => sum + parseFloat(String(payment.amount || '0')),
    0
  );
}

// Exemplo de uso
const janRevenue = await calculateMonthRevenue(supabase, 2024, 1);
console.log(`Receita de Janeiro: ${formatCurrency(janRevenue)}`);
```

### Exemplo 2: Projeção Baseada em Média

```typescript
// Função para calcular projeção baseada na média histórica
function calculateProjection(historicalData: number[]): number[] {
  const average = historicalData.length > 0
    ? historicalData.reduce((sum, value) => sum + value, 0) / historicalData.length
    : 0;

  const remainingMonths = 12 - historicalData.length;
  return Array(remainingMonths).fill(average);
}

// Exemplo de uso
const realRevenue = [5000, 4500, 6200, 5800, 6500, 7100, 6900]; // Jan-Jul
const projection = calculateProjection(realRevenue);
console.log(`Projeção para meses restantes: ${projection.map(formatCurrency)}`);
```

### Exemplo 3: Cálculo de Cobrança por Frequência

```typescript
// Função para determinar se deve cobrar em um mês específico
function shouldChargeInMonth(
  membership: ActiveMembership,
  targetMonth: number,
  targetYear: number
): boolean {
  const { frequency } = membership.plan.pricing_config;

  if (frequency === 'month') {
    return true; // Cobra todo mês
  }

  if (frequency === 'year') {
    const startDate = new Date(membership.start_date);
    const startMonth = startDate.getMonth() + 1;
    return targetMonth === startMonth; // Cobra apenas no mês de renovação
  }

  return false;
}

// Exemplo de uso
const membership = {
  start_date: '2024-03-15',
  plan: {
    pricing_config: {
      frequency: 'year',
      amount: 1200
    }
  }
};

console.log(shouldChargeInMonth(membership, 3, 2024)); // true (março)
console.log(shouldChargeInMonth(membership, 4, 2024)); // false (abril)
```

## Validações e Tratamento de Erros

### Validações Implementadas

1. **Validação de Tenant**:

```typescript
if (!tenant.id) {
  return { success: false, error: 'Tenant não identificado' };
}
```

2. **Tratamento de Erros de Query**:

```typescript
if (ytdError) {
  console.error('Erro ao buscar receita do ano:', ytdError);
  return { success: false, error: 'Erro ao buscar dados de receita' };
}
```

3. **Fallback para Dados Ausentes**:

```typescript
const yearToDateRevenue = (yearToDateData || []).reduce(
  (sum, payment) => sum + parseFloat(String(payment.amount || '0')),
  0
);
```

### Logs de Debug

```typescript
// Exemplo de logs úteis para debug
console.log('Métricas calculadas:', {
  yearToDateRevenue,
  projectedYearRevenue,
  totalToReceive,
  monthlyDataCount: monthlyData.length,
  activeMembershipsCount: activeMemberships?.length || 0
});
```

## Otimizações e Performance

### Queries Otimizadas

- **Uso de índices**: Queries filtram por `status` e `due_date`
- **Seleção específica**: Apenas campos necessários são selecionados
- **Joins eficientes**: Uso de `inner join` para memberships ativas

### Cálculos no Servidor

- **Server Actions**: Processamento no servidor reduz carga no cliente
- **Cache potencial**: Resultados podem ser cacheados por período

### Lazy Loading

- **Componentes**: Dados carregados apenas quando necessário
- **Estados de loading**: UX melhorada com skeletons

## Melhorias Futuras

### Sazonalidade

- Implementar análise de padrões sazonais
- Ajustar projeções baseado em histórico de anos anteriores

### Tendências

- Calcular taxa de crescimento mensal
- Projeções baseadas em tendências, não apenas médias

### Segmentação

- Métricas por modalidade
- Análise por tipo de plano

### Alertas

- Notificações quando receita está abaixo da projeção
- Alertas de cancelamentos que impactam "a receber"
