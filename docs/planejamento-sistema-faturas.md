# Planejamento Completo: Sistema de Faturas (Invoices)

## 📋 Resumo Executivo

Este documento apresenta o planejamento completo para implementação do sistema de faturas no ApexSaaS, permitindo que academias emitam faturas profissionais para seus alunos com numeração sequencial, dados fiscais e geração de PDF.

## 📊 Análise da Estrutura Atual

### ✅ **Dados Disponíveis:**
- **Pagamentos**: Tabela `payments` com valores, status, datas, métodos
- **Planos**: Tabela `plans` com configurações e títulos
- **Estudantes**: Tabela `students` e `users` com dados pessoais
- **Academia**: Tabela `tenants` com nome e logo
- **Filiais**: Tabela `branches` com endereço, email, telefone
- **Métodos de Pagamento**: Tabela `payment_methods`

### ❌ **Dados Faltantes:**
- **CNPJ da academia**
- **CPF do cliente**
- **Sistema de numeração sequencial de faturas**
- **Estrutura para armazenar snapshots dos dados da fatura**

## 🗄️ Alterações no Banco de Dados

### 1. **Nova Tabela: `invoices`**

Armazena todas as faturas emitidas com snapshot dos dados no momento da emissão.

```sql
CREATE TABLE invoices (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  tenant_id UUID NOT NULL REFERENCES tenants(id),
  payment_id UUID NOT NULL REFERENCES payments(id),
  invoice_number TEXT NOT NULL, -- Formato: #00001, #00002, etc.
  invoice_date DATE NOT NULL DEFAULT CURRENT_DATE,
  due_date DATE,
  status TEXT NOT NULL DEFAULT 'pending', -- pending, paid, overdue, canceled
  subtotal NUMERIC(10,2) NOT NULL,
  discount NUMERIC(10,2) DEFAULT 0,
  total NUMERIC(10,2) NOT NULL,
  
  -- Dados da empresa (snapshot no momento da emissão)
  company_name TEXT NOT NULL,
  company_cnpj TEXT,
  company_email TEXT,
  company_phone TEXT,
  company_address JSONB, -- {street, number, complement, neighborhood, city, state, postal_code}
  
  -- Dados do cliente (snapshot no momento da emissão)
  customer_name TEXT NOT NULL,
  customer_email TEXT,
  customer_cpf TEXT,
  customer_phone TEXT,
  
  -- Dados do plano/serviço
  service_description TEXT NOT NULL,
  service_period_start DATE,
  service_period_end DATE,
  
  -- Metadados
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ,
  
  CONSTRAINT unique_tenant_invoice_number UNIQUE(tenant_id, invoice_number)
);

-- Índices para performance
CREATE INDEX idx_invoices_tenant_id ON invoices(tenant_id);
CREATE INDEX idx_invoices_payment_id ON invoices(payment_id);
CREATE INDEX idx_invoices_status ON invoices(tenant_id, status);
CREATE INDEX idx_invoices_date ON invoices(tenant_id, invoice_date);

-- Row Level Security
ALTER TABLE invoices ENABLE ROW LEVEL SECURITY;
CREATE POLICY "tenant_isolation_invoices" ON invoices
  USING (tenant_id = (auth.jwt() ->> 'tenant_id')::uuid);
```

### 2. **Nova Tabela: `invoice_sequences`**

Controla a numeração sequencial de faturas por tenant.

```sql
CREATE TABLE invoice_sequences (
  tenant_id UUID PRIMARY KEY REFERENCES tenants(id),
  current_number INTEGER NOT NULL DEFAULT 0,
  prefix TEXT DEFAULT '#',
  padding INTEGER DEFAULT 5, -- Para formato #00001
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ
);

-- Row Level Security
ALTER TABLE invoice_sequences ENABLE ROW LEVEL SECURITY;
CREATE POLICY "tenant_isolation_invoice_sequences" ON invoice_sequences
  USING (tenant_id = (auth.jwt() ->> 'tenant_id')::uuid);
```

### 3. **Adicionar Campos em Tabelas Existentes**

```sql
-- Adicionar CNPJ e Razão Social na tabela tenants
ALTER TABLE tenants ADD COLUMN cnpj TEXT;
ALTER TABLE tenants ADD COLUMN razao_social TEXT;

-- Adicionar CPF na tabela students
ALTER TABLE students ADD COLUMN cpf TEXT;
```

### 4. **Funções RPC**

#### **Gerar Número de Fatura:**
```sql
CREATE OR REPLACE FUNCTION generate_invoice_number(p_tenant_id UUID)
RETURNS TEXT AS $$
DECLARE
  v_current_number INTEGER;
  v_prefix TEXT;
  v_padding INTEGER;
  v_invoice_number TEXT;
BEGIN
  -- Buscar ou criar sequência para o tenant
  INSERT INTO invoice_sequences (tenant_id, current_number)
  VALUES (p_tenant_id, 1)
  ON CONFLICT (tenant_id) DO UPDATE SET
    current_number = invoice_sequences.current_number + 1,
    updated_at = NOW()
  RETURNING current_number, prefix, padding INTO v_current_number, v_prefix, v_padding;

  -- Formatar número da fatura
  v_invoice_number := v_prefix || LPAD(v_current_number::TEXT, v_padding, '0');

  RETURN v_invoice_number;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

#### **Criar Fatura a partir de Pagamento:**

```sql
CREATE OR REPLACE FUNCTION create_invoice_from_payment(p_payment_id UUID)
RETURNS JSONB AS $$
DECLARE
  v_payment payments%ROWTYPE;
  v_student students%ROWTYPE;
  v_user users%ROWTYPE;
  v_tenant tenants%ROWTYPE;
  v_branch branches%ROWTYPE;
  v_membership memberships%ROWTYPE;
  v_plan plans%ROWTYPE;
  v_payment_method payment_methods%ROWTYPE;
  v_invoice_number TEXT;
  v_invoice_id UUID;
BEGIN
  -- Buscar dados do pagamento
  SELECT * INTO v_payment FROM payments WHERE id = p_payment_id;
  IF NOT FOUND THEN
    RETURN jsonb_build_object('success', false, 'error', 'Pagamento não encontrado');
  END IF;

  -- Buscar dados relacionados
  SELECT * INTO v_student FROM students WHERE id = v_payment.student_id;
  SELECT * INTO v_user FROM users WHERE id = v_student.user_id;
  SELECT * INTO v_tenant FROM tenants WHERE id = v_payment.tenant_id;
  SELECT * INTO v_branch FROM branches WHERE tenant_id = v_payment.tenant_id AND is_main = true;

  -- Buscar método de pagamento
  IF v_payment.payment_method IS NOT NULL THEN
    SELECT * INTO v_payment_method FROM payment_methods WHERE slug = v_payment.payment_method;
  END IF;

  -- Buscar dados do plano se existir membership
  IF v_payment.membership_id IS NOT NULL THEN
    SELECT * INTO v_membership FROM memberships WHERE id = v_payment.membership_id;
    SELECT * INTO v_plan FROM plans WHERE id = v_membership.plan_id;
  END IF;

  -- Gerar número da fatura
  v_invoice_number := generate_invoice_number(v_payment.tenant_id);

  -- Criar fatura
  INSERT INTO invoices (
    tenant_id, payment_id, invoice_number, invoice_date, due_date,
    status, subtotal, total, company_name, company_cnpj, company_email,
    company_phone, company_address, customer_name, customer_email,
    customer_cpf, service_description, service_period_start, service_period_end,
    metadata
  ) VALUES (
    v_payment.tenant_id,
    p_payment_id,
    v_invoice_number,
    CURRENT_DATE,
    v_payment.due_date,
    v_payment.status,
    v_payment.amount,
    v_payment.amount,
    COALESCE(v_tenant.razao_social, v_tenant.name),
    v_tenant.cnpj,
    v_branch.email,
    v_branch.phone,
    jsonb_build_object(
      'street', v_branch.street,
      'number', v_branch.street_number,
      'complement', v_branch.complement,
      'neighborhood', v_branch.neighborhood,
      'city', v_branch.city,
      'state', v_branch.state,
      'postal_code', v_branch.postal_code
    ),
    v_user.full_name,
    v_user.email,
    v_student.cpf,
    COALESCE(v_plan.title || ' - ' || v_payment.description, v_payment.description),
    v_membership.start_date,
    v_membership.end_date,
    jsonb_build_object(
      'payment_method', COALESCE(v_payment_method.name, v_payment.payment_method),
      'plan_id', v_plan.id,
      'membership_id', v_membership.id
    )
  ) RETURNING id INTO v_invoice_id;

  RETURN jsonb_build_object(
    'success', true,
    'invoice_id', v_invoice_id,
    'invoice_number', v_invoice_number
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

#### **Trigger para Criação Automática:**

```sql
CREATE OR REPLACE FUNCTION auto_create_invoice_on_payment()
RETURNS TRIGGER AS $$
BEGIN
  -- Criar fatura automaticamente quando pagamento for marcado como 'paid'
  IF NEW.status = 'paid' AND OLD.status != 'paid' THEN
    PERFORM create_invoice_from_payment(NEW.id);
  END IF;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_auto_create_invoice
  AFTER UPDATE ON payments
  FOR EACH ROW
  EXECUTE FUNCTION auto_create_invoice_on_payment();
```

## 🏗️ Estrutura da Aplicação

### **Estrutura de Diretórios:**

```text
src/app/(dashboard)/financeiro/faturas/
├── [invoice_id]/
│   ├── page.tsx                 # Página principal da fatura
│   └── pdf/
│       └── route.ts             # API route para gerar PDF
├── components/
│   ├── InvoiceView.tsx          # Componente principal da fatura
│   ├── InvoiceHeader.tsx        # Cabeçalho com logo e dados da empresa
│   ├── InvoiceDetails.tsx       # Detalhes da fatura (número, status, datas)
│   ├── CustomerInfo.tsx         # Dados do cliente
│   ├── ServiceDetails.tsx       # Detalhes do serviço/plano
│   ├── PaymentInfo.tsx          # Informações de pagamento
│   └── InvoiceActions.tsx       # Botões de ação (PDF, etc.)
├── actions/
│   ├── invoice-actions.ts       # Server actions para faturas
│   └── pdf-actions.ts           # Server actions para PDF
├── types/
│   └── invoice-types.ts         # Tipos TypeScript
└── utils/
    ├── invoice-formatter.ts     # Formatadores de dados
    └── pdf-generator.ts         # Gerador de PDF
```

### **Tipos TypeScript:**

```typescript
export interface Invoice {
  id: string
  tenant_id: string
  payment_id: string
  invoice_number: string
  invoice_date: string
  due_date: string | null
  status: 'pending' | 'paid' | 'overdue' | 'canceled'
  subtotal: number
  discount: number
  total: number
  
  // Dados da empresa
  company_name: string
  company_cnpj: string | null
  company_email: string | null
  company_phone: string | null
  company_address: CompanyAddress | null
  
  // Dados do cliente
  customer_name: string
  customer_email: string | null
  customer_cpf: string | null
  customer_phone: string | null
  
  // Dados do serviço
  service_description: string
  service_period_start: string | null
  service_period_end: string | null
  
  // Dados do pagamento
  payment: {
    method: string
    paid_at: string | null
    status: string
  }
  
  metadata: Record<string, any>
  created_at: string
  updated_at: string | null
}

export interface CompanyAddress {
  street: string
  number: string
  complement?: string
  neighborhood: string
  city: string
  state: string
  postal_code: string
}
```

## 🎨 Design da Interface

### **Layout da Fatura:**

```text
┌─────────────────────────────────────────────────────────────┐
│ [Logo da Academia]                                          │
│                                                             │
│ Fatura #00125                                              │
│ Status: ✅ Pago                                            │
│                                                             │
│ Emitido por:                    │ Emitido para:            │
│ ApexDojo Ltda                   │ João Silva               │
│ CNPJ: 12.345.678/0001-90       │ CPF: 123.456.789-00      │
│ <EMAIL>        │ <EMAIL>           │
│                                 │                          │
│ Descrição do plano:                                        │
│ Plano Mensal - Jiu-Jitsu                                  │
│ Período: 01/07/2025 - 31/07/2025                          │
│                                                             │
│ Valor: R$ 129,90                                           │
│ Desconto: R$ 0,00                                          │
│ Total: **R$ 129,90**                                       │
│                                                             │
│ Pagamento via: Pix                                         │
│ Data do pagamento: 01/07/2025                              │
│                                                             │
│ [Botão: Baixar Fatura PDF]  [Botão: Ver Recibo]          │
└─────────────────────────────────────────────────────────────┘
```

## 🔄 Fluxo de Implementação

### **Fase 1: Estrutura do Banco de Dados** ⏱️ 2-3 dias

1. ✅ Criar tabelas `invoices` e `invoice_sequences`
2. ✅ Adicionar campos CNPJ e CPF
3. ✅ Criar funções RPC
4. ✅ Criar triggers automáticos

### **Fase 2: Backend/Server Actions** ⏱️ 3-4 dias

1. Criar tipos TypeScript
2. Implementar server actions
3. Criar sistema de geração automática de faturas

### **Fase 3: Frontend** ⏱️ 4-5 dias

1. Criar componentes da página de fatura
2. Implementar visualização da fatura
3. Adicionar navegação e links

### **Fase 4: Geração de PDF** ⏱️ 2-3 dias

1. Implementar gerador de PDF
2. Criar template de fatura
3. Adicionar funcionalidade de download

### **Fase 5: Integração** ⏱️ 2-3 dias

1. Integrar com páginas existentes
2. Adicionar links e navegação
3. Testes e ajustes finais

**⏱️ Tempo Total Estimado: 13-18 dias**

## 🎯 Funcionalidades

### **Funcionalidades Principais:**

- ✅ Numeração sequencial automática de faturas
- ✅ Snapshot dos dados no momento da emissão
- ✅ Visualização web responsiva da fatura
- ✅ Geração de PDF para download
- ✅ Status de pagamento em tempo real
- ✅ Dados fiscais completos (CNPJ, CPF)
- ✅ Integração com sistema de pagamentos existente

### **Funcionalidades Futuras:**

- 📧 Envio automático por email
- 📊 Relatórios de faturas emitidas
- 🔄 Reenvio de faturas
- 📱 Compartilhamento via WhatsApp
- 🎨 Templates personalizáveis

## 🔐 Considerações de Segurança

- **Row Level Security (RLS)** em todas as tabelas
- **Isolamento por tenant** garantido
- **Snapshots imutáveis** dos dados da fatura
- **Validação de permissões** nos server actions
- **Auditoria completa** de criação e modificações

## 📈 Benefícios

1. **Profissionalização**: Faturas com aparência profissional
2. **Compliance Fiscal**: Dados fiscais organizados
3. **Rastreabilidade**: Histórico completo de faturas
4. **Automatização**: Geração automática ao confirmar pagamento
5. **Experiência do Cliente**: Interface limpa e intuitiva

## 🚀 Próximos Passos

1. **Aprovar o planejamento** com stakeholders
2. **Implementar as migrações** do banco de dados
3. **Criar os tipos TypeScript** e server actions
4. **Desenvolver os componentes** da interface
5. **Implementar a geração de PDF**
6. **Realizar testes** e ajustes finais
7. **Deploy em produção**

---

**Documento criado em:** 19/07/2025  
**Versão:** 1.0  
**Status:** Planejamento Aprovado ✅
