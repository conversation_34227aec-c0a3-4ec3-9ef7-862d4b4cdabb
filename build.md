Coolify
v4.0.0-beta.420.5

Current Team

Root Team
Dashboard
Projects
Servers
Sources
Destinations
S3 Storages
Shared Variables
Notifications
Keys & Tokens
Tags
Terminal
Profile
Teams
Settings
Sponsor us
Feedback
Logout
Deployment
ApexDojo
production
ApexDojo
Running

Configuration
Deployments
Logs
Terminal

Links

Advanced
Redeploy
Stop
Deployment Log
Hide Debug Logs
Deployment is Finished.




2025-Jul-17 14:52:42.384122
Starting deployment of ApexDojo to localhost.
2025-Jul-17 14:52:43.157345
Preparing container with helper image: ghcr.io/coollabsio/coolify-helper:1.0.8.
2025-Jul-17 14:52:43.670086
[CMD]: docker stop --time=30 qckw0o00kssco0s4gsogcw0s
2025-Jul-17 14:52:43.670086
Flag --time has been deprecated, use --timeout instead
2025-Jul-17 14:52:43.675627
Error response from daemon: No such container: qckw0o00kssco0s4gsogcw0s
2025-Jul-17 14:52:44.428266
[CMD]: docker rm -f qckw0o00kssco0s4gsogcw0s
2025-Jul-17 14:52:44.428266
Error response from daemon: No such container: qckw0o00kssco0s4gsogcw0s
2025-Jul-17 14:52:44.987961
[CMD]: docker run -d --network coolify --name qckw0o00kssco0s4gsogcw0s --rm -v /var/run/docker.sock:/var/run/docker.sock ghcr.io/coollabsio/coolify-helper:1.0.8
2025-Jul-17 14:52:44.987961
bd5ef3f771f86f604d5f07e15d560123e317e87c1ad68ca3b3da7b8f4be586a7
2025-Jul-17 14:52:48.042839
[CMD]: docker exec qckw0o00kssco0s4gsogcw0s bash -c 'GIT_SSH_COMMAND="ssh -o ConnectTimeout=30 -p 22 -o Port=22 -o LogLevel=ERROR -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null" git ls-remote **************:apexcode1/apexsaas.git feat/financeiro'
2025-Jul-17 14:52:48.042839
e22ee25bfdd59e92870a01214ad1a2619dc4da34	refs/heads/feat/financeiro
2025-Jul-17 14:52:48.092639
----------------------------------------
2025-Jul-17 14:52:48.104011
Importing **************:apexcode1/apexsaas.git:feat/financeiro (commit sha HEAD) to /artifacts/qckw0o00kssco0s4gsogcw0s.
2025-Jul-17 14:52:48.946986
[CMD]: docker exec qckw0o00kssco0s4gsogcw0s bash -c 'mkdir -p /root/.ssh' && docker exec qckw0o00kssco0s4gsogcw0s bash -c 'echo '********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************' | base64 -d | tee /root/.ssh/id_rsa > /dev/null' && docker exec qckw0o00kssco0s4gsogcw0s bash -c 'chmod 600 /root/.ssh/id_rsa' && docker exec qckw0o00kssco0s4gsogcw0s bash -c 'GIT_SSH_COMMAND="ssh -o ConnectTimeout=30 -p 22 -o Port=22 -o LogLevel=ERROR -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null -i /root/.ssh/id_rsa" git clone -b "feat/financeiro" **************:apexcode1/apexsaas.git /artifacts/qckw0o00kssco0s4gsogcw0s && cd /artifacts/qckw0o00kssco0s4gsogcw0s && GIT_SSH_COMMAND="ssh -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null" git submodule update --init --recursive && cd /artifacts/qckw0o00kssco0s4gsogcw0s && GIT_SSH_COMMAND="ssh -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null" git lfs pull'
2025-Jul-17 14:52:48.946986
Cloning into '/artifacts/qckw0o00kssco0s4gsogcw0s'...
2025-Jul-17 14:52:53.316875
[CMD]: docker exec qckw0o00kssco0s4gsogcw0s bash -c 'cd /artifacts/qckw0o00kssco0s4gsogcw0s && git log -1 e22ee25bfdd59e92870a01214ad1a2619dc4da34 --pretty=%B'
2025-Jul-17 14:52:53.316875
é foda
2025-Jul-17 14:52:58.049195
Pulling & building required images.
2025-Jul-17 14:52:58.475003
[CMD]: docker exec qckw0o00kssco0s4gsogcw0s bash -c 'SOURCE_COMMIT=e22ee25bfdd59e92870a01214ad1a2619dc4da34 COOLIFY_BRANCH=feat/financeiro  docker compose --env-file /artifacts/qckw0o00kssco0s4gsogcw0s/.env --project-name e0008k448wcc8wgogkwkcwss --project-directory /artifacts/qckw0o00kssco0s4gsogcw0s -f /artifacts/qckw0o00kssco0s4gsogcw0s/docker-compose.yml build --pull --no-cache'
2025-Jul-17 14:52:58.475003
Compose can now delegate builds to bake for better performance.
2025-Jul-17 14:52:58.475003
To do so, set COMPOSE_BAKE=true.
2025-Jul-17 14:52:58.777894
#0 building with "default" instance using docker driver
2025-Jul-17 14:52:58.777894
2025-Jul-17 14:52:58.777894
#1 [apexsaas-prod internal] load build definition from Dockerfile
2025-Jul-17 14:52:58.777894
#1 transferring dockerfile: 2.76kB done
2025-Jul-17 14:52:58.777894
#1 DONE 0.0s
2025-Jul-17 14:52:58.777894
2025-Jul-17 14:52:58.777894
#2 [apexsaas-prod internal] load metadata for docker.io/library/node:23-alpine
2025-Jul-17 14:52:58.777894
#2 DONE 0.2s
2025-Jul-17 14:52:58.975535
#3 [apexsaas-prod internal] load .dockerignore
2025-Jul-17 14:52:58.975535
#3 transferring context: 424B done
2025-Jul-17 14:52:58.975535
#3 DONE 0.0s
2025-Jul-17 14:52:58.975535
2025-Jul-17 14:52:58.975535
#4 [apexsaas-prod base 1/3] FROM docker.io/library/node:23-alpine@sha256:a34e14ef1df25b58258956049ab5a71ea7f0d498e41d0b514f4b8de09af09456
2025-Jul-17 14:52:58.975535
#4 CACHED
2025-Jul-17 14:52:58.975535
2025-Jul-17 14:52:58.975535
#5 [apexsaas-prod internal] load build context
2025-Jul-17 14:52:59.907628
#5 transferring context: 6.08MB 1.0s done
2025-Jul-17 14:53:00.066602
#5 DONE 1.1s
2025-Jul-17 14:53:00.066602
2025-Jul-17 14:53:00.066602
#6 [apexsaas-prod base 2/3] RUN apk add --no-cache libc6-compat
2025-Jul-17 14:53:00.066602
#6 0.511 fetch https://dl-cdn.alpinelinux.org/alpine/v3.22/main/x86_64/APKINDEX.tar.gz
2025-Jul-17 14:53:00.066602
#6 0.836 fetch https://dl-cdn.alpinelinux.org/alpine/v3.22/community/x86_64/APKINDEX.tar.gz
2025-Jul-17 14:53:00.951633
#6 2.128 (1/3) Installing musl-obstack (1.2.3-r2)
2025-Jul-17 14:53:01.129265
#6 2.132 (2/3) Installing libucontext (1.3.2-r0)
2025-Jul-17 14:53:01.129265
#6 2.144 (3/3) Installing gcompat (1.1.0-r4)
2025-Jul-17 14:53:01.129265
#6 2.155 OK: 10 MiB in 21 packages
2025-Jul-17 14:53:01.385090
#6 DONE 2.5s
2025-Jul-17 14:53:01.512702
#7 [apexsaas-prod base 3/3] WORKDIR /app
2025-Jul-17 14:53:01.512702
#7 DONE 0.1s
2025-Jul-17 14:53:01.512702
2025-Jul-17 14:53:01.512702
#8 [apexsaas-prod deps 1/2] COPY package.json package-lock.json ./
2025-Jul-17 14:53:01.512702
#8 DONE 0.1s
2025-Jul-17 14:53:01.668421
#9 [apexsaas-prod deps 2/2] RUN npm ci --legacy-peer-deps
2025-Jul-17 14:53:05.988391
#9 4.451 npm warn EBADENGINE Unsupported engine {
2025-Jul-17 14:53:05.988391
#9 4.451 npm warn EBADENGINE   package: '@jest/diff-sequences@30.0.1',
2025-Jul-17 14:53:05.988391
#9 4.451 npm warn EBADENGINE   required: { node: '^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0' },
2025-Jul-17 14:53:05.988391
#9 4.451 npm warn EBADENGINE   current: { node: 'v23.11.1', npm: '10.9.2' }
2025-Jul-17 14:53:05.988391
#9 4.451 npm warn EBADENGINE }
2025-Jul-17 14:53:06.178598
#9 4.466 npm warn EBADENGINE Unsupported engine {
2025-Jul-17 14:53:06.178598
#9 4.466 npm warn EBADENGINE   package: '@jest/expect-utils@30.0.2',
2025-Jul-17 14:53:06.178598
#9 4.466 npm warn EBADENGINE   required: { node: '^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0' },
2025-Jul-17 14:53:06.178598
#9 4.466 npm warn EBADENGINE   current: { node: 'v23.11.1', npm: '10.9.2' }
2025-Jul-17 14:53:06.178598
#9 4.466 npm warn EBADENGINE }
2025-Jul-17 14:53:06.178598
#9 4.466 npm warn EBADENGINE Unsupported engine {
2025-Jul-17 14:53:06.178598
#9 4.466 npm warn EBADENGINE   package: '@jest/get-type@30.0.1',
2025-Jul-17 14:53:06.178598
#9 4.466 npm warn EBADENGINE   required: { node: '^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0' },
2025-Jul-17 14:53:06.178598
#9 4.466 npm warn EBADENGINE   current: { node: 'v23.11.1', npm: '10.9.2' }
2025-Jul-17 14:53:06.178598
#9 4.466 npm warn EBADENGINE }
2025-Jul-17 14:53:06.178598
#9 4.469 npm warn EBADENGINE Unsupported engine {
2025-Jul-17 14:53:06.178598
#9 4.469 npm warn EBADENGINE   package: '@jest/pattern@30.0.1',
2025-Jul-17 14:53:06.178598
#9 4.469 npm warn EBADENGINE   required: { node: '^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0' },
2025-Jul-17 14:53:06.178598
#9 4.469 npm warn EBADENGINE   current: { node: 'v23.11.1', npm: '10.9.2' }
2025-Jul-17 14:53:06.178598
#9 4.469 npm warn EBADENGINE }
2025-Jul-17 14:53:06.178598
#9 4.470 npm warn EBADENGINE Unsupported engine {
2025-Jul-17 14:53:06.178598
#9 4.470 npm warn EBADENGINE   package: '@jest/schemas@30.0.1',
2025-Jul-17 14:53:06.178598
#9 4.470 npm warn EBADENGINE   required: { node: '^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0' },
2025-Jul-17 14:53:06.178598
#9 4.470 npm warn EBADENGINE   current: { node: 'v23.11.1', npm: '10.9.2' }
2025-Jul-17 14:53:06.178598
#9 4.470 npm warn EBADENGINE }
2025-Jul-17 14:53:06.178598
#9 4.475 npm warn EBADENGINE Unsupported engine {
2025-Jul-17 14:53:06.178598
#9 4.475 npm warn EBADENGINE   package: '@jest/types@30.0.1',
2025-Jul-17 14:53:06.178598
#9 4.475 npm warn EBADENGINE   required: { node: '^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0' },
2025-Jul-17 14:53:06.178598
#9 4.475 npm warn EBADENGINE   current: { node: 'v23.11.1', npm: '10.9.2' }
2025-Jul-17 14:53:06.178598
#9 4.475 npm warn EBADENGINE }
2025-Jul-17 14:53:06.178598
#9 4.482 npm warn EBADENGINE Unsupported engine {
2025-Jul-17 14:53:06.178598
#9 4.482 npm warn EBADENGINE   package: 'expect@30.0.2',
2025-Jul-17 14:53:06.178598
#9 4.482 npm warn EBADENGINE   required: { node: '^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0' },
2025-Jul-17 14:53:06.178598
#9 4.482 npm warn EBADENGINE   current: { node: 'v23.11.1', npm: '10.9.2' }
2025-Jul-17 14:53:06.178598
#9 4.482 npm warn EBADENGINE }
2025-Jul-17 14:53:06.178598
#9 4.489 npm warn EBADENGINE Unsupported engine {
2025-Jul-17 14:53:06.178598
#9 4.489 npm warn EBADENGINE   package: 'jest-diff@30.0.2',
2025-Jul-17 14:53:06.178598
#9 4.489 npm warn EBADENGINE   required: { node: '^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0' },
2025-Jul-17 14:53:06.178598
#9 4.489 npm warn EBADENGINE   current: { node: 'v23.11.1', npm: '10.9.2' }
2025-Jul-17 14:53:06.178598
#9 4.489 npm warn EBADENGINE }
2025-Jul-17 14:53:06.178598
#9 4.495 npm warn EBADENGINE Unsupported engine {
2025-Jul-17 14:53:06.178598
#9 4.495 npm warn EBADENGINE   package: 'jest-matcher-utils@30.0.2',
2025-Jul-17 14:53:06.178598
#9 4.495 npm warn EBADENGINE   required: { node: '^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0' },
2025-Jul-17 14:53:06.178598
#9 4.495 npm warn EBADENGINE   current: { node: 'v23.11.1', npm: '10.9.2' }
2025-Jul-17 14:53:06.178598
#9 4.495 npm warn EBADENGINE }
2025-Jul-17 14:53:06.178598
#9 4.501 npm warn EBADENGINE Unsupported engine {
2025-Jul-17 14:53:06.178598
#9 4.501 npm warn EBADENGINE   package: 'jest-message-util@30.0.2',
2025-Jul-17 14:53:06.178598
#9 4.501 npm warn EBADENGINE   required: { node: '^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0' },
2025-Jul-17 14:53:06.178598
#9 4.501 npm warn EBADENGINE   current: { node: 'v23.11.1', npm: '10.9.2' }
2025-Jul-17 14:53:06.178598
#9 4.501 npm warn EBADENGINE }
2025-Jul-17 14:53:06.178598
#9 4.501 npm warn EBADENGINE Unsupported engine {
2025-Jul-17 14:53:06.178598
#9 4.501 npm warn EBADENGINE   package: 'jest-mock@30.0.2',
2025-Jul-17 14:53:06.178598
#9 4.501 npm warn EBADENGINE   required: { node: '^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0' },
2025-Jul-17 14:53:06.178598
#9 4.501 npm warn EBADENGINE   current: { node: 'v23.11.1', npm: '10.9.2' }
2025-Jul-17 14:53:06.178598
#9 4.501 npm warn EBADENGINE }
2025-Jul-17 14:53:06.178598
#9 4.501 npm warn EBADENGINE Unsupported engine {
2025-Jul-17 14:53:06.178598
#9 4.501 npm warn EBADENGINE   package: 'jest-regex-util@30.0.1',
2025-Jul-17 14:53:06.178598
#9 4.501 npm warn EBADENGINE   required: { node: '^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0' },
2025-Jul-17 14:53:06.178598
#9 4.501 npm warn EBADENGINE   current: { node: 'v23.11.1', npm: '10.9.2' }
2025-Jul-17 14:53:06.178598
#9 4.501 npm warn EBADENGINE }
2025-Jul-17 14:53:06.178598
#9 4.501 npm warn EBADENGINE Unsupported engine {
2025-Jul-17 14:53:06.178598
#9 4.501 npm warn EBADENGINE   package: 'jest-util@30.0.2',
2025-Jul-17 14:53:06.178598
#9 4.501 npm warn EBADENGINE   required: { node: '^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0' },
2025-Jul-17 14:53:06.178598
#9 4.501 npm warn EBADENGINE   current: { node: 'v23.11.1', npm: '10.9.2' }
2025-Jul-17 14:53:06.178598
#9 4.501 npm warn EBADENGINE }
2025-Jul-17 14:53:06.178598
#9 4.504 npm warn EBADENGINE Unsupported engine {
2025-Jul-17 14:53:06.178598
#9 4.504 npm warn EBADENGINE   package: 'pretty-format@30.0.2',
2025-Jul-17 14:53:06.178598
#9 4.504 npm warn EBADENGINE   required: { node: '^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0' },
2025-Jul-17 14:53:06.178598
#9 4.504 npm warn EBADENGINE   current: { node: 'v23.11.1', npm: '10.9.2' }
2025-Jul-17 14:53:06.178598
#9 4.504 npm warn EBADENGINE }
2025-Jul-17 14:53:16.079700
#9 14.56 npm warn deprecated node-domexception@1.0.0: Use your platform's native DOMException instead
2025-Jul-17 14:53:54.222588
#9 52.71
2025-Jul-17 14:53:54.222588
#9 52.71 added 459 packages, and audited 460 packages in 52s
2025-Jul-17 14:53:54.222588
#9 52.71
2025-Jul-17 14:53:54.222588
#9 52.71 79 packages are looking for funding
2025-Jul-17 14:53:54.222588
#9 52.71   run `npm fund` for details
2025-Jul-17 14:53:54.382947
#9 52.71
2025-Jul-17 14:53:54.382947
#9 52.71 found 0 vulnerabilities
2025-Jul-17 14:53:54.382947
#9 52.72 npm notice
2025-Jul-17 14:53:54.382947
#9 52.72 npm notice New major version of npm available! 10.9.2 -> 11.4.2
2025-Jul-17 14:53:54.382947
#9 52.72 npm notice Changelog: https://github.com/npm/cli/releases/tag/v11.4.2
2025-Jul-17 14:53:54.382947
#9 52.72 npm notice To update run: npm install -g npm@11.4.2
2025-Jul-17 14:53:54.382947
#9 52.72 npm notice
2025-Jul-17 14:53:55.005253
#9 DONE 53.5s
2025-Jul-17 14:54:08.393179
#10 [apexsaas-prod builder 1/7] COPY --from=deps /app/node_modules ./node_modules
2025-Jul-17 14:54:20.398237
#10 DONE 12.0s
2025-Jul-17 14:54:20.553448
#11 [apexsaas-prod builder 2/7] COPY . .
2025-Jul-17 14:54:20.670125
#11 DONE 0.3s
2025-Jul-17 14:54:20.824582
#12 [apexsaas-prod builder 3/7] RUN test -n "https://rfvzmrfzxlkyxgbmyptw.supabase.co" || (echo "Erro: O argumento de build NEXT_PUBLIC_SUPABASE_URL é obrigatório." && exit 1)
2025-Jul-17 14:54:20.925956
#12 DONE 0.3s
2025-Jul-17 14:54:21.080580
#13 [apexsaas-prod builder 4/7] RUN test -n "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJmdnptcmZ6eGxreXhnYm15cHR3Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDA4NzQ3MjksImV4cCI6MjA1NjQ1MDcyOX0.rj8IqgQ1T3eXs449nWOclwMSq8INPpjIhkon6zpvobk" || (echo "Erro: O argumento de build NEXT_PUBLIC_SUPABASE_ANON_KEY é obrigatório." && exit 1)
2025-Jul-17 14:54:21.291562
#13 DONE 0.4s
2025-Jul-17 14:54:21.450992
#14 [apexsaas-prod builder 5/7] RUN test -n "uy7F8uCGqq5s7XhHTDRbjfvV" || (echo "Erro: O argumento de build NEXT_PUBLIC_CHATWOOT_WEBSITE_TOKEN é obrigatório." && exit 1)
2025-Jul-17 14:54:21.595470
#14 DONE 0.3s
2025-Jul-17 14:54:21.745201
#15 [apexsaas-prod builder 6/7] RUN test -n "https://chatwoot.sondtheanime.site" || (echo "Erro: O argumento de build NEXT_PUBLIC_CHATWOOT_BASE_URL é obrigatório." && exit 1)
2025-Jul-17 14:54:21.962905
#15 DONE 0.4s
2025-Jul-17 14:54:22.119311
#16 [apexsaas-prod builder 7/7] RUN npm run build
2025-Jul-17 14:54:23.279017
#16 1.313
2025-Jul-17 14:54:23.279017
#16 1.313 > build
2025-Jul-17 14:54:23.279017
#16 1.313 > next build
2025-Jul-17 14:54:23.279017
#16 1.313
2025-Jul-17 14:54:24.889615
#16 2.923  ⚠ Found lockfile missing swc dependencies, patching...
2025-Jul-17 14:54:25.341270
#16 3.373 Attention: Next.js now collects completely anonymous telemetry regarding usage.
2025-Jul-17 14:54:25.490888
#16 3.374 This information is used to shape Next.js' roadmap and prioritize features.
2025-Jul-17 14:54:25.490888
#16 3.374 You can learn more, including how to opt-out if you'd not like to participate in this anonymous program, by visiting the following URL:
2025-Jul-17 14:54:25.490888
#16 3.374 https://nextjs.org/telemetry
2025-Jul-17 14:54:25.490888
#16 3.374
2025-Jul-17 14:54:25.652150
#16 3.685    ▲ Next.js 15.3.4
2025-Jul-17 14:54:25.803261
#16 3.685    - Experiments (use with caution):
2025-Jul-17 14:54:25.803261
#16 3.686      ✓ optimizeCss
2025-Jul-17 14:54:25.803261
#16 3.686
2025-Jul-17 14:54:25.932569
#16 3.964    Creating an optimized production build ...
2025-Jul-17 14:54:30.356173
#16 8.390  ⚠ Lockfile was successfully patched, please run "npm install" to ensure @next/swc dependencies are downloaded
2025-Jul-17 14:58:05.242210
#16 223.3  ✓ Compiled successfully in 3.6min
2025-Jul-17 14:58:05.463252
#16 223.3    Skipping validation of types
2025-Jul-17 14:58:05.463252
#16 223.3    Linting ...
2025-Jul-17 14:58:06.204098
#16 224.2    Collecting page data ...
2025-Jul-17 14:58:10.310240
#16 228.3  ⚠ Using edge runtime on a page currently disables static generation for that page
2025-Jul-17 14:58:26.432799
#16 244.5    Generating static pages (0/18) ...
2025-Jul-17 14:58:28.816837
#16 246.8    Generating static pages (4/18)
2025-Jul-17 14:58:28.968888
#16 246.9    Generating static pages (8/18)
2025-Jul-17 14:58:29.004497
#16 247.0    Generating static pages (13/18)
2025-Jul-17 14:58:29.155378
#16 247.0  ✓ Generating static pages (18/18)
2025-Jul-17 14:58:30.092112
#16 248.1    Finalizing page optimization ...
2025-Jul-17 14:58:30.092112
#16 248.1    Collecting build traces ...
2025-Jul-17 14:59:01.383878
#16 279.4
2025-Jul-17 14:59:01.602411
#16 279.5 Route (app)                                               Size  First Load JS
2025-Jul-17 14:59:01.602411
#16 279.5 ┌ ƒ /                                                    135 B        1.01 MB
2025-Jul-17 14:59:01.602411
#16 279.5 ├ ƒ /_not-found                                          201 B        1.01 MB
2025-Jul-17 14:59:01.602411
#16 279.5 ├ ƒ /academia                                            135 B        1.01 MB
2025-Jul-17 14:59:01.602411
#16 279.5 ├ ƒ /academia/configuracoes                              987 B        1.01 MB
2025-Jul-17 14:59:01.602411
#16 279.5 ├ ƒ /academia/configuracoes/dominio-email              1.43 kB        1.01 MB
2025-Jul-17 14:59:01.602411
#16 279.5 ├ ƒ /academia/configuracoes/graduacoes                   512 B        1.01 MB
2025-Jul-17 14:59:01.602411
#16 279.5 ├ ƒ /academia/configuracoes/migracao                   2.26 kB        1.01 MB
2025-Jul-17 14:59:01.602411
#16 279.5 ├ ƒ /academia/configuracoes/modalidades                1.92 kB        1.01 MB
2025-Jul-17 14:59:01.602411
#16 279.5 ├ ƒ /academia/configuracoes/modalidades/[slug]/editar  7.74 kB        1.01 MB
2025-Jul-17 14:59:01.602411
#16 279.5 ├ ƒ /academia/configuracoes/responsaveis               2.46 kB        1.01 MB
2025-Jul-17 14:59:01.602411
#16 279.5 ├ ƒ /agenda                                            7.31 kB        1.01 MB
2025-Jul-17 14:59:01.602411
#16 279.5 ├ ƒ /alunos                                            12.4 kB        1.02 MB
2025-Jul-17 14:59:01.602411
#16 279.5 ├ ƒ /alunos/configuracoes                              1.12 kB        1.01 MB
2025-Jul-17 14:59:01.602411
#16 279.5 ├ ƒ /alunos/importar                                   8.57 kB        1.01 MB
2025-Jul-17 14:59:01.602411
#16 279.5 ├ ƒ /alunos/novo                                       7.61 kB        1.01 MB
2025-Jul-17 14:59:01.602411
#16 279.5 ├ ƒ /api/ai/chat                                         135 B        1.01 MB
2025-Jul-17 14:59:01.602411
#16 279.5 ├ ƒ /api/ai/usage                                        135 B        1.01 MB
2025-Jul-17 14:59:01.602411
#16 279.5 ├ ƒ /api/auth/reset-password                             135 B        1.01 MB
2025-Jul-17 14:59:01.602411
#16 279.5 ├ ƒ /api/auth/sign-in                                    135 B        1.01 MB
2025-Jul-17 14:59:01.602411
#16 279.5 ├ ƒ /api/auth/sign-out                                   135 B        1.01 MB
2025-Jul-17 14:59:01.602411
#16 279.5 ├ ƒ /api/auth/sign-up                                    135 B        1.01 MB
2025-Jul-17 14:59:01.602411
#16 279.5 ├ ƒ /api/auth/user-welcome-data                          134 B        1.01 MB
2025-Jul-17 14:59:01.602411
#16 279.5 ├ ƒ /api/check-favicon                                   135 B        1.01 MB
2025-Jul-17 14:59:01.602411
#16 279.5 ├ ƒ /api/cron/update-class-status                        135 B        1.01 MB
2025-Jul-17 14:59:01.602411
#16 279.5 ├ ƒ /api/favicon                                         135 B        1.01 MB
2025-Jul-17 14:59:01.602411
#16 279.5 ├ ƒ /api/instructors/[instructorId]                      133 B        1.01 MB
2025-Jul-17 14:59:01.602411
#16 279.5 ├ ƒ /api/instructors/[instructorId]/class-groups         134 B        1.01 MB
2025-Jul-17 14:59:01.602411
#16 279.5 ├ ƒ /api/instructors/[instructorId]/toggle-status        172 B        1.01 MB
2025-Jul-17 14:59:01.602411
#16 279.5 ├ ƒ /api/instructors/email-availability                  135 B        1.01 MB
2025-Jul-17 14:59:01.602411
#16 279.5 ├ ƒ /api/instructors/query                               135 B        1.01 MB
2025-Jul-17 14:59:01.602411
#16 279.5 ├ ƒ /api/permissions/check                               135 B        1.01 MB
2025-Jul-17 14:59:01.602411
#16 279.5 ├ ƒ /api/plans/available                                 135 B        1.01 MB
2025-Jul-17 14:59:01.602411
#16 279.5 ├ ƒ /api/students/attendance                             134 B        1.01 MB
2025-Jul-17 14:59:01.602411
#16 279.5 ├ ƒ /api/students/email-availability                     135 B        1.01 MB
2025-Jul-17 14:59:01.602411
#16 279.5 ├ ƒ /api/students/email-availability/bulk                135 B        1.01 MB
2025-Jul-17 14:59:01.602411
#16 279.5 ├ ƒ /api/students/query                                  135 B        1.01 MB
2025-Jul-17 14:59:01.602411
#16 279.5 ├ ƒ /api/tenant/[slug]                                   135 B        1.01 MB
2025-Jul-17 14:59:01.602411
#16 279.5 ├ ƒ /api/tenant/[slug]/carousel                          135 B        1.01 MB
2025-Jul-17 14:59:01.602411
#16 279.5 ├ ƒ /api/tenant/[slug]/id                                135 B        1.01 MB
2025-Jul-17 14:59:01.602411
#16 279.5 ├ ƒ /api/tenant/[slug]/theme                             134 B        1.01 MB
2025-Jul-17 14:59:01.602411
#16 279.5 ├ ƒ /api/user/[userId]/attendance/history                135 B        1.01 MB
2025-Jul-17 14:59:01.602411
#16 279.5 ├ ƒ /api/user/[userId]/attendance/stats                  135 B        1.01 MB
2025-Jul-17 14:59:01.602411
#16 279.5 ├ ƒ /api/user/[userId]/belts/details                     135 B        1.01 MB
2025-Jul-17 14:59:01.602411
#16 279.5 ├ ƒ /api/user/[userId]/belts/history                     135 B        1.01 MB
2025-Jul-17 14:59:01.602411
#16 279.5 ├ ƒ /api/user/[userId]/belts/requirements                135 B        1.01 MB
2025-Jul-17 14:59:01.602411
#16 279.5 ├ ƒ /api/user/[userId]/belts/stats                       135 B        1.01 MB
2025-Jul-17 14:59:01.602411
#16 279.5 ├ ƒ /api/user/[userId]/change-plan                       135 B        1.01 MB
2025-Jul-17 14:59:01.602411
#16 279.5 ├ ƒ /api/user/[userId]/current-plan                      135 B        1.01 MB
2025-Jul-17 14:59:01.602411
#16 279.5 ├ ƒ /api/user/[userId]/metadata/consistency              135 B        1.01 MB
2025-Jul-17 14:59:01.602411
#16 279.5 ├ ƒ /api/user/[userId]/metadata/sync                     135 B        1.01 MB
2025-Jul-17 14:59:01.602411
#16 279.5 ├ ƒ /api/user/[userId]/payments/history                  135 B        1.01 MB
2025-Jul-17 14:59:01.602411
#16 279.5 ├ ƒ /api/user/[userId]/payments/info                     135 B        1.01 MB
2025-Jul-17 14:59:01.602411
#16 279.5 ├ ƒ /api/user/[userId]/payments/upcoming                 135 B        1.01 MB
2025-Jul-17 14:59:01.602411
#16 279.5 ├ ƒ /api/user/[userId]/profile                           135 B        1.01 MB
2025-Jul-17 14:59:01.602411
#16 279.5 ├ ƒ /api/user/[userId]/role                              135 B        1.01 MB
2025-Jul-17 14:59:01.602411
#16 279.5 ├ ƒ /aulas/calendario                                    954 B        1.01 MB
2025-Jul-17 14:59:01.602411
#16 279.5 ├ ƒ /aulas/checkin/qr/[codigo]                         2.77 kB        1.01 MB
2025-Jul-17 14:59:01.602411
#16 279.5 ├ ƒ /aulas/livres                                      4.86 kB        1.01 MB
2025-Jul-17 14:59:01.602411
#16 279.5 ├ ƒ /aulas/livres/editar/[classId]                     5.28 kB        1.01 MB
2025-Jul-17 14:59:01.602411
#16 279.5 ├ ƒ /aulas/livres/nova                                 2.48 kB        1.01 MB
2025-Jul-17 14:59:01.602411
#16 279.5 ├ ƒ /auth/callback                                       135 B        1.01 MB
2025-Jul-17 14:59:01.602411
#16 279.5 ├ ƒ /checkin                                            9.8 kB        1.01 MB
2025-Jul-17 14:59:01.602411
#16 279.5 ├ ƒ /checkout/[paymentId]                              4.96 kB        1.01 MB
2025-Jul-17 14:59:01.602411
#16 279.5 ├ ƒ /dashboard                                           134 B        1.01 MB
2025-Jul-17 14:59:01.602411
#16 279.5 ├ ƒ /financeiro                                          135 B        1.01 MB
2025-Jul-17 14:59:01.602411
#16 279.5 ├ ƒ /financeiro/configuracoes                           3.2 kB        1.01 MB
2025-Jul-17 14:59:01.602411
#16 279.5 ├ ƒ /financeiro/contabilidade                            135 B        1.01 MB
2025-Jul-17 14:59:01.602411
#16 279.5 ├ ƒ /financeiro/crescimento                              135 B        1.01 MB
2025-Jul-17 14:59:01.602411
#16 279.5 ├ ƒ /financeiro/descontos                                135 B        1.01 MB
2025-Jul-17 14:59:01.602411
#16 279.5 ├ ƒ /financeiro/formas-pagamento                       2.68 kB        1.01 MB
2025-Jul-17 14:59:01.602411
#16 279.5 ├ ƒ /financeiro/mensalidades                           3.67 kB        1.01 MB
2025-Jul-17 14:59:01.602411
#16 279.5 ├ ƒ /financeiro/pagamentos                             5.36 kB        1.01 MB
2025-Jul-17 14:59:01.602411
#16 279.5 ├ ƒ /financeiro/recorrentes                            8.53 kB        1.01 MB
2025-Jul-17 14:59:01.602411
#16 279.5 ├ ƒ /financeiro/recorrentes/planos                     5.73 kB        1.01 MB
2025-Jul-17 14:59:01.602411
#16 279.5 ├ ƒ /financeiro/recorrentes/planos/[id]/editar         1.35 kB        1.01 MB
2025-Jul-17 14:59:01.602411
#16 279.5 ├ ƒ /financeiro/recorrentes/planos/criar               1.34 kB        1.01 MB
2025-Jul-17 14:59:01.602411
#16 279.5 ├ ƒ /home                                                507 B        1.01 MB
2025-Jul-17 14:59:01.602411
#16 279.5 ├ ƒ /instrutores                                       10.9 kB        1.02 MB
2025-Jul-17 14:59:01.602411
#16 279.5 ├ ƒ /instrutores/novo                                    11 kB        1.02 MB
2025-Jul-17 14:59:01.602411
#16 279.5 ├ ƒ /login                                               826 B        1.01 MB
2025-Jul-17 14:59:01.602411
#16 279.5 ├ ƒ /perfil                                              135 B        1.01 MB
2025-Jul-17 14:59:01.602411
#16 279.5 ├ ƒ /perfil/[userId]                                   50.2 kB        1.06 MB
2025-Jul-17 14:59:01.602411
#16 279.5 ├ ƒ /perfil/configuracoes                                889 B        1.01 MB
2025-Jul-17 14:59:01.602411
#16 279.5 ├ ƒ /presenca                                          9.35 kB        1.01 MB
2025-Jul-17 14:59:01.602411
#16 279.5 ├ ƒ /presenca/[classId]                                13.1 kB        1.02 MB
2025-Jul-17 14:59:01.602411
#16 279.5 ├ ƒ /reset-password                                      643 B        1.01 MB
2025-Jul-17 14:59:01.602411
#16 279.5 ├ ƒ /tenant/[slug]                                       135 B        1.01 MB
2025-Jul-17 14:59:01.602411
#16 279.5 ├ ƒ /turmas                                            7.85 kB        1.01 MB
2025-Jul-17 14:59:01.602411
#16 279.5 ├ ƒ /turmas/[groupId]                                    135 B        1.01 MB
2025-Jul-17 14:59:01.602411
#16 279.5 ├ ƒ /turmas/[groupId]/alunos                           18.1 kB        1.02 MB
2025-Jul-17 14:59:01.602411
#16 279.5 ├ ƒ /turmas/[groupId]/aulas                            8.99 kB        1.01 MB
2025-Jul-17 14:59:01.602411
#16 279.5 ├ ƒ /turmas/[groupId]/aulas/editar/[classId]           5.06 kB        1.01 MB
2025-Jul-17 14:59:01.602411
#16 279.5 ├ ƒ /turmas/[groupId]/aulas/nova                       2.63 kB        1.01 MB
2025-Jul-17 14:59:01.602411
#16 279.5 ├ ƒ /turmas/[groupId]/editar                             233 B        1.01 MB
2025-Jul-17 14:59:01.602411
#16 279.5 └ ƒ /turmas/nova                                       6.51 kB        1.01 MB
2025-Jul-17 14:59:01.602411
#16 279.5 + First Load JS shared by all                             1 MB
2025-Jul-17 14:59:01.602411
#16 279.5   ├ chunks/common-c39299c177d3e6d6.js                   124 kB
2025-Jul-17 14:59:01.602411
#16 279.5   └ chunks/vendors-30fbcf8a6656070a.js                  879 kB
2025-Jul-17 14:59:01.602411
#16 279.5   └ other shared chunks (total)                        2.12 kB
2025-Jul-17 14:59:01.602411
#16 279.5
2025-Jul-17 14:59:01.602411
#16 279.5
2025-Jul-17 14:59:01.602411
#16 279.5 ƒ Middleware                                             68 kB
2025-Jul-17 14:59:01.602411
#16 279.5
2025-Jul-17 14:59:01.602411
#16 279.5 ƒ  (Dynamic)  server-rendered on demand
2025-Jul-17 14:59:01.602411
#16 279.5
2025-Jul-17 14:59:01.899233
#16 279.9 npm notice
2025-Jul-17 14:59:01.899233
#16 279.9 npm notice New major version of npm available! 10.9.2 -> 11.4.2
2025-Jul-17 14:59:01.899233
#16 279.9 npm notice Changelog: https://github.com/npm/cli/releases/tag/v11.4.2
2025-Jul-17 14:59:01.899233
#16 279.9 npm notice To update run: npm install -g npm@11.4.2
2025-Jul-17 14:59:01.899233
#16 279.9 npm notice
2025-Jul-17 14:59:02.232209
#16 DONE 280.3s
2025-Jul-17 14:59:03.234755
2025-Jul-17 14:59:03.240782
#17 [apexsaas-prod production 1/4] COPY --from=builder /app/.next/standalone ./
2025-Jul-17 14:59:04.145384
#17 DONE 0.9s
2025-Jul-17 14:59:05.595832
#18 [apexsaas-prod production 2/4] COPY --from=builder /app/.next/static ./.next/static
2025-Jul-17 14:59:05.797389
#18 DONE 0.2s
2025-Jul-17 14:59:05.996626
#19 [apexsaas-prod production 3/4] COPY --from=builder /app/public ./public
2025-Jul-17 14:59:05.996626
#19 DONE 0.0s
2025-Jul-17 14:59:05.996626
2025-Jul-17 14:59:05.996626
#20 [apexsaas-prod production 4/4] RUN mkdir -p .next/cache/images &&     chown -R node:node .next &&     chown -R node:node public
2025-Jul-17 14:59:07.502195
#20 DONE 1.6s
2025-Jul-17 14:59:07.669356
#21 [apexsaas-prod] exporting to image
2025-Jul-17 14:59:07.669356
#21 exporting layers
2025-Jul-17 14:59:08.622963
#21 exporting layers 1.1s done
2025-Jul-17 14:59:08.711054
#21 writing image sha256:aa99b3382aac4b33c18dfe93b65b1afefb64af7fec3eb9d513a9477df987b462 done
2025-Jul-17 14:59:08.711054
#21 naming to docker.io/library/e0008k448wcc8wgogkwkcwss-apexsaas-prod done
2025-Jul-17 14:59:08.711054
#21 DONE 1.1s
2025-Jul-17 14:59:08.711054
2025-Jul-17 14:59:08.711054
#22 [apexsaas-prod] resolving provenance for metadata file
2025-Jul-17 14:59:08.711054
#22 DONE 0.0s
2025-Jul-17 14:59:08.718259
apexsaas-prod  Built
2025-Jul-17 14:59:08.734342
Removing old containers.
2025-Jul-17 14:59:09.036932
[CMD]: docker stop --time=30 apexsaas-prod-e0008k448wcc8wgogkwkcwss-143139113547
2025-Jul-17 14:59:09.036932
Flag --time has been deprecated, use --timeout instead
2025-Jul-17 14:59:09.269393
apexsaas-prod-e0008k448wcc8wgogkwkcwss-143139113547
2025-Jul-17 14:59:09.538013
[CMD]: docker rm -f apexsaas-prod-e0008k448wcc8wgogkwkcwss-143139113547
2025-Jul-17 14:59:09.538013
apexsaas-prod-e0008k448wcc8wgogkwkcwss-143139113547
2025-Jul-17 14:59:09.544484
Starting new application.
2025-Jul-17 14:59:10.436721
[CMD]: docker exec qckw0o00kssco0s4gsogcw0s bash -c 'SOURCE_COMMIT=e22ee25bfdd59e92870a01214ad1a2619dc4da34 COOLIFY_BRANCH=feat/financeiro  docker compose --env-file /artifacts/qckw0o00kssco0s4gsogcw0s/.env --project-name e0008k448wcc8wgogkwkcwss --project-directory /artifacts/qckw0o00kssco0s4gsogcw0s -f /artifacts/qckw0o00kssco0s4gsogcw0s/docker-compose.yml up -d'
2025-Jul-17 14:59:10.436721
Container apexsaas-prod-e0008k448wcc8wgogkwkcwss-145257312068  Creating
2025-Jul-17 14:59:10.636968
Container apexsaas-prod-e0008k448wcc8wgogkwkcwss-145257312068  Created
2025-Jul-17 14:59:10.636968
Container apexsaas-prod-e0008k448wcc8wgogkwkcwss-145257312068  Starting
2025-Jul-17 14:59:10.998320
Container apexsaas-prod-e0008k448wcc8wgogkwkcwss-145257312068  Started
2025-Jul-17 14:59:11.761853
New container started.
2025-Jul-17 14:59:13.238188
Gracefully shutting down build container: qckw0o00kssco0s4gsogcw0s
2025-Jul-17 14:59:13.868262
[CMD]: docker stop --time=30 qckw0o00kssco0s4gsogcw0s
2025-Jul-17 14:59:13.868262
Flag --time has been deprecated, use --timeout instead
2025-Jul-17 14:59:14.348046
qckw0o00kssco0s4gsogcw0s
2025-Jul-17 14:59:14.640146
[CMD]: docker rm -f qckw0o00kssco0s4gsogcw0s
2025-Jul-17 14:59:14.640146
Error response from daemon: No such container: qckw0o00kssco0s4gsogcw0s