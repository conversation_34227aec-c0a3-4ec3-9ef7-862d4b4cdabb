-- Migration: Migrate enrollment_pauses table from enrollments to memberships
-- Purpose: Update enrollment_pauses table to reference memberships instead of enrollments
-- Affected tables: enrollment_pauses (modified)
-- Dependencies: 003_create_memberships_table.sql
-- Date: 2024-12-21

-- Step 1: Add new membership_id column temporarily
alter table public.enrollment_pauses 
add column membership_id uuid references public.memberships(id) on delete cascade;

-- Step 2: Create index for the new foreign key
create index idx_enrollment_pauses_membership_id on public.enrollment_pauses(membership_id);

-- Step 3: Add comment for the new column
comment on column public.enrollment_pauses.membership_id is 'References the membership this pause is associated with. Replaces enrollment_id.';

-- Step 4: For existing data migration (if enrollment table still exists)
-- This would be handled in a separate data migration script after both tables coexist
-- For now, we'll just ensure the column exists and is properly indexed

-- Step 5: Eventually, after data migration, we would:
-- 1. Make membership_id NOT NULL
-- 2. Drop enrollment_id column (or rename enrollment_pauses to membership_pauses)
-- 3. Update any views or functions that reference enrollment_id

-- Note: The actual data migration and cleanup will be done in subsequent scripts
-- after ensuring all existing enrollments have been migrated to memberships

-- Add constraint to ensure at least one reference exists (during transition period)
alter table public.enrollment_pauses 
add constraint enrollment_pauses_has_reference 
check (enrollment_id is not null or membership_id is not null);

-- Add partial index for pauses with membership_id
create index idx_enrollment_pauses_membership_not_null on public.enrollment_pauses(tenant_id, membership_id) 
where membership_id is not null;

-- Add partial index for active pauses with membership
create index idx_enrollment_pauses_active_membership on public.enrollment_pauses(membership_id, start_date, end_date) 
where membership_id is not null and end_date is null; 