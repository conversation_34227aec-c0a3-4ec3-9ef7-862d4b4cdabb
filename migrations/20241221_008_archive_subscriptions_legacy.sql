-- Migration: Archive subscriptions table as legacy
-- Purpose: Rename subscriptions table to subscriptions_legacy to preserve historical data
-- Affected tables: subscriptions (renamed to subscriptions_legacy)
-- Dependencies: All previous migration scripts in this series
-- Date: 2024-12-21

-- Step 1: Rename the subscriptions table to preserve historical data
alter table public.subscriptions rename to subscriptions_legacy;

-- Step 2: Update the table comment to reflect its legacy status
comment on table public.subscriptions_legacy is 'LEGACY: Former subscriptions table, replaced by memberships system. Kept for historical data and migration reference.';

-- Step 3: Disable RLS on legacy table (optional, for maintenance access)
alter table public.subscriptions_legacy disable row level security;

-- Step 4: Create a view for easy access to legacy data during transition period
create or replace view public.subscriptions_legacy_view as
select 
  id,
  tenant_id,
  student_id,
  plan_name,
  amount,
  frequency,
  status,
  start_date,
  end_date,
  created_at,
  updated_at,
  'LEGACY_SUBSCRIPTION' as source_type
from public.subscriptions_legacy;

-- Step 5: Add RLS to the view for tenant isolation
alter view public.subscriptions_legacy_view set (security_invoker = true);

-- Step 6: Create a comment for the view
comment on view public.subscriptions_legacy_view is 'Read-only view of legacy subscriptions data for migration and historical reference.';

-- Step 7: Add note about future cleanup
-- Note: This table should be dropped after a grace period (e.g., 6-12 months)
-- once all data has been successfully migrated and validated in the new system.
-- Before dropping, ensure:
-- 1. All subscription data has been migrated to memberships
-- 2. All related foreign keys have been updated
-- 3. Backup has been created
-- 4. Stakeholders have confirmed the migration is complete

-- Step 8: Create a function to help with data migration (if needed)
create or replace function public.get_legacy_subscription_count()
returns table (
  tenant_id uuid,
  total_subscriptions bigint,
  active_subscriptions bigint,
  inactive_subscriptions bigint
)
language sql
security invoker
set search_path = ''
as $$
  select 
    s.tenant_id,
    count(*) as total_subscriptions,
    count(*) filter (where s.status = 'active') as active_subscriptions,
    count(*) filter (where s.status != 'active') as inactive_subscriptions
  from public.subscriptions_legacy s
  group by s.tenant_id
  order by s.tenant_id;
$$;

-- Add comment for the helper function
comment on function public.get_legacy_subscription_count() is 'Helper function to get subscription counts by tenant for migration validation.'; 