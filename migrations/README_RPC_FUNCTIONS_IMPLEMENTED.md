# Funções RPC Implementadas - Sistema de Memberships e Planos

## 📋 Resumo

Este documento detalha todas as funções RPC (Remote Procedure Call) implementadas para o novo sistema de memberships e planos, seguindo o padrão de versionamento e auditoria definido na documentação de migração.

## 🚀 Funções Implementadas

### **Gerenciamento de Planos**

#### `create_plan()`
```sql
public.create_plan(
  p_title text,
  p_plan_type public.plan_type default 'individual',
  p_pricing_config jsonb default '{}',
  p_duration_config jsonb default '{}',
  p_access_config jsonb default '{}',
  p_metadata jsonb default '{}'
)
```
- **Propósito**: Cria um novo plano em status `draft`
- **Segurança**: `SECURITY DEFINER` com isolamento por tenant
- **Retorno**: JSON com `success`, `plan_id` e `message`

#### `publish_plan(p_plan_id uuid)`
- **Propósito**: Ativa uma versão do plano e arquiva versões anteriores
- **Comportamento**: Automaticamente define status `active` e arquiva outras versões do mesmo `base_plan_id`
- **Retorno**: JSON com confirmação de sucesso

#### `duplicate_plan(p_plan_id uuid)`
- **Propósito**: Cria nova versão de um plano existente
- **Comportamento**: Incrementa automaticamente o número da versão
- **Retorno**: JSON com `plan_id`, `version` da nova versão

#### `get_active_plans()`
- **Propósito**: Lista todos os planos ativos do tenant
- **Retorno**: Array JSON de planos com configurações completas

#### `get_plan_latest_version(p_base_plan_id uuid)`
- **Propósito**: Retorna a versão mais recente de um plano base
- **Retorno**: JSON com dados completos da versão mais recente

### **Gerenciamento de Matrículas**

#### `create_membership()`
```sql
public.create_membership(
  p_student_id uuid,
  p_plan_id uuid,
  p_start_date date default current_date,
  p_metadata jsonb default '{}'
)
```
- **Propósito**: Cria matrícula com cálculo automático de datas de cobrança
- **Validações**: 
  - Verifica política de múltiplas matrículas do tenant
  - Valida se estudante pertence ao tenant
  - Confirma se plano está ativo
- **Retorno**: JSON com `membership_id`, `end_date`, `next_billing_date`

#### `update_membership_status()`
```sql
public.update_membership_status(
  p_membership_id uuid,
  p_new_status public.membership_status,
  p_reason text default null
)
```
- **Propósito**: Atualiza status da matrícula com log automático
- **Auditoria**: Trigger gera automaticamente registro em `membership_status_logs`
- **Retorno**: JSON com `old_status`, `new_status`

#### `pause_membership()`
```sql
public.pause_membership(
  p_membership_id uuid,
  p_reason text default null,
  p_metadata jsonb default '{}'
)
```
- **Propósito**: Pausa matrícula e cria registro de pausa
- **Comportamento**: Atualiza status para `paused` e insere em `enrollment_pauses`
- **Retorno**: JSON com `pause_id`

#### `get_student_active_memberships(p_student_id uuid)`
- **Propósito**: Lista matrículas ativas de um estudante
- **Retorno**: Array JSON com matrículas e dados completos dos planos

### **Cálculos e Cobrança**

#### `calculate_price(p_plan_id uuid, p_calculation_date date)`
- **Propósito**: Calcula preço baseado na configuração do plano
- **Inclui**: Valor base, taxas, descontos
- **Retorno**: JSON com breakdown completo dos valores

#### `process_membership_billing(p_membership_id uuid)`
- **Propósito**: Processa cobrança recorrente
- **Comportamento**: 
  - Cria registro de pagamento
  - Atualiza próxima data de cobrança
  - Valida se matrícula permite cobrança recorrente
- **Retorno**: JSON com `payment_id`, `amount`, `next_billing_date`

### **Configurações do Tenant**

#### `set_tenant_setting(p_setting_key text, p_setting_value jsonb)`
- **Propósito**: Atualiza configuração específica do tenant
- **Uso**: Configurar `allow_multiple_memberships` e outras políticas
- **Retorno**: JSON com confirmação

#### `get_tenant_settings()`
- **Propósito**: Retorna todas as configurações do tenant
- **Retorno**: JSON com configurações completas

### **Dashboard e Relatórios**

#### `membership_overview(p_start_date date, p_end_date date)`
- **Propósito**: Dados agregados para dashboard
- **Inclui**: 
  - Estatísticas de matrículas por status
  - Receita do período
  - Novas matrículas
- **Retorno**: JSON estruturado para métricas

#### `get_membership_statistics(p_start_date date, p_end_date date)`
- **Propósito**: Estatísticas diárias de matrículas
- **Retorno**: Array JSON com dados por dia

#### `get_plan_usage_analytics()`
- **Propósito**: Analytics de uso de planos
- **Inclui**: Popularidade, receita, tempo médio ativo
- **Retorno**: Array JSON com métricas por plano

### **Validação e Utilitários**

#### `validate_plan_config()`
```sql
public.validate_plan_config(
  p_pricing_config jsonb,
  p_duration_config jsonb,
  p_access_config jsonb
)
```
- **Propósito**: Valida configurações antes de salvar plano
- **Validações**:
  - Campos obrigatórios por tipo de plano
  - Consistência entre configurações
  - Formato de dados
- **Retorno**: JSON com `valid` boolean e array de `errors`

## 🔒 Segurança e Isolamento

### Políticas RLS Implementadas

Todas as tabelas possuem políticas RLS granulares:

**Plans Table:**
- `plans_select_policy`: Leitura isolada por tenant
- `plans_insert_policy`: Inserção autorizada por tenant
- `plans_update_policy`: Atualização isolada por tenant
- `plans_delete_policy`: Exclusão autorizada por tenant

**Memberships Table:**
- `memberships_select_policy`: Leitura isolada por tenant
- `memberships_insert_policy`: Criação autorizada por tenant
- `memberships_update_policy`: Atualização isolada por tenant
- `memberships_delete_policy`: Exclusão autorizada por tenant

**Membership Status Logs Table:**
- Políticas completas para auditoria isolada por tenant
- Permissões especiais para triggers automáticos

### Triggers de Segurança

1. **`check_single_membership_trigger`**: Impede múltiplas matrículas ativas quando desabilitado nas configurações do tenant
2. **`membership_status_change_trigger`**: Log automático de mudanças de status
3. **`membership_update_timestamp_trigger`**: Atualização automática de timestamps

## 📊 Índices de Performance

### Índices Estratégicos Criados

```sql
-- Consultas rápidas por tenant e status
idx_plans_tenant_status
idx_memberships_tenant_status

-- Versionamento de planos
idx_plans_base_plan_version

-- Consultas de estudante
idx_memberships_student_tenant

-- Analytics de planos
idx_memberships_plan_id

-- Cobrança automática
idx_memberships_next_billing_date

-- Auditoria
idx_membership_status_logs_membership_date
idx_membership_status_logs_tenant

-- Consultas JSONB
idx_plans_pricing_config_gin
idx_plans_duration_config_gin
idx_plans_access_config_gin
```

## 🔄 Fluxo de Versionamento

### Como Funciona o Versionamento de Planos

1. **Criação**: `create_plan()` → Status `draft`, versão 1
2. **Edição**: `duplicate_plan()` → Nova versão em `draft`
3. **Publicação**: `publish_plan()` → Ativa versão, arquiva anteriores
4. **Matrículas**: Sempre referenciam versão específica (imutável)

### Benefícios do Versionamento

- **Histórico Preservado**: Cada matrícula mantém snapshot do plano na data de assinatura
- **Auditoria Completa**: Rastrea todas as mudanças com timestamps e usuários
- **Rollback Simples**: Reativar versão anterior sem perder dados
- **A/B Testing**: Múltiplas versões ativas simultaneamente (se necessário)

## 🧪 Testes e Validação

### Funções Testadas

✅ **validate_plan_config()**: Validação de configurações válidas e inválidas  
✅ **Políticas RLS**: Isolamento correto por tenant  
✅ **Triggers**: Funcionamento de auditoria e validação  
✅ **Índices**: Criação e otimização de queries  

### Próximos Passos para Testes

1. **Testes de Integração**: Testar fluxo completo com JWT válido
2. **Testes de Performance**: Validar queries com grandes volumes
3. **Testes de Segurança**: Verificar isolamento entre tenants
4. **Testes de Concorrência**: Validar locks e transações

## 📈 Monitoramento e Observabilidade

### Logs de Auditoria

- **membership_status_logs**: Todas as mudanças de status com usuário e motivo
- **plans**: Versionamento automático com criador e timestamps
- **enrollment_pauses**: Histórico completo de pausas e reativações

### Métricas Disponíveis

- Taxa de conversão por plano
- Tempo médio de permanência
- Receita recorrente mensal (MRR)
- Churn rate por período
- Estatísticas de crescimento

## 🛠️ Como Usar no Frontend

### Exemplo de Integração

```typescript
// Criar um novo plano
const result = await supabase.rpc('create_plan', {
  p_title: 'Plano Premium',
  p_plan_type: 'individual',
  p_pricing_config: {
    type: 'recurring',
    amount: 200,
    frequency: 'monthly',
    currency: 'BRL'
  },
  p_duration_config: {
    type: 'ongoing',
    auto_renew: true
  },
  p_access_config: {
    modalities: ['jiu-jitsu', 'muay-thai'],
    frequency: 'unlimited'
  }
});

// Criar matrícula
const membership = await supabase.rpc('create_membership', {
  p_student_id: 'uuid-do-estudante',
  p_plan_id: result.data.plan_id
});

// Obter relatórios
const dashboard = await supabase.rpc('membership_overview', {
  p_start_date: '2024-01-01',
  p_end_date: '2024-12-31'
});
```

## 🎯 Benefícios Implementados

1. **Escalabilidade**: Estrutura preparada para milhares de planos e matrículas
2. **Flexibilidade**: Configurações JSONB permitem evolução sem mudanças de schema
3. **Auditoria**: Compliance total com LGPD e requisitos de auditoria
4. **Performance**: Índices otimizados para consultas frequentes
5. **Segurança**: Isolamento multi-tenant robusto com RLS
6. **Manutenibilidade**: Código bem documentado e estruturado

---