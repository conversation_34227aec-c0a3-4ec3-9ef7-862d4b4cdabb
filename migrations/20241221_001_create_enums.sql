-- Migration: Create enums for plans and memberships system
-- Purpose: Define enumeration types for plan and membership status management
-- Affected tables: plans, memberships (will be created in subsequent migrations)
-- Date: 2024-12-21

-- Enable necessary extensions if not already enabled
create extension if not exists "uuid-ossp";

-- Create enum for plan status
-- Represents the lifecycle state of a plan
create type public.plan_status as enum (
  'draft',     -- Plan is being configured but not yet available
  'active',    -- Plan is published and available for new memberships
  'archived'   -- Plan is no longer available but existing memberships continue
);

-- Create enum for plan type  
-- Defines the target audience and structure of the plan
create type public.plan_type as enum (
  'individual',  -- Standard individual membership
  'family',      -- Family plan (future implementation)
  'corporate'    -- Corporate/group plan (future implementation)
);

-- Create enum for membership status
-- Tracks the current state of a student's membership
create type public.membership_status as enum (
  'active',    -- Membership is current and in good standing
  'paused',    -- Membership is temporarily suspended but will resume
  'canceled',  -- Membership has been terminated by user or system
  'expired'    -- Membership has reached its end date naturally
);

-- Add comments for documentation
comment on type public.plan_status is 'Status lifecycle for subscription plans - draft (editing), active (available), archived (discontinued)';
comment on type public.plan_type is 'Type classification for plans - individual (single person), family (household), corporate (business)';
comment on type public.membership_status is 'Current state of a student membership - active (current), paused (suspended), canceled (terminated), expired (ended)'; 