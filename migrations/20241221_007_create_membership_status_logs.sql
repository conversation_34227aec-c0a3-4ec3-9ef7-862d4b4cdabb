-- Migration: Create membership status audit logs
-- Purpose: Track all status changes in memberships for audit and compliance
-- Affected tables: membership_status_logs (new)
-- Dependencies: 003_create_memberships_table.sql
-- Date: 2024-12-21

-- Create table for membership status change audit logs
create table public.membership_status_logs (
  id uuid primary key default gen_random_uuid(),
  tenant_id uuid not null references public.tenants(id) on delete cascade,
  membership_id uuid not null references public.memberships(id) on delete cascade,
  
  -- Status change tracking
  old_status public.membership_status,
  new_status public.membership_status not null,
  
  -- Change metadata
  changed_at timestamptz not null default now(),
  changed_by uuid references auth.users(id),
  change_reason text,
  
  -- Additional context
  metadata jsonb not null default '{}'
);

-- Enable RLS
alter table public.membership_status_logs enable row level security;

-- Create policies for tenant isolation
create policy "tenant_isolation_membership_status_logs" on public.membership_status_logs
  using (tenant_id = (auth.jwt() ->> 'tenant_id')::uuid);

create policy "users_can_select_status_logs" on public.membership_status_logs
  for select using (
    tenant_id = (auth.jwt() ->> 'tenant_id')::uuid
  );

create policy "system_can_insert_status_logs" on public.membership_status_logs
  for insert with check (
    tenant_id = (auth.jwt() ->> 'tenant_id')::uuid
  );

-- Create indexes for performance
create index idx_membership_status_logs_tenant_id on public.membership_status_logs(tenant_id);
create index idx_membership_status_logs_membership_id on public.membership_status_logs(membership_id);
create index idx_membership_status_logs_changed_at on public.membership_status_logs(changed_at desc);
create index idx_membership_status_logs_membership_changed_at on public.membership_status_logs(membership_id, changed_at desc);

-- Create function to log membership status changes
create or replace function public.log_membership_status_change()
returns trigger
language plpgsql
security invoker
set search_path = ''
as $$
begin
  -- Only log when status actually changes
  if tg_op = 'UPDATE' and old.status != new.status then
    insert into public.membership_status_logs (
      tenant_id,
      membership_id,
      old_status,
      new_status,
      changed_at,
      changed_by,
      change_reason
    ) values (
      new.tenant_id,
      new.id,
      old.status,
      new.status,
      now(),
      (auth.jwt() ->> 'sub')::uuid,
      coalesce(new.metadata ->> 'status_change_reason', 'Status changed')
    );
  end if;
  
  -- Log initial status on INSERT
  if tg_op = 'INSERT' then
    insert into public.membership_status_logs (
      tenant_id,
      membership_id,
      old_status,
      new_status,
      changed_at,
      changed_by,
      change_reason
    ) values (
      new.tenant_id,
      new.id,
      null,
      new.status,
      now(),
      (auth.jwt() ->> 'sub')::uuid,
      'Membership created'
    );
  end if;
  
  return new;
end;
$$;

-- Create trigger to automatically log status changes
create trigger log_membership_status_change_trigger
  after insert or update on public.memberships
  for each row
  execute function public.log_membership_status_change();

-- Add table comment
comment on table public.membership_status_logs is 'Audit log for all membership status changes, providing compliance and tracking capabilities.';

-- Add column comments
comment on column public.membership_status_logs.old_status is 'Previous status before the change. NULL for initial creation.';
comment on column public.membership_status_logs.new_status is 'New status after the change.';
comment on column public.membership_status_logs.changed_by is 'User who initiated the status change.';
comment on column public.membership_status_logs.change_reason is 'Human-readable reason for the status change.';

-- Add function comment
comment on function public.log_membership_status_change() is 'Automatically logs membership status changes for audit purposes.'; 