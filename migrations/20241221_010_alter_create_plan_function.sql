-- Migration: Alter create_plan function to include tenant_id and created_by
-- Purpose: Modify the existing RPC function to correctly handle multi-tenancy and audit information.
-- Affected functions: public.create_plan
-- Dependencies: 002_create_plans_table.sql
-- Date: 2024-12-22

-- Drop the old function if it exists to avoid conflicts
drop function if exists public.create_plan(text, public.plan_type, jsonb, jsonb, jsonb, text[], jsonb);

-- Create the new version of the function
create function public.create_plan(
  p_tenant_id uuid,
  p_title text,
  p_plan_type public.plan_type default 'individual',
  p_pricing_config jsonb default '{}',
  p_duration_config jsonb default '{}',
  p_access_config jsonb default '{}',
  p_benefits text[] default '{}',
  p_metadata jsonb default '{}'
)
returns json
language plpgsql
security definer
set search_path = public
as $$
declare
  new_plan_id uuid;
  user_id uuid := auth.uid();
begin
  -- Validate that the user belongs to the tenant they are trying to insert into
  if not exists (
    select 1
    from public.users
    where id = user_id and tenant_id = p_tenant_id
  ) then
    raise exception 'User does not have permission for this tenant';
  end if;

  insert into public.plans(
    tenant_id,
    title,
    plan_type,
    pricing_config,
    duration_config,
    access_config,
    benefits,
    metadata,
    created_by
  )
  values (
    p_tenant_id,
    p_title,
    p_plan_type,
    p_pricing_config,
    p_duration_config,
    p_access_config,
    p_benefits,
    p_metadata,
    user_id
  ) returning id into new_plan_id;

  return json_build_object(
    'success', true,
    'plan_id', new_plan_id,
    'message', 'Plan created successfully'
  );
exception
  when others then
    return json_build_object(
      'success', false,
      'message', 'Failed to create plan: ' || SQLERRM
    );
end;
$$;

comment on function public.create_plan(uuid, text, public.plan_type, jsonb, jsonb, jsonb, text[], jsonb) is 'Creates a new plan for a specific tenant in "draft" status. Requires tenant_id for authorization.'; 