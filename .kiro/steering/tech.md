# Technology Stack

## Framework & Runtime
- **Next.js 15** with App Router and React Server Components
- **React 19** with TypeScript for type safety
- **Node.js** runtime environment

## Backend & Database
- **Supabase** as Backend-as-a-Service
  - PostgreSQL database with Row Level Security (RLS)
  - Authentication and user management
  - Real-time subscriptions
  - File storage
- **Custom tenant isolation** using `tenant_auth` schema functions

## UI & Styling
- **Tailwind CSS** for utility-first styling
- **Shadcn/ui** components built on Radix UI primitives
- **Framer Motion** for animations
- **Lucide React** for icons
- **Lottie** animations for interactive elements

## State Management & Data Fetching
- **TanStack Query (React Query)** for server state management
- **Zustand** for client state management
- **React Hook Form** with Zod validation for forms

## Development Tools
- **TypeScript** with strict configuration
- **Prettier** for code formatting
- **ESLint** for code linting (implied)

## Common Commands

```bash
# Development
npm run dev                    # Start development server
npm run build                  # Build for production
npm run start                  # Start production server

# Subdomain setup (development)
npm run setup-subdomains       # Configure local subdomains for multi-tenancy

# Database migrations
# Run SQL files in /migrations directory manually via Supabase dashboard
```

## Environment Configuration
- `.env` for local environment variables
- `.env.example` as template
- Docker support with `Dockerfile` and `docker-compose.yml`

## Multi-tenancy Implementation
- Subdomain-based tenant detection via Next.js middleware
- RLS policies with `tenant_auth.tenant_id()` functions
- Tenant isolation at database level with optimized indexes