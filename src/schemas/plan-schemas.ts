import { z } from 'zod'

/*
 * Schemas e tipos relacionados à criação e configuração de Planos
 * Versão canônica em português – utilizada tanto pelo front-end (React) quanto
 * pelas Server Actions.
 */

/* --------------------------------------------------
 * Enums base
 * -------------------------------------------------- */
export const planoStatusSchema = z.enum(['draft', 'active', 'archived'])
export const tipoPlanoSchema = z.enum(['individual', 'family', 'corporate'])
export const tipoPrecificacaoSchema = z.enum(['recurring', 'one-time', 'per-session', 'trial'])
export const unidadeFrequenciaSchema = z.enum(['day', 'week', 'month', 'year'])
export const unidadeTempoSchema = z.enum(['days', 'weeks', 'months', 'years'])
export const frequenciaAcessoSchema = z.enum([
  'sessions',
  'days',
  'payment',
  'week',
  'month',
  'year',
  'unlimited',
])

/* --------------------------------------------------
 * Pricing Config Schemas
 * -------------------------------------------------- */
// Recorrente
const configRecorrenteSchema = z.object({
  tipo: z.literal('recurring'),
  valor: z.number().min(0, 'Valor deve ser maior ou igual a zero'),
  moeda: z.string().default('BRL'),
  frequencia: unidadeFrequenciaSchema,
  numeroFrequencia: z.number().min(1, 'Frequência deve ser maior que zero'),
  maxPagamentos: z.number().nullable().optional(),
  taxaInscricao: z.number().min(0).optional(),
  taxaAtraso: z.number().min(0).optional(),
  diasAtraso: z.number().min(1).optional(),
})

// Pagamento único
const configUnicoSchema = z.object({
  tipo: z.literal('one-time'),
  custo: z.number().min(0, 'Valor deve ser maior ou igual a zero').default(0),
  moeda: z.string().default('BRL'),
  renovacaoAutomatica: z.boolean().optional(),
  descontos: z
    .object({
      segundoMembro: z.number().optional(),
      terceiroMembro: z.number().optional(),
      quartoMembro: z.number().optional(),
    })
    .optional(),
  taxaInscricao: z.number().min(0).optional(),
  taxaAtraso: z.number().min(0).optional(),
  diasAtraso: z.number().min(1).optional(),
})

// Por aula
const configPorAulaSchema = z.object({
  tipo: z.literal('per-session'),
  custo: z.number().min(0, 'Valor deve ser maior ou igual a zero').default(0),
  moeda: z.string().default('BRL'),
  taxaInscricao: z.number().min(0).optional(),
  taxaAtraso: z.number().min(0).optional(),
  diasAtraso: z.number().min(1).optional(),
})

// Trial
const configTrialSchema = z.object({
  tipo: z.literal('trial'),
  duracao: z.object({
    valor: z.coerce
      .number({ invalid_type_error: 'A duração deve ser um número.' })
      .min(1, 'Duração do trial deve ser maior que zero'),
    unidade: z.enum(['days', 'weeks', 'months']),
  }),
  valorDuranteTrial: z.number().min(0).nullable().optional(),
  valorAposTrial: z.number().min(0, 'Valor após trial deve ser maior ou igual a zero'),
  moeda: z.string().default('BRL'),
  frequenciaAposTrial: unidadeFrequenciaSchema,
  taxaInscricao: z.number().min(0).optional(),
})

export {
  configRecorrenteSchema,
  configUnicoSchema,
  configPorAulaSchema,
  configTrialSchema,
}

export const configuracaoPrecoSchema = z.discriminatedUnion('tipo', [
  configRecorrenteSchema,
  configUnicoSchema,
  configPorAulaSchema,
  configTrialSchema,
])

/* --------------------------------------------------
 * Duration Config Schemas
 * -------------------------------------------------- */
const duracaoContinuaSchema = z.object({
  tipo: z.literal('ongoing'),
  renovacaoAutomatica: z.boolean().optional(),
})

const duracaoLimitadaSchema = z.object({
  tipo: z.literal('limited'),
  duracao: z.number().min(1, 'Duração deve ser maior que zero'),
  unidadeTempo: unidadeTempoSchema,
  adicionarDuracaoRenovacao: z.boolean().optional(),
  cobrarTaxaRenovacao: z.boolean().optional(),
  opcaoRenovacao: z.enum(['auto-renew', 'manual']),
  taxaCancelamento: z.number().min(0).optional(),
})

const isValidDateString = (value: string) => {
  if (!value) return false
  if (/^\d{4}-\d{2}-\d{2}$/.test(value)) {
    const [year, month, day] = value.split('-').map(Number)
    const date = new Date(year, month - 1, day)
    return !isNaN(date.getTime())
  }
  return !isNaN(Date.parse(value))
}

const duracaoEspecificaSchema = z.object({
  tipo: z.literal('specific'),
  dataInicio: z.string().refine(isValidDateString, 'Data de início inválida'),
  dataFim: z.string().refine(isValidDateString, 'Data de fim inválida'),
  periodosAdicionais: z
    .array(
      z.object({
        inicio: z.string().refine(isValidDateString, 'Data de início inválida'),
        fim: z.string().refine(isValidDateString, 'Data de fim inválida'),
      }),
    )
    .optional(),
  mostrarPeriodosAtivos: z.boolean().optional(),
  agendarPrimeiroPagamento: z.boolean().optional(),
  taxaCancelamento: z.number().min(0).optional(),
})

export const configuracaoDuracaoSchema = z.discriminatedUnion('tipo', [
  duracaoContinuaSchema,
  duracaoLimitadaSchema,
  duracaoEspecificaSchema,
])

/* --------------------------------------------------
 * Access Config Schema (Acesso à academia)
 * -------------------------------------------------- */
export const configuracaoAcessoSchema = z.object({
  frequencia: frequenciaAcessoSchema,
  quantidade: z.number().min(1).optional(),
  capacidade: z.enum(['limited', 'unlimited']),
  capacidadeMaxima: z.number().min(1).optional(),
  modalidades: z.array(z.string()).min(1, 'Selecione pelo menos uma modalidade'),
  todasSessoes: z.boolean().optional(),
})

// Schema para formulário (com validação de modalidades)
export const configuracaoAcessoFormSchema = z.object({
  frequencia: frequenciaAcessoSchema,
  quantidade: z.number().min(1).optional(),
  capacidade: z.enum(['limited', 'unlimited']),
  capacidadeMaxima: z.number().min(1).optional(),
  modalidades: z.array(z.string()).min(1, 'Selecione pelo menos uma modalidade'),
  todasSessoes: z.boolean().optional(),
}).refine(data => {
  // Validação: se capacidade ilimitada estiver marcada, capacidadeMaxima deve ser null/undefined
  if (data.capacidade === 'unlimited') {
    return data.capacidadeMaxima === null || data.capacidadeMaxima === undefined;
  }
  return true;
}, {
  message: 'Capacidade máxima deve ser vazia quando capacidade ilimitada está selecionada',
  path: ['capacidadeMaxima']
}).refine(data => {
  // Validação: se capacidade é limitada, capacidadeMaxima deve ser fornecida e válida
  if (data.capacidade === 'limited') {
    return data.capacidadeMaxima !== null &&
           data.capacidadeMaxima !== undefined &&
           typeof data.capacidadeMaxima === 'number' &&
           data.capacidadeMaxima >= 1;
  }
  return true;
}, {
  message: 'Capacidade máxima é obrigatória quando capacidade limitada está selecionada',
  path: ['capacidadeMaxima']
})

/* --------------------------------------------------
 * Benefits Schema
 * -------------------------------------------------- */
export const beneficiosPlanoSchema = z
  .array(z.string().min(2, 'Benefício deve ter pelo menos 1 caracteres'))
  .min(1, 'Adicione pelo menos um benefício')

export type BeneficiosPlano = z.infer<typeof beneficiosPlanoSchema>

/* --------------------------------------------------
 * Schemas principais
 * -------------------------------------------------- */
export const criarPlanoSchema = z.object({
  details: z.object({
    titulo: z.string().min(2, 'Título deve ter pelo menos 2 caracteres'),
    tipo: tipoPlanoSchema.default('individual'),
  }),
  pricing: configuracaoPrecoSchema,
  duration: configuracaoDuracaoSchema,
  academyAccess: configuracaoAcessoSchema,
  benefits: beneficiosPlanoSchema,
  metadata: z.record(z.unknown()).optional().default({}),
})

export const publicarPlanoSchema = z.object({
  planoId: z.string().uuid('ID do plano inválido'),
})

export const duplicarPlanoSchema = z.object({
  planoId: z.string().uuid('ID do plano inválido'),
})

export const validarConfiguracaoPlanoSchema = z.object({
  pricing: configuracaoPrecoSchema,
  duration: configuracaoDuracaoSchema,
  academyAccess: configuracaoAcessoSchema,
})

/* --------------------------------------------------
 * Tipos utilitários
 * -------------------------------------------------- */
export type CriarPlanoData = z.infer<typeof criarPlanoSchema>
export type PublicarPlanoData = z.infer<typeof publicarPlanoSchema>
export type DuplicarPlanoData = z.infer<typeof duplicarPlanoSchema>
export type ValidarConfiguracaoPlanoData = z.infer<typeof validarConfiguracaoPlanoSchema> 

// detalhes schema para formulário de detalhes
export const detalhesPlanoSchema = criarPlanoSchema.shape.details; 