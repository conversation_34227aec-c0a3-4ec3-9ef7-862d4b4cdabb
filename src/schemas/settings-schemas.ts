import { z } from 'zod'

// Schema genérico para definir qualquer setting do tenant
export const definirSettingTenantSchema = z.object({
  chaveSetting: z.string().min(1, 'Chave da configuração é obrigatória'),
  valorSetting: z.unknown(),
})

// Schema específico para permitir múltiplas matrículas
export const permitirMultiplasMembershipsSchema = z.object({
  permitirMultiplasMemberships: z.boolean(),
})

/* Tipos */
export type DefinirSettingTenantData = z.infer<typeof definirSettingTenantSchema>
export type PermitirMultiplasMembershipsData = z.infer<typeof permitirMultiplasMembershipsSchema> 