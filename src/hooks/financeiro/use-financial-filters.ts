'use client';

import { useState, useCallback } from 'react';
import { FinancialFilterState, TransactionType } from '@/app/(dashboard)/financeiro/pagamentos/components/types';

// Estado padrão dos filtros para receitas
const defaultIncomeFilterState: FinancialFilterState = {
  search: '',
  status: ['paid'], // Status "Pago" ativo por padrão para receitas
  paymentMethod: [],
  paymentType: [],
  categories: [],
  page: 1,
  limit: 20
};

// Estado padrão dos filtros para despesas
const defaultExpenseFilterState: FinancialFilterState = {
  search: '',
  status: ['paid'], // Status "Pago" ativo por padrão para despesas
  paymentMethod: [],
  paymentType: [],
  categories: [],
  page: 1,
  limit: 20
};

export function useFinancialFilters(transactionType: TransactionType = 'income') {
  const defaultState = transactionType === 'income' ? defaultIncomeFilterState : defaultExpenseFilterState;
  const [filters, setFilters] = useState<FinancialFilterState>(defaultState);

  const updateFilters = useCallback((newFilters: FinancialFilterState) => {
    setFilters(prev => ({
      ...prev,
      ...newFilters,
      // Reset page when filters change (except when only page is changing)
      page: newFilters.page !== undefined ? newFilters.page : 1
    }));
  }, []);

  const clearFilters = useCallback(() => {
    setFilters(defaultState);
  }, [defaultState]);

  const resetPage = useCallback(() => {
    setFilters(prev => ({ ...prev, page: 1 }));
  }, []);

  // Função para alternar entre tipos de transação
  const switchTransactionType = useCallback((newType: TransactionType) => {
    const newDefaultState = newType === 'income' ? defaultIncomeFilterState : defaultExpenseFilterState;
    setFilters(newDefaultState);
  }, []);

  return {
    filters,
    updateFilters,
    clearFilters,
    resetPage,
    switchTransactionType
  };
}
