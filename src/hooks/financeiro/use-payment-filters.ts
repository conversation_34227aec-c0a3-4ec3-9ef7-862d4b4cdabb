'use client';

import { useState, useCallback } from 'react';
import { PaymentFilterState } from '@/app/(dashboard)/financeiro/pagamentos/components/types';

// Estado padrão dos filtros
const defaultFilterState: PaymentFilterState = {
  search: '',
  status: ['paid'], // Status "Pago" ativo por padrão
  paymentMethod: [],
  paymentType: [],
  page: 1,
  limit: 20
};

export function usePaymentFilters() {
  const [filters, setFilters] = useState<PaymentFilterState>(defaultFilterState);

  const updateFilters = useCallback((newFilters: PaymentFilterState) => {
    setFilters(prev => ({
      ...prev,
      ...newFilters,
      // Reset page when filters change (except when only page is changing)
      page: newFilters.page !== undefined ? newFilters.page : 1
    }));
  }, []);

  const clearFilters = useCallback(() => {
    setFilters(defaultFilterState);
  }, []);

  const resetPage = useCallback(() => {
    setFilters(prev => ({ ...prev, page: 1 }));
  }, []);

  return {
    filters,
    updateFilters,
    clearFilters,
    resetPage
  };
}
