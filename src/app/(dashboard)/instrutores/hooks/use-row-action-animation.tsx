'use client';

import { useState, useCallback } from 'react';

export type ActionType = 
  | 'delete' 
  | 'delete-permanently' 
  | 'inactivate' 
  | 'activate' 
  | 'generic';

interface UserAnimationState {
  id: string;
  isAnimating: boolean;
  action: ActionType;
  isRemoving: boolean;
}

export function useRowActionAnimation() {
  const [animatingUsers, setAnimatingUsers] = useState<UserAnimationState[]>([]);

  const isUserAnimating = useCallback((userId: string): boolean => {
    return animatingUsers.some(user => user.id === userId && user.isAnimating);
  }, [animatingUsers]);

  const isUserRemoving = useCallback((userId: string): boolean => {
    return animatingUsers.some(user => user.id === userId && user.isRemoving);
  }, [animatingUsers]);

  const getUserAction = useCallback((userId: string): ActionType | null => {
    const user = animatingUsers.find(user => user.id === userId);
    return user?.action || null;
  }, [animatingUsers]);

  const executeActionWithAnimation = useCallback(async (
    userId: string,
    action: ActionType,
    actionFunction: () => Promise<any>
  ) => {
    // Iniciar animação
    setAnimatingUsers(prev => {
      const existing = prev.find(u => u.id === userId);
      if (existing) {
        return prev.map(u => 
          u.id === userId 
            ? { ...u, isAnimating: true, action, isRemoving: false }
            : u
        );
      }
      return [...prev, { id: userId, isAnimating: true, action, isRemoving: false }];
    });

    try {
      // Executar a ação
      const result = await actionFunction();
      
      // Se for uma ação de remoção bem-sucedida, marcar para remoção
      if (result.success && (action === 'delete' || action === 'delete-permanently')) {
        setAnimatingUsers(prev => 
          prev.map(u => 
            u.id === userId 
              ? { ...u, isAnimating: false, isRemoving: true }
              : u
          )
        );
      } else {
        // Parar animação
        setAnimatingUsers(prev => prev.filter(u => u.id !== userId));
      }
      
      return result;
    } catch (error) {
      // Em caso de erro, parar animação
      setAnimatingUsers(prev => prev.filter(u => u.id !== userId));
      throw error;
    }
  }, []);

  const removeFromList = useCallback((userId: string) => {
    setAnimatingUsers(prev => prev.filter(u => u.id !== userId));
  }, []);

  const clearAnimations = useCallback(() => {
    setAnimatingUsers([]);
  }, []);

  return {
    isUserAnimating,
    isUserRemoving,
    getUserAction,
    executeActionWithAnimation,
    removeFromList,
    clearAnimations
  };
} 