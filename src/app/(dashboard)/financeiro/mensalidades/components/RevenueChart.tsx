'use client';

import { useEffect, useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { getMonthlyPaymentStatus } from '../actions/payment-actions';
import { MonthlyPaymentStatus } from '../types';
import { formatCurrency } from '@/utils/format-utils';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Legend } from 'recharts';

interface RevenueChartProps {
  year?: number;
}

interface ChartData {
  month: string;
  paid: number;
  overdue: number;
  paidAmount: number;
  overdueAmount: number;
  displayMonth: string;
}

export function RevenueChart({ year = new Date().getFullYear() }: RevenueChartProps) {
  const [data, setData] = useState<ChartData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      setError(null);

      const result = await getMonthlyPaymentStatus(year);

      if (result.success && result.data) {
        // Preencher todos os meses do ano, mesmo se não houver dados
        const allMonths = Array.from({ length: 12 }, (_, index) => {
          const month = String(index + 1).padStart(2, '0');
          const monthKey = `${year}-${month}`;
          const existingData = result.data?.find((item: MonthlyPaymentStatus) => item.month === monthKey);

          return {
            month: monthKey,
            paid: existingData?.paid || 0,
            overdue: existingData?.overdue || 0,
            paidAmount: existingData?.paidAmount || 0,
            overdueAmount: existingData?.overdueAmount || 0,
            displayMonth: new Date(year, index).toLocaleDateString('pt-BR', { month: 'short' })
          };
        });

        setData(allMonths);
      } else {
        setError(result.error || 'Erro ao carregar dados de pagamentos');
      }

      setLoading(false);
    };

    fetchData();
  }, [year]);

  const CustomTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-white/95 dark:bg-gray-900/95 backdrop-blur-sm p-4 border-0 rounded-xl shadow-2xl ring-1 ring-black/5 dark:ring-white/10">
          <p className="font-semibold text-gray-900 dark:text-gray-100 mb-3 text-sm">
            {data.displayMonth} {year}
          </p>
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 rounded-full bg-gradient-to-r from-emerald-500 to-green-500"></div>
              <p className="text-sm text-gray-700 dark:text-gray-300">
                <span className="font-medium">Pagos:</span> {data.paid} <span className="text-emerald-600 dark:text-emerald-400 font-semibold">({formatCurrency(data.paidAmount)})</span>
              </p>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 rounded-full bg-gradient-to-r from-red-500 to-rose-500"></div>
              <p className="text-sm text-gray-700 dark:text-gray-300">
                <span className="font-medium">Vencidos:</span> {data.overdue} <span className="text-red-600 dark:text-red-400 font-semibold">({formatCurrency(data.overdueAmount)})</span>
              </p>
            </div>
          </div>
        </div>
      );
    }
    return null;
  };

  if (loading) {
    return (
      <Card className="border-0 shadow-lg bg-gradient-to-br from-white to-gray-50/50 dark:from-gray-900 dark:to-gray-800/50">
        <CardHeader className="pb-4">
          <CardTitle className="text-gray-800 dark:text-gray-200 font-semibold">Pagamentos Mensais</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-80 bg-gradient-to-r from-gray-100 via-gray-50 to-gray-100 dark:from-gray-800 dark:via-gray-700 dark:to-gray-800 animate-pulse rounded-xl relative overflow-hidden">
            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-shimmer"></div>
            <div className="flex items-end justify-between h-full p-6">
              {Array.from({ length: 12 }).map((_, i) => (
                <div
                  key={i}
                  className="bg-gray-200 dark:bg-gray-600 rounded-t animate-pulse"
                  style={{
                    height: `${Math.random() * 60 + 20}%`,
                    width: '6%',
                    animationDelay: `${i * 0.1}s`
                  }}
                />
              ))}
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className="border-0 shadow-lg bg-gradient-to-br from-white to-gray-50/50 dark:from-gray-900 dark:to-gray-800/50">
        <CardHeader className="pb-4">
          <CardTitle className="text-gray-800 dark:text-gray-200 font-semibold">Pagamentos Mensais</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col items-center justify-center py-12 px-6">
            <div className="w-16 h-16 bg-gradient-to-br from-red-100 to-red-200 dark:from-red-900/30 dark:to-red-800/30 rounded-full flex items-center justify-center mb-4">
              <svg className="w-8 h-8 text-red-500 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
            <p className="text-red-600 dark:text-red-400 text-center font-medium">{error}</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  const totalPaid = data.reduce((sum, item) => sum + item.paid, 0);
  const totalOverdue = data.reduce((sum, item) => sum + item.overdue, 0);
  const totalPaidAmount = data.reduce((sum, item) => sum + item.paidAmount, 0);
  const totalOverdueAmount = data.reduce((sum, item) => sum + item.overdueAmount, 0);

  return (
    <Card className="border-0 shadow-lg bg-gradient-to-br from-white to-gray-50/50 dark:from-gray-900 dark:to-gray-800/50 overflow-hidden">
      <CardHeader className="pb-6 bg-gradient-to-r from-gray-50/50 to-transparent dark:from-gray-800/50 dark:to-transparent">
        <CardTitle className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
          <span className="text-xl font-semibold text-gray-800 dark:text-gray-200">
            Pagamentos Mensais - {year}
          </span>
          <div className="flex flex-wrap gap-6 text-sm">
            <div className="flex items-center gap-2 px-3 py-1.5 bg-emerald-50 dark:bg-emerald-900/20 rounded-full">
              <div className="w-2 h-2 rounded-full bg-gradient-to-r from-emerald-500 to-green-500"></div>
              <span className="text-emerald-700 dark:text-emerald-300 font-medium">
                Pagos: {totalPaid} ({formatCurrency(totalPaidAmount)})
              </span>
            </div>
            <div className="flex items-center gap-2 px-3 py-1.5 bg-red-50 dark:bg-red-900/20 rounded-full">
              <div className="w-2 h-2 rounded-full bg-gradient-to-r from-red-500 to-rose-500"></div>
              <span className="text-red-700 dark:text-red-300 font-medium">
                Vencidos: {totalOverdue} ({formatCurrency(totalOverdueAmount)})
              </span>
            </div>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent className="pt-2">
        <div className="relative">
          <ResponsiveContainer width="100%" height={350}>
            <BarChart
              data={data}
              margin={{ top: 30, right: 40, left: 20, bottom: 20 }}
              barCategoryGap="20%"
            >
              <defs>
                <linearGradient id="paidGradient" x1="0" y1="0" x2="0" y2="1">
                  <stop offset="0%" stopColor="#10b981" stopOpacity={0.9} />
                  <stop offset="100%" stopColor="#059669" stopOpacity={0.7} />
                </linearGradient>
                <linearGradient id="overdueGradient" x1="0" y1="0" x2="0" y2="1">
                  <stop offset="0%" stopColor="#ef4444" stopOpacity={0.9} />
                  <stop offset="100%" stopColor="#dc2626" stopOpacity={0.7} />
                </linearGradient>
              </defs>
              <CartesianGrid
                strokeDasharray="2 4"
                className="opacity-20 dark:opacity-10"
                stroke="#e5e7eb"
                vertical={false}
              />
              <XAxis
                dataKey="displayMonth"
                tick={{
                  fontSize: 13,
                  fill: 'currentColor',
                  fontWeight: 500
                }}
                className="text-gray-600 dark:text-gray-400"
                axisLine={false}
                tickLine={false}
                dy={10}
              />
              <YAxis
                tick={{
                  fontSize: 12,
                  fill: 'currentColor',
                  fontWeight: 500
                }}
                className="text-gray-600 dark:text-gray-400"
                axisLine={false}
                tickLine={false}
                dx={-10}
              />
              <Tooltip
                content={<CustomTooltip />}
                cursor={{ fill: 'rgba(0, 0, 0, 0.05)', radius: 8 }}
              />
              <Legend
                wrapperStyle={{
                  paddingTop: '20px',
                  fontSize: '14px',
                  fontWeight: '500'
                }}
              />
              <Bar
                dataKey="paid"
                fill="url(#paidGradient)"
                radius={[6, 6, 0, 0]}
                className="drop-shadow-sm"
                name="Pagos"
                animationDuration={800}
                animationBegin={0}
              />
              <Bar
                dataKey="overdue"
                fill="url(#overdueGradient)"
                radius={[6, 6, 0, 0]}
                className="drop-shadow-sm"
                name="Vencidos"
                animationDuration={800}
                animationBegin={200}
              />
            </BarChart>
          </ResponsiveContainer>
        </div>
      </CardContent>
    </Card>
  );
}