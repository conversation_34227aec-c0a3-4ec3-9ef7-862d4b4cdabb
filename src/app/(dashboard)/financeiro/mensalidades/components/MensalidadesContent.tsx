'use client';

import { useState } from 'react';
import { MensalidadesMetrics } from './MensalidadesMetrics';
import { RecentPaymentsTable } from '@/src/components/financeiro/mensalidades/RecentPaymentsTable';
import { RevenueChart } from './RevenueChart';
import { DateRange } from 'react-day-picker';

export function MensalidadesContent() {
  const [date, setDate] = useState<DateRange | undefined>({
    from: new Date(2025, 0, 1),
    to: new Date(2025, 0, 31),
  });

  return (
    <div className="space-y-6">
      {/* Seletor de período */}
      {/* <div className="flex items-center justify-end">
        <Popover>
          <PopoverTrigger asChild>
            <Button
              id="date"
              variant={"outline"}
              className={cn(
                "w-[300px] justify-start text-left font-normal",
                !date && "text-muted-foreground"
              )}
            >
              <CalendarIcon className="mr-2 h-4 w-4" />
              {date?.from ? (
                date.to ? (
                  <>
                    {format(date.from, "dd/MM/yyyy", { locale: ptBR })} -{" "}
                    {format(date.to, "dd/MM/yyyy", { locale: ptBR })}
                  </>
                ) : (
                  format(date.from, "dd/MM/yyyy", { locale: ptBR })
                )
              ) : (
                <span>Selecione o período</span>
              )}
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-auto p-0" align="start">
            <CalendarComponent
              initialFocus
              mode="range"
              defaultMonth={date?.from}
              selected={date}
              onSelect={setDate}
              numberOfMonths={2}
              locale={ptBR}
            />
          </PopoverContent>
        </Popover>
      </div> */}

      {/* Métricas de mensalidades */}
      <MensalidadesMetrics />

      {/* Gráfico de receita e tabela de pagamentos recentes */}
      <div className="grid gap-6 md:grid-cols-2">
        <RevenueChart />
        <RecentPaymentsTable limit={8} />
      </div>
    </div>
  );
} 