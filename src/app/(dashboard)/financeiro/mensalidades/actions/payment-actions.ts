'use server';

import { createTenantServerClient } from '@/services/supabase/server';
import { PaymentMetrics, Payment, MonthlyRevenue, MonthlyPaymentStatus, PaymentMethodDistribution, TuitionMetrics, TuitionValueMetrics } from '../types';
import { formatPaymentMethodName } from '@/utils/payment-method-formatter';

export async function getTuitionMetrics(): Promise<{ 
  success: boolean; 
  data?: TuitionMetrics; 
  error?: string 
}> {
  try {
    const supabase = await createTenantServerClient();
    
    // Obter o tenant_id do usuário atual
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      return { success: false, error: 'Usuário não autenticado' };
    }

    const { data: userWithTenant, error: userError } = await supabase
      .from('users')
      .select('tenant_id')
      .eq('id', user.id)
      .single();

    if (userError || !userWithTenant?.tenant_id) {
      return { success: false, error: 'Tenant não encontrado' };
    }

    // Usar a função RPC agregada que funciona corretamente
    const { data: metrics, error } = await supabase
      .rpc('get_tuition_metrics' as any, { tenant_uuid: userWithTenant.tenant_id });

    if (error) {
      console.error('Error fetching tuition metrics:', error);
      return { success: false, error: 'Erro ao buscar métricas de mensalidades' };
    }

    if (!metrics || !Array.isArray(metrics) || metrics.length === 0) {
      // Retornar métricas zeradas se não houver dados
      return {
        success: true,
        data: {
          paidTuitions: 0,
          scheduledTuitions: 0,
          overdueTuitions: 0,
          monthlyRevenueAverage: 0,
        }
      };
    }

    const result = metrics[0];
    
    return {
      success: true,
      data: {
        paidTuitions: Number(result.paid_tuitions) || 0,
        scheduledTuitions: Number(result.scheduled_tuitions) || 0,
        overdueTuitions: Number(result.overdue_tuitions) || 0,
        monthlyRevenueAverage: parseFloat(result.monthly_revenue_average) || 0,
      }
    };
  } catch (error) {
    console.error('Error in getTuitionMetrics:', error);
    return { success: false, error: 'Erro interno do servidor' };
  }
}

export async function getTuitionValueMetrics(): Promise<{
  success: boolean;
  data?: TuitionValueMetrics;
  error?: string
}> {
  try {
    const supabase = await createTenantServerClient();

    // Obter o tenant_id do usuário atual
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      return { success: false, error: 'Usuário não autenticado' };
    }

    const { data: userWithTenant, error: userError } = await supabase
      .from('users')
      .select('tenant_id')
      .eq('id', user.id)
      .single();

    if (userError || !userWithTenant?.tenant_id) {
      return { success: false, error: 'Tenant não encontrado' };
    }

    // Usar a nova função RPC para valores
    const { data: metrics, error } = await supabase
      .rpc('get_tuition_value_metrics' as any, { tenant_uuid: userWithTenant.tenant_id });

    if (error) {
      console.error('Error fetching tuition value metrics:', error);
      return { success: false, error: 'Erro ao buscar métricas de valores de mensalidades' };
    }

    if (!metrics || !Array.isArray(metrics) || metrics.length === 0) {
      // Retornar métricas zeradas se não houver dados
      return {
        success: true,
        data: {
          paidTuitionsValue: 0,
          scheduledTuitionsValue: 0,
          overdueTuitionsValue: 0,
          monthlyRevenueAverage: 0,
          expectedMonthlyRevenue: 0,
        }
      };
    }

    const result = metrics[0];

    return {
      success: true,
      data: {
        paidTuitionsValue: parseFloat(result.paid_tuitions_value) || 0,
        scheduledTuitionsValue: parseFloat(result.scheduled_tuitions_value) || 0,
        overdueTuitionsValue: parseFloat(result.overdue_tuitions_value) || 0,
        monthlyRevenueAverage: parseFloat(result.monthly_revenue_average) || 0,
        expectedMonthlyRevenue: parseFloat(result.expected_monthly_revenue) || 0,
      }
    };
  } catch (error) {
    console.error('Error in getTuitionValueMetrics:', error);
    return { success: false, error: 'Erro interno do servidor' };
  }
}

export async function getPaymentMetrics(
  startDate?: Date,
  endDate?: Date
): Promise<{ success: boolean; data?: PaymentMetrics; error?: string }> {
  try {
    const supabase = await createTenantServerClient();
    
    // Construir query usando QueryBuilder do Supabase
    let query = supabase
      .from('payments')
      .select('amount, status, created_at');
    
    if (startDate && endDate) {
      query = query
        .gte('created_at', startDate.toISOString())
        .lte('created_at', endDate.toISOString());
    }
    
    const { data: payments, error } = await query;
    
    if (error) {
      console.error('Error fetching payments:', error);
      return { success: false, error: 'Erro ao buscar pagamentos' };
    }
    
    // Calcular métricas em JavaScript
    const totalPayments = payments?.length || 0;
    const paidPayments = payments?.filter((p: any) => p.status === 'paid').length || 0;
    const pendingPayments = payments?.filter((p: any) => p.status === 'pending').length || 0;
    const overduePayments = payments?.filter((p: any) => p.status === 'overdue').length || 0;
    
    const paidPaymentAmounts = payments?.filter((p: any) => p.status === 'paid').map((p: any) => parseFloat(p.amount)) || [];
    const totalRevenue = paidPaymentAmounts.reduce((sum: number, amount: number) => sum + amount, 0);
    const averagePayment = paidPaymentAmounts.length > 0 ? totalRevenue / paidPaymentAmounts.length : 0;
    
    return {
      success: true,
      data: {
        totalPayments,
        paidPayments,
        pendingPayments,
        failedPayments: overduePayments, // Manter compatibilidade com interface
        totalRevenue,
        averagePayment,
      }
    };
  } catch (error) {
    console.error('Error in getPaymentMetrics:', error);
    return { success: false, error: 'Erro interno do servidor' };
  }
}

export async function getRecentPayments(
  limit: number = 10
): Promise<{ success: boolean; data?: Payment[]; error?: string }> {
  try {
    const supabase = await createTenantServerClient();
    
    const { data, error } = await supabase
      .from('payments')
      .select(`
        id,
        student_id,
        amount,
        currency,
        status,
        payment_method,
        paid_at,
        created_at,
        students!inner(
          user_id,
          users!students_user_id_fkey(
            full_name,
            email
          )
        )
      `)
      .eq('status', 'paid')
      .order('paid_at', { ascending: false })
      .limit(limit);
    
    if (error) {
      console.error('Error fetching recent payments:', error);
      return { success: false, error: 'Erro ao buscar pagamentos recentes' };
    }
    
    const payments: Payment[] = (data || []).map((payment: any) => ({
      id: payment.id,
      studentId: payment.student_id,
      amount: parseFloat(payment.amount),
      currency: payment.currency,
      status: payment.status,
      paymentMethod: payment.payment_method,
      paidAt: payment.paid_at,
      createdAt: payment.created_at,
      studentName: payment.students.users.full_name,
      studentEmail: payment.students.users.email,
    }));
    
    return { success: true, data: payments };
  } catch (error) {
    console.error('Error in getRecentPayments:', error);
    return { success: false, error: 'Erro interno do servidor' };
  }
}

export async function getMonthlyRevenue(
  year: number = new Date().getFullYear()
): Promise<{ success: boolean; data?: MonthlyRevenue[]; error?: string }> {
  try {
    const supabase = await createTenantServerClient();
    
    const startOfYear = new Date(year, 0, 1);
    const endOfYear = new Date(year, 11, 31, 23, 59, 59);
    
    const { data: payments, error } = await supabase
      .from('payments')
      .select('amount, status, created_at')
      .gte('created_at', startOfYear.toISOString())
      .lte('created_at', endOfYear.toISOString())
      .eq('status', 'paid');
    
    if (error) {
      console.error('Error fetching payments for monthly revenue:', error);
      return { success: false, error: 'Erro ao buscar receita mensal' };
    }
    
    // Agrupar por mês em JavaScript
    const monthlyData: { [key: string]: { revenue: number; payments: number } } = {};
    
    // Inicializar todos os meses do ano
    for (let month = 0; month < 12; month++) {
      const monthKey = `${year}-${String(month + 1).padStart(2, '0')}`;
      monthlyData[monthKey] = { revenue: 0, payments: 0 };
    }
    
    // Agregar dados dos pagamentos
    (payments || []).forEach((payment: any) => {
      const date = new Date(payment.created_at);
      const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
      
      if (monthlyData[monthKey]) {
        monthlyData[monthKey].revenue += parseFloat(payment.amount);
        monthlyData[monthKey].payments += 1;
      }
    });
    
    // Converter para array
    const monthlyRevenue: MonthlyRevenue[] = Object.entries(monthlyData).map(([month, data]) => ({
      month,
      revenue: data.revenue,
      payments: data.payments,
    }));
    
    return { success: true, data: monthlyRevenue };
  } catch (error) {
    console.error('Error in getMonthlyRevenue:', error);
    return { success: false, error: 'Erro interno do servidor' };
  }
}

export async function getPaymentMethodDistribution(): Promise<{
  success: boolean;
  data?: PaymentMethodDistribution[];
  error?: string;
}> {
  try {
    const supabase = await createTenantServerClient();

    const { data: payments, error } = await supabase
      .from('payments')
      .select('payment_method')
      .eq('status', 'paid');

    if (error) {
      console.error('Error fetching payment methods:', error);
      return { success: false, error: 'Erro ao buscar métodos de pagamento' };
    }

    // Contar métodos de pagamento em JavaScript
    const methodCounts: { [key: string]: number } = {};
    const totalPayments = payments?.length || 0;

    (payments || []).forEach((payment: any) => {
      const method = formatPaymentMethodName(payment.payment_method);
      methodCounts[method] = (methodCounts[method] || 0) + 1;
    });

    // Converter para array com percentuais
    const distribution: PaymentMethodDistribution[] = Object.entries(methodCounts)
      .map(([method, count]) => ({
        method,
        count,
        percentage: totalPayments > 0 ? Math.round((count / totalPayments) * 100 * 100) / 100 : 0,
      }))
      .sort((a, b) => b.count - a.count);

    return { success: true, data: distribution };
  } catch (error) {
    console.error('Error in getPaymentMethodDistribution:', error);
    return { success: false, error: 'Erro interno do servidor' };
  }
}

export async function getMonthlyPaymentStatus(
  year: number = new Date().getFullYear()
): Promise<{ success: boolean; data?: MonthlyPaymentStatus[]; error?: string }> {
  try {
    const supabase = await createTenantServerClient();

    // Buscar todos os pagamentos para filtrar no JavaScript
    const { data: payments, error } = await supabase
      .from('payments')
      .select('amount, status, due_date, paid_at, created_at, overdue_date');

    if (error) {
      console.error('Error fetching payments for monthly status:', error);
      return { success: false, error: 'Erro ao buscar status mensal de pagamentos' };
    }

    // Agrupar por mês em JavaScript
    const monthlyData: { [key: string]: { paid: number; overdue: number; paidAmount: number; overdueAmount: number } } = {};

    // Inicializar todos os meses do ano
    for (let month = 0; month < 12; month++) {
      const monthKey = `${year}-${String(month + 1).padStart(2, '0')}`;
      monthlyData[monthKey] = { paid: 0, overdue: 0, paidAmount: 0, overdueAmount: 0 };
    }

    // Agregar dados dos pagamentos
    (payments || []).forEach((payment: any) => {
      const amount = parseFloat(payment.amount);

      // Contar pagamentos vencidos APENAS se tiverem status 'overdue' ou overdue_date preenchida
      if (payment.status === 'overdue') {
        // Para pagamentos com status overdue, usar a due_date para determinar o mês
        const dueDate = new Date(payment.due_date);
        if (dueDate.getFullYear() === year) {
          const dueMonthKey = `${dueDate.getFullYear()}-${String(dueDate.getMonth() + 1).padStart(2, '0')}`;
          monthlyData[dueMonthKey].overdue += 1;
          monthlyData[dueMonthKey].overdueAmount += amount;
        }
      } else if (payment.overdue_date) {
        // Para pagamentos com overdue_date definida, usar essa data
        const overdueDate = new Date(payment.overdue_date);
        if (overdueDate.getFullYear() === year) {
          const overdueMonthKey = `${overdueDate.getFullYear()}-${String(overdueDate.getMonth() + 1).padStart(2, '0')}`;
          monthlyData[overdueMonthKey].overdue += 1;
          monthlyData[overdueMonthKey].overdueAmount += amount;
        }
      }

      // Para pagamentos pagos, contar no mês em que foi pago (receita)
      if (payment.status === 'paid' && payment.paid_at) {
        const paidDate = new Date(payment.paid_at);
        // Só contar se o pagamento foi no ano especificado
        if (paidDate.getFullYear() === year) {
          const paidMonthKey = `${paidDate.getFullYear()}-${String(paidDate.getMonth() + 1).padStart(2, '0')}`;
          monthlyData[paidMonthKey].paid += 1;
          monthlyData[paidMonthKey].paidAmount += amount;
        }
      }
    });

    // Converter para array
    const monthlyPaymentStatus: MonthlyPaymentStatus[] = Object.entries(monthlyData).map(([month, data]) => ({
      month,
      paid: data.paid,
      overdue: data.overdue,
      paidAmount: data.paidAmount,
      overdueAmount: data.overdueAmount,
    }));

    return { success: true, data: monthlyPaymentStatus };
  } catch (error) {
    console.error('Error in getMonthlyPaymentStatus:', error);
    return { success: false, error: 'Erro interno do servidor' };
  }
}