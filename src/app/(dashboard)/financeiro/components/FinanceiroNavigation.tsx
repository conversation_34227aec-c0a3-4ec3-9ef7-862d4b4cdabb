'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { cn } from '@/lib/utils';
import {
  DollarSign,
  CreditCard,
  Clock,
  Wallet,
  TrendingUp,
  Settings,
  BarChart3
} from 'lucide-react';

const financeiroRoutes = [
  {
    href: '/financeiro/dashboard',
    label: 'Dashboard',
    icon: BarChart3,
  },
  {
    href: '/financeiro/mensalidades',
    label: 'Mensalidades',
    icon: DollarSign,
  },
  {
    href: '/financeiro/pagamentos',
    label: 'Pagamentos',
    icon: CreditCard,
  },
  {
    href: '/financeiro/recorrentes',
    label: 'Recorrentes',
    icon: Clock,
  },
  // {
  //   href: '/financeiro/descontos',
  //   label: 'Descontos',
  //   icon: Percent,
  // },
  {
    href: '/financeiro/formas-pagamento',
    label: 'Formas de Pagamento',
    icon: Wallet,
  },
  // {
  //   href: '/financeiro/contabilidade',
  //   label: 'Contabilidade',
  //   icon: Calculator,
  // },
  {
    href: '/financeiro/crescimento',
    label: 'Crescimento',
    icon: TrendingUp,
  },
  {
    href: '/financeiro/configuracoes',
    label: 'Configurações',
    icon: Settings,
  },
];

export function FinanceiroNavigation() {
  const pathname = usePathname();

  return (
    <div className="border-b bg-background">
      <div className="px-6">
        <nav className="flex space-x-1 overflow-x-auto scrollbar-hide" aria-label="Navegação Financeiro">
          {financeiroRoutes.map((route) => {
            const isActive = pathname === route.href;
            const Icon = route.icon;
            
            return (
              <Link
                key={route.href}
                href={route.href}
                className={cn(
                  'flex items-center gap-2 px-3 py-3 text-sm font-medium transition-colors whitespace-nowrap',
                  'border-b-2 border-transparent',
                  'hover:text-foreground hover:border-muted-foreground/20',
                  isActive
                    ? 'text-tenant-primary tenant-primary-border'
                    : 'text-muted-foreground'
                )}
              >
                <Icon className="h-4 w-4" />
                {route.label}
              </Link>
            );
          })}
        </nav>
      </div>
    </div>
  );
} 