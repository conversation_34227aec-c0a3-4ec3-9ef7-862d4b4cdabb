'use client'

import React, { useEffect, useState } from 'react'
import { FileText, DollarSign, Clock, Users, Settings, Gift } from 'lucide-react'

import { PlanForm } from '@/components/features/planos'
import { usePlanForm } from '@/components/features/planos/PlanFormContext'
import { usePlanFormStatus } from '@/components/features/planos/hooks/usePlanFormStatus'

const sectionErrorOrder: (keyof NonNullable<
  ReturnType<typeof usePlanForm>['submissionErrors']
>)[] = ['details', 'benefits', 'pricing', 'duration', 'academyAccess']

const sectionToAccordion: Record<string, string> = {
  academyAccess: 'access',
}

export function EditarPlanoClient() {
  const { submitForm, isSubmitting, submissionErrors } = usePlanForm()
  const { pricingStatus, durationStatus, academyAccessStatus } =
    usePlanFormStatus()
  const [activeAccordion, setActiveAccordion] = useState<string | undefined>(
    'details',
  )

  useEffect(() => {
    if (submissionErrors) {
      const errorSection = sectionErrorOrder.find(
        (section) => submissionErrors[section],
      )
      if (errorSection) {
        const accordionValue =
          sectionToAccordion[errorSection as string] || String(errorSection)
        setActiveAccordion(accordionValue)
        setTimeout(() => {
          const element = document.querySelector(
            `[data-value="${accordionValue}"]`,
          )
          element?.scrollIntoView({ behavior: 'smooth', block: 'center' })
        }, 100)
      }
    }
  }, [submissionErrors])

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    submitForm()
  }

  return (
    <form onSubmit={handleSubmit}>
      <PlanForm.Container.Root title="Editar Plano">
        <PlanForm.Container.Content
          accordionType="single"
          collapsible
          value={activeAccordion}
          onValueChange={setActiveAccordion}
        >
          <PlanForm.Section.Root value="details">
            <PlanForm.Section.Header
              icon={<FileText className="h-5 w-5" />}
              title="Detalhes da Assinatura"
              description="Informações gerais e configurações da assinatura."
              iconColor="blue"
            />
            <PlanForm.Section.Content>
              <PlanForm.Details />
            </PlanForm.Section.Content>
          </PlanForm.Section.Root>

          <PlanForm.Section.Root value="benefits">
            <PlanForm.Section.Header
              icon={<Gift className="h-5 w-5" />}
              title="Benefícios"
              description="Liste os benefícios oferecidos por este plano."
              iconColor="green"
            />
            <PlanForm.Section.Content>
              <PlanForm.Benefits />
            </PlanForm.Section.Content>
          </PlanForm.Section.Root>

          <PlanForm.Section.Root value="pricing">
            <PlanForm.Section.Header
              icon={<DollarSign className="h-5 w-5" />}
              title="Preços"
              description="Configure os preços e opções de desconto para a assinatura."
              iconColor="green"
              status={
                <PlanForm.Section.Status variant={pricingStatus.variant}>
                  {pricingStatus.text}
                </PlanForm.Section.Status>
              }
            />
            <PlanForm.Section.Content>
              <PlanForm.Pricing />
            </PlanForm.Section.Content>
          </PlanForm.Section.Root>

          <PlanForm.Section.Root value="duration">
            <PlanForm.Section.Header
              icon={<Clock className="h-5 w-5" />}
              title="Duração"
              description="Gerencie a duração e opções de renovação das assinaturas."
              iconColor="orange"
              status={
                <PlanForm.Section.Status variant={durationStatus.variant}>
                  {durationStatus.text}
                </PlanForm.Section.Status>
              }
            />
            <PlanForm.Section.Content>
              <PlanForm.Duration />
            </PlanForm.Section.Content>
          </PlanForm.Section.Root>

          <PlanForm.Section.Root value="access">
            <PlanForm.Section.Header
              icon={<Users className="h-5 w-5" />}
              title="Acesso a Academia"
              description="Configure o acesso a modalidades e aulas para a assinatura."
              iconColor="purple"
              status={
                <PlanForm.Section.Status variant={academyAccessStatus.variant}>
                  {academyAccessStatus.text}
                </PlanForm.Section.Status>
              }
            />
            <PlanForm.Section.Content>
              <PlanForm.AcademyAccess />
            </PlanForm.Section.Content>
          </PlanForm.Section.Root>
        </PlanForm.Container.Content>

        <PlanForm.Container.Actions alignment="center">
          <PlanForm.Button.Submit
            size="lg"
            icon={<Settings className="h-5 w-5" />}
            submitText="SALVAR ALTERAÇÕES"
            submittingText="SALVANDO..."
            submitting={isSubmitting}
            className="px-8 py-3"
          />
        </PlanForm.Container.Actions>
      </PlanForm.Container.Root>

      <PlanForm.SuccessFeedback />
    </form>
  )
} 