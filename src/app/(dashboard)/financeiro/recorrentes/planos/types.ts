export type PlanStatus = 'draft' | 'active' | 'archived' | 'paused'

export interface Plan {
  id: string
  title: string
  status: PlanStatus
  price: number | null
  period: string // ex: "mês", "3 meses", "ano"
  billing_period: string // ex: "Mensal", "Trimestral", etc.
  students: number
  monthly_revenue?: number
  annual_revenue?: number
  benefits: string[]
  modalities: string[] // nomes das modalidades incluídas no plano
  theme: {
    icon: React.ElementType
    iconColor: string
    bgColor: string
    priceColor: string
    borderColor?: string
    badgeClass?: string
  }
  discount?: string
  duration?: string
  cancellation_fee?: number | null
  enrollment_fee?: number | null
  late_fee?: number | null
  late_days?: number | null
  max_payments?: number | null
  capacity?: 'limited' | 'unlimited'
  max_capacity?: number | null
}