'use client'

import { Plan } from '../types'

// TODO: Mover para um arquivo de utils
const formatCurrency = (value: number) => {
  return new Intl.NumberFormat('pt-BR', {
    style: 'currency',
    currency: 'BRL',
  }).format(value)
}

type PriceDisplayProps = Pick<Plan, 'price' | 'period' | 'discount' | 'theme'>

export const PriceDisplay = ({
  price,
  period,
  discount,
  theme,
}: PriceDisplayProps) => {
  const displayPrice = price === null ? 'Grátis' : formatCurrency(price)

  const displayPeriod =
    period === 'Pagamento único' || period.startsWith('por ') || price === null
      ? period
      : `por ${period}`

  return (
    <div className={`text-center py-4 border rounded-lg ${theme.bgColor}`}>
      <p className={`text-3xl font-bold ${theme.priceColor}`}>{displayPrice}</p>
      <p className="text-sm text-muted-foreground">{displayPeriod}</p>
      {discount && (
        <p className={`text-xs ${theme.priceColor} font-medium`}>{discount}</p>
      )}
    </div>
  )
} 