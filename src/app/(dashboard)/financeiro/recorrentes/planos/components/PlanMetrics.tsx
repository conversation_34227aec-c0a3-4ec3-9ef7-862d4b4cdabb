'use client'

import { useEffect, useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { getPlanMetrics, type PlanMetrics } from '@/app/(dashboard)/academia/actions'
import { Users, DollarSign, TrendingUp } from 'lucide-react'
import { MdCardMembership } from "react-icons/md";


export function PlanMetrics() {
  const [metrics, setMetrics] = useState<PlanMetrics | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    async function fetchMetrics() {
      try {
        setLoading(true)
        setError(null)

        const result = await getPlanMetrics()

        if (result.success && result.data) {
          setMetrics(result.data)
        } else {
          setError(result.errors?._form || 'Erro ao carregar métricas')
        }
      } catch (err) {
        console.error('Erro ao buscar métricas:', err)
        setError('Erro interno ao carregar métricas')
      } finally {
        setLoading(false)
      }
    }

    fetchMetrics()
  }, [])

  if (loading) {
    return (
      <div className="grid gap-4 md:grid-cols-4 mb-6">
        {Array.from({ length: 4 }).map((_, index) => (
          <Card key={index} className="border-0 shadow-lg bg-gradient-to-br from-white to-gray-50/50 dark:from-gray-900 dark:to-gray-800/50 overflow-hidden">
            <CardHeader className="pb-4 bg-gradient-to-r from-gray-50/50 to-transparent dark:from-gray-800/50 dark:to-transparent">
              <div className="h-4 bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200 dark:from-gray-700 dark:via-gray-600 dark:to-gray-700 animate-pulse rounded-full w-24 relative overflow-hidden">
                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-shimmer"></div>
              </div>
            </CardHeader>
            <CardContent className="pt-2">
              <div className="text-center space-y-2">
                <div className="h-6 bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200 dark:from-gray-700 dark:via-gray-600 dark:to-gray-700 animate-pulse rounded-full w-20 mx-auto relative overflow-hidden">
                  <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-shimmer"></div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  if (error) {
    return (
      <div className="grid gap-4 md:grid-cols-4 mb-6">
        <Card className="md:col-span-4 border-0 shadow-lg bg-gradient-to-br from-white to-gray-50/50 dark:from-gray-900 dark:to-gray-800/50">
          <CardContent className="pt-8 pb-8">
            <div className="flex flex-col items-center justify-center">
              <div className="w-16 h-16 bg-gradient-to-br from-red-100 to-red-200 dark:from-red-900/30 dark:to-red-800/30 rounded-full flex items-center justify-center mb-4">
                <svg className="w-8 h-8 text-red-500 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
              </div>
              <p className="text-red-600 dark:text-red-400 text-center font-medium">{error}</p>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (!metrics) {
    return null
  }

  const colorConfig = {
    blue: {
      text: 'text-blue-600 dark:text-blue-400',
      bg: 'bg-blue-100 dark:bg-blue-900/30',
      header: 'from-blue-50/50 to-transparent dark:from-blue-900/20 dark:to-transparent',
      icon: 'text-blue-600 dark:text-blue-400'
    },
    green: {
      text: 'text-emerald-600 dark:text-emerald-400',
      bg: 'bg-emerald-100 dark:bg-emerald-900/30',
      header: 'from-emerald-50/50 to-transparent dark:from-emerald-900/20 dark:to-transparent',
      icon: 'text-emerald-600 dark:text-emerald-400'
    },
    purple: {
      text: 'text-purple-600 dark:text-purple-400',
      bg: 'bg-purple-100 dark:bg-purple-900/30',
      header: 'from-purple-50/50 to-transparent dark:from-purple-900/20 dark:to-transparent',
      icon: 'text-purple-600 dark:text-purple-400'
    },
    orange: {
      text: 'text-orange-600 dark:text-orange-400',
      bg: 'bg-orange-100 dark:bg-orange-900/30',
      header: 'from-orange-50/50 to-transparent dark:from-orange-900/20 dark:to-transparent',
      icon: 'text-orange-600 dark:text-orange-400'
    }
  }

  const metricsData = [
    {
      value: metrics.activePlansCount.toString(),
      label: 'Planos de Matrícula Ativos',
      color: 'blue' as keyof typeof colorConfig,
      icon: TrendingUp
    },
    {
      value: metrics.totalEnrolledStudents.toString(),
      label: 'Alunos Matriculados em Planos',
      color: 'green' as keyof typeof colorConfig,
      icon: Users
    },
    {
      value: `R$ ${metrics.averageTicket.toFixed(2).replace('.', ',')}`,
      label: 'Ticket Médio por Aluno',
      color: 'purple' as keyof typeof colorConfig,
      icon: DollarSign
    },
    {
      value: metrics.mostPopularPlan,
      label: 'Plano Mais Escolhido',
      color: 'orange' as keyof typeof colorConfig,
      icon: MdCardMembership
    },
  ]

  return (
    <div className="grid gap-4 md:grid-cols-4 mb-6">
      {metricsData.map((metric) => {
        const config = colorConfig[metric.color]
        const IconComponent = metric.icon

        return (
          <Card key={metric.label} className="border-0 shadow-lg bg-gradient-to-br from-white to-gray-50/50 dark:from-gray-900 dark:to-gray-800/50 overflow-hidden group hover:shadow-xl transition-all duration-300">
            <CardHeader className={`pb-4 bg-gradient-to-r ${config.header}`}>
              <CardTitle className="text-sm font-semibold text-gray-700 dark:text-gray-300 flex items-center gap-2">
                <div className={`p-1 ${config.bg} rounded-full`}>
                  <IconComponent className={`w-4 h-4 ${config.icon}`} />
                </div>
                {metric.label}
              </CardTitle>
            </CardHeader>
            <CardContent className="pt-2">
              <div className="text-center space-y-1">
                <p className={`text-2xl font-bold ${config.text} tracking-tight`}>
                  {metric.value}
                </p>
              </div>
            </CardContent>
          </Card>
        )
      })}
    </div>
  )
}