import { <PERSON>ada<PERSON> } from 'next';
import { RecorrentesPageClient } from '@/components/financeiro/recorrentes';
import { RowActionProvider } from '@/app/(dashboard)/alunos/hooks/use-row-action-animation';

export const metadata: Metadata = {
  title: 'Recorrentes - Financeiro',
  description: 'Configure e gerencie assinaturas e pagamentos automáticos',
};

export default function RecorrentesPage() {
  return (
    <RowActionProvider>
      <RecorrentesPageClient />
    </RowActionProvider>
  );
}