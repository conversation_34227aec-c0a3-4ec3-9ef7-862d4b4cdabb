'use server';

import { z } from 'zod';
import { createTenantServerClient } from '@/services/supabase/server';
import { getCurrentUser } from '@/services/auth/actions/auth-actions';

/**
 * Interface para as métricas de recorrentes
 */
export interface MetricsData {
  alunosEmDia: number;
  receitaMensal: number;
  alunosAtrasados: number;
  taxaSucesso: number;
}

/**
 * Schema para validação de filtros de métricas
 */
const metricsFiltersSchema = z.object({
  month: z.number().min(1).max(12).optional(),
  year: z.number().min(2020).max(2030).optional(),
}).optional();

export type MetricsFilters = z.infer<typeof metricsFiltersSchema>;

interface ActionResult<T = unknown> {
  success: boolean;
  data?: T;
  errors?: Record<string, string>;
}

/**
 * Calcula as métricas de pagamentos recorrentes de forma otimizada
 * Utiliza queries SQL otimizadas para buscar dados diretamente do banco
 */
export async function getRecurrentMetrics(
  filters?: MetricsFilters
): Promise<ActionResult<MetricsData>> {
  try {
    // Validar filtros
    const validatedFilters = metricsFiltersSchema.parse(filters);
    
    // Verificar autenticação
    const user = await getCurrentUser();
    if (!user) {
      return {
        success: false,
        errors: { auth: 'Usuário não autenticado' }
      };
    }

    const tenantId = (user.app_metadata as any)?.tenant_id;
    if (!tenantId) {
      return {
        success: false,
        errors: { tenant: 'Tenant não identificado' }
      };
    }

    const supabase = await createTenantServerClient();

    // Definir período para análise (mês atual se não especificado) - usando timezone do Brasil
    const nowBrazil = new Date().toLocaleDateString('en-CA', {
      timeZone: 'America/Sao_Paulo'
    });
    const now = new Date(nowBrazil);
    const targetMonth = validatedFilters?.month ?? now.getMonth() + 1;
    const targetYear = validatedFilters?.year ?? now.getFullYear();

    // Data de início e fim do mês
    const startDate = `${targetYear}-${targetMonth.toString().padStart(2, '0')}-01`;
    const endDate = new Date(targetYear, targetMonth, 0).toISOString().split('T')[0];

    // Query otimizada para buscar todas as métricas em uma única consulta
    const { data: metricsData, error } = await supabase.rpc('get_recurrent_metrics', {
      p_tenant_id: tenantId,
      p_start_date: startDate,
      p_end_date: endDate
    });

    if (error) {
      console.error('Erro ao buscar métricas:', error);
      
      // Fallback: calcular métricas usando queries separadas
      return await calculateMetricsFallback(supabase, tenantId, startDate, endDate);
    }

    if (!metricsData || metricsData.length === 0) {
      return {
        success: true,
        data: {
          alunosEmDia: 0,
          receitaMensal: 0,
          alunosAtrasados: 0,
          taxaSucesso: 0
        }
      };
    }

    const metrics = metricsData[0];

    return {
      success: true,
      data: {
        alunosEmDia: Number(metrics.alunos_em_dia) || 0,
        receitaMensal: parseFloat(String(metrics.receita_mensal || '0')),
        alunosAtrasados: Number(metrics.alunos_atrasados) || 0,
        taxaSucesso: parseFloat(String(metrics.taxa_sucesso || '0'))
      }
    };

  } catch (error) {
    console.error('Erro ao calcular métricas de recorrentes:', error);
    return {
      success: false,
      errors: { 
        _form: 'Erro interno ao calcular métricas. Tente novamente.' 
      }
    };
  }
}

/**
 * Função de fallback para calcular métricas usando queries separadas
 * Utilizada quando a função RPC não está disponível
 */
async function calculateMetricsFallback(
  supabase: any,
  tenantId: string,
  startDate: string,
  endDate: string
): Promise<ActionResult<MetricsData>> {
  try {
    // 1. Alunos com mensalidade em dia (pagamentos 'paid' no período)
    const { data: alunosEmDiaData, error: alunosEmDiaError } = await supabase
      .from('payments')
      .select('student_id')
      .eq('tenant_id', tenantId)
      .eq('status', 'paid')
      .gte('due_date', startDate)
      .lte('due_date', endDate)
      .not('membership_id', 'is', null);

    if (alunosEmDiaError) throw alunosEmDiaError;

    // Contar alunos únicos
    const alunosEmDia = new Set(alunosEmDiaData?.map((p: { student_id: string }) => p.student_id) || []).size;

    // 2. Receita mensal (soma dos pagamentos 'paid' no período)
    const { data: receitaData, error: receitaError } = await supabase
      .from('payments')
      .select('amount')
      .eq('tenant_id', tenantId)
      .eq('status', 'paid')
      .gte('due_date', startDate)
      .lte('due_date', endDate)
      .not('membership_id', 'is', null);

    if (receitaError) throw receitaError;

    const receitaMensal = receitaData?.reduce((sum: number, payment: { amount: string | number | null }) =>
      sum + parseFloat(String(payment.amount || '0')), 0
    ) || 0;

    // 3. Alunos com pagamento atrasado (status 'overdue' ou com overdue_date preenchido)
    const { data: alunosAtrasadosData, error: alunosAtrasadosError } = await supabase
      .from('payments')
      .select('student_id')
      .eq('tenant_id', tenantId)
      .or('status.eq.overdue,overdue_date.not.is.null')
      .not('membership_id', 'is', null);

    if (alunosAtrasadosError) throw alunosAtrasadosError;

    const alunosAtrasados = new Set(alunosAtrasadosData?.map((p: { student_id: string }) => p.student_id) || []).size;

    // 4. Taxa de sucesso (pagamentos paid / total de pagamentos no período)
    const { data: totalPagamentosData, error: totalPagamentosError } = await supabase
      .from('payments')
      .select('id, status')
      .eq('tenant_id', tenantId)
      .gte('due_date', startDate)
      .lte('due_date', endDate)
      .not('membership_id', 'is', null);

    if (totalPagamentosError) throw totalPagamentosError;

    const totalPagamentos = totalPagamentosData?.length || 0;
    const pagamentosPagos = totalPagamentosData?.filter((p: { status: string }) => p.status === 'paid').length || 0;
    const taxaSucesso = totalPagamentos > 0 ? (pagamentosPagos / totalPagamentos) * 100 : 0;

    return {
      success: true,
      data: {
        alunosEmDia,
        receitaMensal,
        alunosAtrasados,
        taxaSucesso: Math.round(taxaSucesso * 100) / 100 // Arredondar para 2 casas decimais
      }
    };

  } catch (error) {
    console.error('Erro no fallback de métricas:', error);
    return {
      success: false,
      errors: { 
        _form: 'Erro ao calcular métricas. Tente novamente.' 
      }
    };
  }
}

/**
 * Busca métricas de comparação com o mês anterior
 */
export async function getMetricsComparison(): Promise<ActionResult<{
  current: MetricsData;
  previous: MetricsData;
  growth: {
    alunosEmDia: number;
    receitaMensal: number;
    alunosAtrasados: number;
    taxaSucesso: number;
  };
}>> {
  try {
    const now = new Date();
    const currentMonth = now.getMonth() + 1;
    const currentYear = now.getFullYear();
    
    // Calcular mês anterior
    const previousMonth = currentMonth === 1 ? 12 : currentMonth - 1;
    const previousYear = currentMonth === 1 ? currentYear - 1 : currentYear;

    // Buscar métricas do mês atual e anterior
    const [currentResult, previousResult] = await Promise.all([
      getRecurrentMetrics({ month: currentMonth, year: currentYear }),
      getRecurrentMetrics({ month: previousMonth, year: previousYear })
    ]);

    if (!currentResult.success || !previousResult.success) {
      return {
        success: false,
        errors: { 
          _form: 'Erro ao buscar dados de comparação' 
        }
      };
    }

    const current = currentResult.data!;
    const previous = previousResult.data!;

    // Calcular crescimento percentual para valores absolutos (receita, quantidade)
    const calculatePercentualGrowth = (current: number, previous: number): number => {
      // Se ambos são zero, não há mudança
      if (previous === 0 && current === 0) return 0;

      // Se anterior era zero mas atual tem valor, considerar como "novo" (sem mostrar %)
      if (previous === 0 && current > 0) return 0;

      // Cálculo normal de crescimento percentual
      return Math.round(((current - previous) / previous) * 100 * 100) / 100;
    };

    // Calcular diferença em pontos percentuais para métricas que já são porcentagens
    const calculatePercentagePointsDifference = (current: number, previous: number): number => {
      // Para taxa de sucesso, mostrar diferença em pontos percentuais
      return Math.round((current - previous) * 100) / 100;
    };

    const growth = {
      alunosEmDia: calculatePercentualGrowth(current.alunosEmDia, previous.alunosEmDia),
      receitaMensal: calculatePercentualGrowth(current.receitaMensal, previous.receitaMensal),
      alunosAtrasados: calculatePercentualGrowth(current.alunosAtrasados, previous.alunosAtrasados),
      taxaSucesso: calculatePercentagePointsDifference(current.taxaSucesso, previous.taxaSucesso)
    };

    return {
      success: true,
      data: {
        current,
        previous,
        growth
      }
    };

  } catch (error) {
    console.error('Erro ao calcular comparação de métricas:', error);
    return {
      success: false,
      errors: {
        _form: 'Erro ao calcular comparação. Tente novamente.'
      }
    };
  }
}

/**
 * Interface para dados de próximas cobranças
 */
export interface NextCharge {
  id: string;
  student_name: string;
  plan_title: string;
  amount: number;
  due_date: string;
  status: string;
  payment_method: string;
  days_until_due: number;
}

/**
 * Busca as próximas cobranças de forma otimizada usando função RPC
 * Por padrão retorna as próximas 3 cobranças
 */
export async function getNextCharges(limit: number = 3): Promise<ActionResult<{
  charges: NextCharge[];
  total: number;
}>> {
  try {
    const user = await getCurrentUser();
    if (!user) {
      return {
        success: false,
        errors: { auth: 'Usuário não autenticado' }
      };
    }

    const tenantId = (user.app_metadata as any)?.tenant_id;
    if (!tenantId) {
      return {
        success: false,
        errors: { tenant: 'Tenant não identificado' }
      };
    }

    const supabase = await createTenantServerClient();

    // Usar função RPC otimizada para buscar próximas cobranças
    const { data: charges, error } = await supabase.rpc('get_next_charges' as any, {
      p_tenant_id: tenantId,
      p_limit: 100 // Buscar mais registros para filtrar no código
    });

    if (error) {
      console.error('Erro ao buscar próximas cobranças:', error);

      // Fallback: usar query tradicional se RPC falhar
      return await getNextChargesFallback(supabase, tenantId, limit);
    }

    // Obter data atual no timezone do Brasil de forma mais precisa
    const nowBrazil = new Date().toLocaleDateString('en-CA', {
      timeZone: 'America/Sao_Paulo'
    });
    const todayBrazil = new Date(nowBrazil + 'T00:00:00'); // Garantir que é tratado como data local

    const formattedCharges: NextCharge[] = (charges as any[])?.map((charge: any) => {
      const dueDate = new Date(charge.due_date + 'T00:00:00'); // Garantir que é tratado como data local
      const diffTime = dueDate.getTime() - todayBrazil.getTime();
      const diffDays = Math.round(diffTime / (1000 * 60 * 60 * 24)); // Usar Math.round para cálculo mais preciso

      return {
        id: charge.payment_id,
        student_name: charge.student_name,
        plan_title: charge.plan_title,
        amount: parseFloat(String(charge.amount || '0')),
        due_date: charge.due_date,
        status: charge.status,
        payment_method: charge.payment_method,
        days_until_due: diffDays
      };
    }) || [];

    // Filtrar e ordenar no código para incluir vencidos e próximos
    const filteredCharges = formattedCharges
      .filter(charge => charge.days_until_due >= -30) // Incluir vencidos até 30 dias
      .sort((a, b) => {
        // Priorizar vencidos primeiro, depois por data
        if (a.days_until_due < 0 && b.days_until_due >= 0) return -1;
        if (a.days_until_due >= 0 && b.days_until_due < 0) return 1;
        return a.days_until_due - b.days_until_due;
      })
      .slice(0, limit);

    return {
      success: true,
      data: {
        charges: filteredCharges,
        total: filteredCharges.length
      }
    };

  } catch (error) {
    console.error('Erro ao buscar próximas cobranças:', error);
    return {
      success: false,
      errors: { _form: 'Erro interno ao buscar próximas cobranças' }
    };
  }
}

/**
 * Função de fallback para buscar próximas cobranças usando query tradicional
 */
async function getNextChargesFallback(
  supabase: any,
  tenantId: string,
  limit: number
): Promise<ActionResult<{ charges: NextCharge[]; total: number }>> {
  try {
    const { data: charges, error } = await supabase
      .from('payments')
      .select(`
        id,
        amount,
        due_date,
        status,
        payment_method,
        students!inner(
          user_id,
          users!inner(
            first_name,
            last_name
          )
        ),
        memberships!inner(
          plans!inner(
            title
          )
        )
      `)
      .eq('tenant_id', tenantId)
      .in('status', ['pending', 'awaiting_confirmation'])
      .not('membership_id', 'is', null)
      .order('due_date', { ascending: true })
      .order('created_at', { ascending: true });

    if (error) throw error;

    // Obter data atual no timezone do Brasil de forma mais precisa
    const nowBrazil = new Date().toLocaleDateString('en-CA', {
      timeZone: 'America/Sao_Paulo'
    });
    const todayBrazil = new Date(nowBrazil + 'T00:00:00'); // Garantir que é tratado como data local

    const formattedCharges: NextCharge[] = charges?.map((charge: any) => {
      const dueDate = new Date(charge.due_date + 'T00:00:00'); // Garantir que é tratado como data local
      const diffTime = dueDate.getTime() - todayBrazil.getTime();
      const diffDays = Math.round(diffTime / (1000 * 60 * 60 * 24)); // Usar Math.round para cálculo mais preciso

      return {
        id: charge.id,
        student_name: `${charge.students.users.first_name} ${charge.students.users.last_name || ''}`.trim(),
        plan_title: charge.memberships.plans.title,
        amount: parseFloat(String(charge.amount || '0')),
        due_date: charge.due_date,
        status: charge.status,
        payment_method: charge.payment_method || 'Não definido',
        days_until_due: diffDays
      };
    }) || [];

    // Filtrar e ordenar no código para incluir vencidos e próximos
    const filteredCharges = formattedCharges
      .filter(charge => charge.days_until_due >= -30) // Incluir vencidos até 30 dias
      .sort((a, b) => {
        // Priorizar vencidos primeiro, depois por data
        if (a.days_until_due < 0 && b.days_until_due >= 0) return -1;
        if (a.days_until_due >= 0 && b.days_until_due < 0) return 1;
        return a.days_until_due - b.days_until_due;
      })
      .slice(0, limit);

    return {
      success: true,
      data: {
        charges: filteredCharges,
        total: filteredCharges.length
      }
    };

  } catch (error) {
    console.error('Erro no fallback de próximas cobranças:', error);
    return {
      success: false,
      errors: { _form: 'Erro ao buscar próximas cobranças' }
    };
  }
}

/**
 * Busca dados de próximas cobranças para o dashboard (função legada)
 * @deprecated Use getNextCharges() para melhor performance
 */
export async function getUpcomingCharges(limit: number = 10): Promise<ActionResult<{
  charges: Array<{
    id: string;
    student_name: string;
    amount: number;
    due_date: string;
    status: string;
    plan_title: string;
  }>;
  total: number;
}>> {
  const result = await getNextCharges(limit);

  if (!result.success) {
    return result as any;
  }

  // Converter formato para compatibilidade com código existente
  const legacyCharges = result.data!.charges.map(charge => ({
    id: charge.id,
    student_name: charge.student_name,
    amount: charge.amount,
    due_date: charge.due_date,
    status: charge.status,
    plan_title: charge.plan_title
  }));

  return {
    success: true,
    data: {
      charges: legacyCharges,
      total: result.data!.total
    }
  };
}

/**
 * Interface para dados de status das recorrências
 */
export interface RecurrenceStatusData {
  ativas: number;
  pausadas: number;
  canceladas: number;
  expiradas: number;
  total: number;
}

/**
 * Busca estatísticas de status das recorrências (memberships)
 * Retorna dados formatados para o componente StatusRecorrencias
 */
export async function getRecurrenceStatusStats(): Promise<ActionResult<RecurrenceStatusData>> {
  try {
    const user = await getCurrentUser();
    if (!user) {
      return {
        success: false,
        errors: { auth: 'Usuário não autenticado' }
      };
    }

    const tenantId = (user.app_metadata as any)?.tenant_id;
    if (!tenantId) {
      return {
        success: false,
        errors: { tenant: 'Tenant não identificado' }
      };
    }

    const supabase = await createTenantServerClient();

    // Buscar estatísticas de memberships por status
    const { data: stats, error } = await supabase
      .from('memberships')
      .select('status')
      .eq('tenant_id', tenantId);

    if (error) {
      console.error('Erro ao buscar estatísticas de recorrências:', error);
      return {
        success: false,
        errors: { _form: 'Erro ao buscar estatísticas de recorrências' }
      };
    }

    // Contar memberships por status
    const statusCounts = stats?.reduce((acc: Record<string, number>, membership: { status: string }) => {
      acc[membership.status] = (acc[membership.status] || 0) + 1;
      return acc;
    }, {} as Record<string, number>) || {};

    const total = stats?.length || 0;

    return {
      success: true,
      data: {
        ativas: statusCounts.active || 0,
        pausadas: statusCounts.paused || 0,
        canceladas: statusCounts.canceled || 0,
        expiradas: statusCounts.expired || 0,
        total
      }
    };

  } catch (error) {
    console.error('Erro ao buscar estatísticas de recorrências:', error);
    return {
      success: false,
      errors: { _form: 'Erro interno ao buscar estatísticas' }
    };
  }
}
