"use server";

/**
 * Server Actions para gráficos de receita
 */

import { 
  DashboardActionResult, 
  MonthlyRevenueData, 
  ModalityRevenueData 
} from '../../types/dashboard-types';
import { ensureNumber, formatCurrency } from '../../utils/dashboard-utils';
import { getAuthenticatedClient } from '../shared/auth-utils';

/**
 * Buscar dados para gráfico de receita mensal
 */
export async function getMonthlyRevenueChart(
  year: number = new Date().getFullYear()
): Promise<DashboardActionResult<MonthlyRevenueData[]>> {
  try {
    const { supabase, tenantId } = await getAuthenticatedClient();

    const startOfYear = new Date(year, 0, 1);
    const endOfYear = new Date(year, 11, 31, 23, 59, 59);

    const { data: payments, error } = await supabase
      .from('payments')
      .select('amount, status, paid_at, created_at')
      .eq('tenant_id', tenantId)
      .gte('paid_at', startOfYear.toISOString())
      .lte('paid_at', endOfYear.toISOString())
      .eq('status', 'paid');

    if (error) {
      console.error('Error fetching monthly revenue chart data:', error);
      return { success: false, error: 'Erro ao buscar dados de receita mensal' };
    }

    // Processar dados por mês
    const monthlyData: { [key: string]: number } = {};
    const months = [
      'Jan', 'Fev', 'Mar', 'Abr', 'Mai', 'Jun',
      'Jul', 'Ago', 'Set', 'Out', 'Nov', 'Dez'
    ];

    // Inicializar todos os meses com 0
    months.forEach((month, index) => {
      monthlyData[month] = 0;
    });

    // Somar receitas por mês
    (payments || []).forEach((payment: any) => {
      const paidDate = new Date(payment.paid_at);
      const monthIndex = paidDate.getMonth();
      const monthName = months[monthIndex];
      monthlyData[monthName] += ensureNumber(payment.amount);
    });

    // Converter para array
    const chartData: MonthlyRevenueData[] = months.map(month => ({
      month,
      revenue: monthlyData[month],
      formattedRevenue: formatCurrency(monthlyData[month])
    }));

    return { success: true, data: chartData };
  } catch (error) {
    console.error('Error in getMonthlyRevenueChart:', error);
    return { success: false, error: 'Erro interno do servidor' };
  }
}

/**
 * Buscar dados para gráfico de receita por modalidade
 */
export async function getRevenueByModalityChart(): Promise<DashboardActionResult<ModalityRevenueData[]>> {
  try {
    const { supabase, tenantId } = await getAuthenticatedClient();

    // Buscar pagamentos com informações dos planos
    const { data: paymentsData, error } = await supabase
      .from('payments')
      .select(`
        amount,
        status,
        memberships!inner(
          plans!inner(
            title,
            access_config
          )
        )
      `)
      .eq('tenant_id', tenantId)
      .eq('status', 'paid');

    if (error) {
      console.error('Error fetching revenue by modality:', error);
      return { success: false, error: 'Erro ao buscar receita por modalidade' };
    }

    // Buscar todas as modalidades para fazer o mapeamento
    const { data: modalities, error: modalitiesError } = await supabase
      .from('modalities')
      .select('id, name, slug')
      .eq('tenant_id', tenantId)
      .eq('is_active', true);

    if (modalitiesError) {
      console.error('Error fetching modalities:', modalitiesError);
      return { success: false, error: 'Erro ao buscar modalidades' };
    }

    // Criar mapa de modalidades por ID
    const modalityMap = new Map(
      (modalities || []).map(modality => [modality.id, modality])
    );

    // Processar dados por modalidade
    const modalityRevenue: { [key: string]: { amount: number; count: number } } = {};

    (paymentsData || []).forEach((payment: any) => {
      const plan = payment.memberships?.plans;
      if (plan?.access_config?.modalities) {
        const modalityIds = plan.access_config.modalities;

        // Para cada modalidade no plano, dividir o valor do pagamento
        modalityIds.forEach((modalityId: string) => {
          const modality = modalityMap.get(modalityId);
          if (modality) {
            const modalityName = modality.name;
            if (!modalityRevenue[modalityName]) {
              modalityRevenue[modalityName] = { amount: 0, count: 0 };
            }
            // Dividir o valor do pagamento pelo número de modalidades no plano
            const amountPerModality = ensureNumber(payment.amount) / modalityIds.length;
            modalityRevenue[modalityName].amount += amountPerModality;
            modalityRevenue[modalityName].count += 1;
          }
        });
      }
    });

    // Converter para array e calcular percentuais
    const totalRevenue = Object.values(modalityRevenue).reduce((sum, item) => sum + item.amount, 0);

    const chartData: ModalityRevenueData[] = Object.entries(modalityRevenue)
      .map(([modality, data]) => ({
        modality,
        revenue: data.amount,
        count: data.count,
        percentage: totalRevenue > 0 ? Math.round((data.amount / totalRevenue) * 100 * 100) / 100 : 0,
        formattedRevenue: formatCurrency(data.amount),
        color: generateModalityColor(modality)
      }))
      .sort((a, b) => b.revenue - a.revenue);

    return { success: true, data: chartData };
  } catch (error) {
    console.error('Error in getRevenueByModalityChart:', error);
    return { success: false, error: 'Erro interno do servidor' };
  }
}

/**
 * Gerar cor para modalidade
 */
function generateModalityColor(modality: string): string {
  const colors = [
    '#3b82f6', // blue
    '#10b981', // emerald
    '#f59e0b', // amber
    '#ef4444', // red
    '#8b5cf6', // violet
    '#06b6d4', // cyan
    '#84cc16', // lime
    '#f97316', // orange
    '#ec4899', // pink
    '#6366f1'  // indigo
  ];

  // Usar hash simples para consistência
  let hash = 0;
  for (let i = 0; i < modality.length; i++) {
    hash = modality.charCodeAt(i) + ((hash << 5) - hash);
  }

  return colors[Math.abs(hash) % colors.length];
}
