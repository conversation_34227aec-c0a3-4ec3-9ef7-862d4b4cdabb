"use server";

/**
 * Server Actions para métricas de despesas
 */

import { DashboardActionResult, ExpenseMetrics, DateRange } from '../../types/dashboard-types';
import { ensureNumber } from '../../utils/dashboard-utils';
import { getAuthenticatedClient, formatDateForSQL } from '../shared/auth-utils';

/**
 * Busca métricas de despesas para um período
 */
export async function getExpenseMetrics(
  currentRange: DateRange,
  previousRange: DateRange
): Promise<DashboardActionResult<ExpenseMetrics>> {
  try {
    const { supabase, tenantId } = await getAuthenticatedClient();

    // Query para despesas do período atual
    const { data: currentData, error: currentError } = await supabase
      .from('expenses')
      .select(`
        amount, 
        status, 
        due_date, 
        paid_at,
        expense_categories (
          id,
          name,
          color
        )
      `)
      .eq('tenant_id', tenantId)
      .gte('due_date', formatDateForSQL(currentRange.startDate))
      .lte('due_date', formatDateForSQL(currentRange.endDate));

    if (currentError) {
      return {
        success: false,
        error: `Erro ao buscar despesas: ${currentError.message}`
      };
    }

    // Calcular métricas do período atual
    const expenses = currentData || [];
    const totalExpenses = expenses
      .filter(e => e.status === 'paid')
      .reduce((sum, e) => sum + ensureNumber(e.amount), 0);

    const paidExpenses = expenses.filter(e => e.status === 'paid').length;
    const pendingExpenses = expenses.filter(e => e.status === 'pending').length;
    const overdueExpenses = expenses.filter(e => e.status === 'overdue').length;
    
    const averageExpense = paidExpenses > 0 ? totalExpenses / paidExpenses : 0;

    // Despesas por categoria
    const categoryMap = new Map();
    expenses
      .filter(e => e.status === 'paid')
      .forEach(expense => {
        const category = expense.expense_categories;
        const categoryId = category?.id || 'uncategorized';
        const categoryName = category?.name || 'Sem categoria';
        const categoryColor = category?.color || '#6B7280';
        
        if (!categoryMap.has(categoryId)) {
          categoryMap.set(categoryId, {
            categoryId,
            categoryName,
            categoryColor,
            amount: 0,
            count: 0
          });
        }
        
        const categoryData = categoryMap.get(categoryId);
        categoryData.amount += ensureNumber(expense.amount);
        categoryData.count += 1;
      });

    const expensesByCategory = Array.from(categoryMap.values()).map(category => ({
      ...category,
      percentage: totalExpenses > 0 ? (category.amount / totalExpenses) * 100 : 0
    }));

    const metrics: ExpenseMetrics = {
      totalExpenses,
      paidExpenses,
      pendingExpenses,
      overdueExpenses,
      averageExpense,
      expenseGrowth: 0, // TODO: Implementar comparação com período anterior
      expensesByCategory
    };

    return {
      success: true,
      data: metrics,
      message: 'Métricas de despesas obtidas com sucesso'
    };

  } catch (error) {
    return {
      success: false,
      error: `Erro interno: ${error instanceof Error ? error.message : 'Erro desconhecido'}`
    };
  }
}
