/**
 * Server Actions para o Dashboard Financeiro
 * 
 * Este arquivo foi reorganizado em múltiplos arquivos menores para melhor manutenibilidade:
 * 
 * - core/dashboard-data-actions.ts: Consolidação de dados e KPIs principais
 * - metrics/: Actions específicas para cada tipo de métrica
 *   - revenue-actions.ts: Métricas de receitas
 *   - expense-actions.ts: Métricas de despesas
 *   - cashflow-actions.ts: Métricas de fluxo de caixa
 *   - student-actions.ts: Métricas de alunos
 * - charts/: Actions para dados de gráficos
 *   - revenue-chart-actions.ts: Gráficos de receita
 *   - payment-chart-actions.ts: Gráficos de pagamentos
 * - shared/: Utilitários compartilhados
 *   - auth-utils.ts: Funções de autenticação e formatação
 */

// Exports principais para consolidação de dados
export * from './core/dashboard-data-actions';

// Exports de métricas específicas
export * from './metrics/revenue-actions';
export * from './metrics/expense-actions';
export * from './metrics/cashflow-actions';
export * from './metrics/student-actions';

// Exports de gráficos
export * from './charts/revenue-chart-actions';
export * from './charts/payment-chart-actions';

// Exports de utilitários (se necessário para uso externo)
export * from './shared/auth-utils';
