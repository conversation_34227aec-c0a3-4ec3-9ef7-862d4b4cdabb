# Dashboard Financeiro - Fase 1

## 📊 Visão Geral

Este é o dashboard financeiro completo da academia, implementado em fases. A **Fase 1** estabelece a estrutura base com KPIs principais, filtros de período e layout responsivo.

## 🎯 Objetivos da Fase 1

- ✅ Criar componente FinancialDashboard principal
- ✅ Implementar layout responsivo
- ✅ Configurar sistema de filtros
- ✅ Implementar loading states
- ✅ Integrar com dados reais do Supabase
- ✅ Exibir KPIs principais com comparações

## 🏗️ Estrutura de Arquivos

```
src/app/(dashboard)/financeiro/dashboard/
├── page.tsx                          # Página principal do dashboard
├── index.ts                          # Exports centralizados
├── README.md                         # Esta documentação
├── components/
│   ├── FinancialDashboard.tsx        # Componente principal
│   ├── DashboardFilters.tsx          # Filtros de período
│   ├── DashboardLayout.tsx           # Layout responsivo
│   ├── LoadingStates.tsx             # Estados de carregamento
│   └── index.ts                      # Exports dos componentes
├── actions/
│   └── dashboard-actions.ts          # Server actions para dados
├── types/
│   └── dashboard-types.ts            # Tipos TypeScript
└── utils/
    └── dashboard-utils.ts            # Utilitários e formatação
```

## 🚀 Como Usar

### Acesso ao Dashboard

1. Navegue para `/financeiro/dashboard`
2. Ou acesse através da navegação: **Financeiro > Dashboard**
3. Ou clique no card na página principal do financeiro

### Filtros Disponíveis

- **Mês Atual**: Dados do mês corrente
- **Trimestre Atual**: Dados dos últimos 3 meses
- **Semestre Atual**: Dados dos últimos 6 meses
- **Ano Atual**: Dados do ano corrente
- **Período Personalizado**: Selecione datas específicas

### KPIs Exibidos

1. **Receita Total**: Soma de todos os pagamentos confirmados
2. **Despesas Totais**: Soma de todas as despesas pagas
3. **Lucro Líquido**: Receita - Despesas
4. **Alunos Ativos**: Número de alunos com matrículas ativas
5. **Receita por Aluno**: Receita total / número de alunos ativos
6. **Margem de Lucro**: (Lucro / Receita) × 100

## 🔧 Componentes Principais

### FinancialDashboard

Componente principal que orquestra todo o dashboard:

```tsx
import { FinancialDashboard } from '@/app/(dashboard)/financeiro/dashboard';

<FinancialDashboard 
  refreshInterval={5 * 60 * 1000} // 5 minutos
/>
```

### DashboardFilters

Sistema de filtros de período:

```tsx
import { DashboardFilters } from '@/app/(dashboard)/financeiro/dashboard';

<DashboardFilters
  filters={filters}
  onFiltersChange={handleFiltersChange}
  loading={loading}
/>
```

### DashboardLayout

Layout responsivo com seções organizadas:

```tsx
import { DashboardLayout } from '@/app/(dashboard)/financeiro/dashboard';

<DashboardLayout
  kpis={kpis}
  data={data}
  loading={loading}
  error={error}
/>
```

## 📊 Server Actions

### getDashboardKPIs

Busca KPIs principais de forma otimizada:

```tsx
import { getDashboardKPIs } from '@/app/(dashboard)/financeiro/dashboard';

const result = await getDashboardKPIs(dateRange);
```

### getDashboardData

Busca todos os dados do dashboard:

```tsx
import { getDashboardData } from '@/app/(dashboard)/financeiro/dashboard';

const result = await getDashboardData(currentRange, previousRange);
```

## 🎨 Layout Responsivo

### Desktop (≥1024px)
- Grid 6 colunas para KPIs
- Layout 3 colunas para seções principais
- Gráficos grandes e detalhados

### Tablet (768px - 1023px)
- Grid 3 colunas para KPIs
- Layout 2 colunas para seções
- Gráficos médios

### Mobile (<768px)
- Grid 2 colunas para KPIs
- Layout 1 coluna empilhado
- Gráficos simplificados

## 🔄 Estados de Loading

O dashboard implementa loading states granulares:

- **KPIs**: Skeleton para cards de métricas
- **Gráficos**: Placeholder para visualizações
- **Tabelas**: Skeleton para listas
- **Dashboard Completo**: Loading de toda a interface

## 📈 Dados e Integração

### Fonte de Dados

- **Pagamentos**: Tabela `payments` no Supabase
- **Despesas**: Tabela `expenses` no Supabase
- **Alunos**: Tabela `memberships` no Supabase
- **Categorias**: Tabela `expense_categories` no Supabase

### Cálculos

- **Receita Total**: `SUM(amount) WHERE status = 'paid'`
- **Despesas Totais**: `SUM(amount) WHERE status = 'paid'`
- **Alunos Ativos**: `COUNT(DISTINCT student_id) WHERE status = 'active'`

## 🚧 Próximas Fases

### Fase 2: Métricas e KPIs Avançados
- [ ] Comparações com período anterior
- [ ] Indicadores de tendência
- [ ] Alertas e notificações
- [ ] Métricas de crescimento

### Fase 3: Gráficos de Receita
- [ ] Gráfico de receita mensal
- [ ] Gráfico de receita por modalidade
- [ ] Gráfico de status de pagamentos
- [ ] Gráfico de métodos de pagamento

### Fase 4: Gráficos de Despesas
- [ ] Gráfico de despesas por categoria
- [ ] Gráfico de evolução de despesas
- [ ] Gráfico de despesas fixas vs variáveis
- [ ] Lista de despesas pendentes

### Fase 5: Análise de Fluxo de Caixa
- [ ] Gráfico de fluxo mensal
- [ ] Projeções de fluxo
- [ ] Indicadores de saldo
- [ ] Análise de sazonalidade

### Fase 6: Funcionalidades Avançadas
- [ ] Drill-down em gráficos
- [ ] Exportação de relatórios
- [ ] Filtros avançados
- [ ] Comparações históricas

## 🔧 Desenvolvimento

### Executar em Desenvolvimento

```bash
npm run dev
```

### Estrutura de Tipos

Todos os tipos estão centralizados em `types/dashboard-types.ts`:

```tsx
import type { 
  DashboardData,
  FinancialKPIs,
  DashboardFilters 
} from '@/app/(dashboard)/financeiro/dashboard';
```

### Utilitários

Funções de formatação e cálculo em `utils/dashboard-utils.ts`:

```tsx
import { 
  formatCurrency,
  formatPercentage,
  calculateGrowth 
} from '@/app/(dashboard)/financeiro/dashboard';
```

## 📝 Notas Técnicas

- **Performance**: Queries otimizadas com índices no Supabase
- **Cache**: Implementação futura de cache para melhor performance
- **Real-time**: Preparado para WebSockets nas próximas fases
- **Responsividade**: Mobile-first design
- **Acessibilidade**: Componentes seguem padrões WCAG
- **TypeScript**: Tipagem completa em toda a aplicação

## 🐛 Troubleshooting

### Dados não carregam
1. Verifique a conexão com Supabase
2. Confirme as permissões RLS
3. Verifique os logs do console

### Performance lenta
1. Verifique o tamanho do período selecionado
2. Considere implementar paginação
3. Otimize queries no Supabase

### Erros de tipo
1. Verifique as importações dos tipos
2. Confirme a estrutura dos dados retornados
3. Valide as interfaces TypeScript
