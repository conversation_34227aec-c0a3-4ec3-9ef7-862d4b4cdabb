/**
 * Exports dos Componentes do Dashboard Financeiro
 * Centraliza todas as exportações para facilitar importação
 */

// Componente principal
export { FinancialDashboard } from './FinancialDashboard';
export { FinancialDashboardTabs } from './FinancialDashboardTabs';

// Componentes de layout e estrutura
export { DashboardLayout } from './DashboardLayout';
export { DashboardFilters } from './DashboardFilters';

// Seções específicas - Fases 3, 4 e 5
export { RevenueSection } from './RevenueSection';
export { ExpenseSection } from './ExpenseSection';
export { CashFlowSection } from './CashFlowSection';

// Estados de loading
export { LoadingStates } from './LoadingStates';

// Fase 2 - Componentes Aprimorados
export { EnhancedKPICard } from './EnhancedKPICard';
export { ComparisonMetrics } from './ComparisonMetrics';
export { TrendIndicator, PerformanceIndicator, Sparkline, StatusBadge } from './TrendIndicator';
export { TabNavigation } from './TabNavigation';

// Gráficos
export * from './charts';

// Re-exports dos tipos para conveniência
export type {
  FinancialDashboardProps,
  DashboardFiltersProps,
  DashboardLayoutProps,
  LoadingStates as LoadingStatesType
} from '../types/dashboard-types';
