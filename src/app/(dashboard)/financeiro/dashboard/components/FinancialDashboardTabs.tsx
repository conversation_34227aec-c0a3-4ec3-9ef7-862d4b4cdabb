"use client";

/**
 * Componente de Abas do Dashboard Financeiro
 * Organiza o dashboard em seções navegáveis por abas
 */

import { useState, useEffect, useCallback } from 'react';
import { Tabs, Ta<PERSON>List, TabsTrigger, TabsContent } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  BarChart3,
  DollarSign,
  TrendingDown,
  TrendingUp,
  PieChart,
  Users,
  AlertCircle
} from 'lucide-react';
import { cn } from '@/lib/utils';

import { RevenueSection } from './RevenueSection';
import { ExpenseSection } from './ExpenseSection';
import { CashFlowSection } from './CashFlowSection';
import { DashboardLayout } from './DashboardLayout';
import { DashboardFilters } from './DashboardFilters';
import { LoadingStates } from './LoadingStates';

// ============================================================================
// CONFIGURAÇÃO DAS ABAS
// ============================================================================

interface TabConfig {
  id: string;
  label: string;
  shortLabel: string;
  icon: React.ComponentType<{ className?: string }>;
  description: string;
}

const dashboardTabs: TabConfig[] = [
  {
    id: 'overview',
    label: 'Visão Geral',
    shortLabel: 'Geral',
    icon: BarChart3,
    description: 'KPIs principais e resumo executivo'
  },
  {
    id: 'revenue',
    label: 'Receitas',
    shortLabel: 'Receitas',
    icon: DollarSign,
    description: 'Análise de receitas e pagamentos'
  },
  {
    id: 'expenses',
    label: 'Despesas',
    shortLabel: 'Despesas',
    icon: TrendingDown,
    description: 'Controle de gastos e categorias'
  },
  {
    id: 'cashflow',
    label: 'Fluxo de Caixa',
    shortLabel: 'Fluxo',
    icon: TrendingUp,
    description: 'Entradas, saídas e projeções'
  },
  {
    id: 'students',
    label: 'Alunos',
    shortLabel: 'Alunos',
    icon: Users,
    description: 'Métricas de retenção e crescimento'
  }
];

// ============================================================================
// COMPONENTE PRINCIPAL
// ============================================================================

import {
  FinancialDashboardProps,
  DashboardFilters as DashboardFiltersType,
  DashboardData,
  LoadingStates as LoadingStatesType
} from '../types/dashboard-types';
import {
  getDateRangeForPeriod,
  getPreviousPeriodRange,
  debounce
} from '../utils/dashboard-utils';
import { fetchDashboardData, fetchDashboardKPIs } from '../api/dashboard-client-api';

interface FinancialDashboardTabsProps {
  initialFilters?: DashboardFiltersType;
  refreshInterval?: number;
}

const DEFAULT_REFRESH_INTERVAL = 5 * 60 * 1000; // 5 minutos
const DEFAULT_PERIOD = 'month';

export const FinancialDashboardTabs: React.FC<FinancialDashboardTabsProps> = ({
  initialFilters,
  refreshInterval = DEFAULT_REFRESH_INTERVAL
}) => {
  const [activeTab, setActiveTab] = useState('overview');

  // Estado do dashboard
  const [filters, setFilters] = useState<DashboardFiltersType>(() => {
    const defaultRange = getDateRangeForPeriod(DEFAULT_PERIOD);
    return initialFilters || {
      dateRange: defaultRange,
      refreshInterval
    };
  });

  const [data, setData] = useState<DashboardData | null>(null);
  const [loadingStates, setLoadingStates] = useState<LoadingStatesType>({
    kpis: false,
    revenue: false,
    expenses: false,
    cashFlow: false,
    students: false,
    overall: false
  });
  const [error, setError] = useState<string | null>(null);

  // Funções de carregamento de dados
  const loadDashboardData = useCallback(async (showLoading = true) => {
    try {
      if (showLoading) {
        setLoadingStates(prev => ({ ...prev, overall: true }));
      }
      setError(null);

      const currentRange = filters.dateRange;

      // Garantir que as datas são objetos Date válidos
      const validDateRange = {
        ...currentRange,
        startDate: currentRange.startDate instanceof Date ?
                   currentRange.startDate :
                   new Date(currentRange.startDate),
        endDate: currentRange.endDate instanceof Date ?
                 currentRange.endDate :
                 new Date(currentRange.endDate)
      };

      const previousRange = getPreviousPeriodRange(validDateRange);

      // Para MVP da Fase 1, carregar apenas KPIs de forma otimizada
      const result = await fetchDashboardKPIs(validDateRange, previousRange);

      if (!result.success) {
        throw new Error(result.error || 'Erro ao carregar dados do dashboard');
      }

      // Criar dados mock para as outras seções (serão implementadas nas próximas fases)
      const dashboardData: DashboardData = {
        kpis: result.data!,
        revenueMetrics: {
          totalRevenue: result.data!.totalRevenue.current,
          paidPayments: 0,
          pendingPayments: 0,
          overduePayments: 0,
          averagePayment: 0,
          monthlyRecurringRevenue: 0,
          revenueGrowth: result.data!.totalRevenue.growth
        },
        expenseMetrics: {
          totalExpenses: result.data!.totalExpenses.current,
          paidExpenses: 0,
          pendingExpenses: 0,
          overdueExpenses: 0,
          averageExpense: 0,
          expenseGrowth: result.data!.totalExpenses.growth,
          expensesByCategory: []
        },
        cashFlowMetrics: {
          currentBalance: result.data!.netProfit.current,
          totalInflows: result.data!.totalRevenue.current,
          totalOutflows: result.data!.totalExpenses.current,
          netCashFlow: result.data!.netProfit.current,
          projectedBalance: result.data!.netProfit.current,
          cashFlowTrend: result.data!.netProfit.trend
        },
        studentMetrics: {
          totalStudents: result.data!.activeStudents.current,
          activeStudents: result.data!.activeStudents.current,
          newStudents: 0,
          churnedStudents: 0,
          retentionRate: 100,
          churnRate: 0,
          averageLifetimeValue: result.data!.averageRevenuePerUser.current
        },
        lastUpdated: new Date()
      };

      setData(dashboardData);

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erro desconhecido';
      setError(errorMessage);
      console.error('Erro ao carregar dashboard:', err);
    } finally {
      if (showLoading) {
        setLoadingStates(prev => ({ ...prev, overall: false }));
      }
    }
  }, [filters.dateRange]);

  // Debounce para evitar muitas chamadas durante mudanças de filtro
  const debouncedLoadData = useCallback(
    debounce(() => loadDashboardData(true), 500),
    [loadDashboardData]
  );

  // Handlers
  const handleFiltersChange = useCallback((newFilters: DashboardFiltersType) => {
    // Garantir que as datas são objetos Date válidos antes de atualizar o estado
    const validatedFilters = {
      ...newFilters,
      dateRange: {
        ...newFilters.dateRange,
        startDate: newFilters.dateRange.startDate instanceof Date ?
                   newFilters.dateRange.startDate :
                   new Date(newFilters.dateRange.startDate),
        endDate: newFilters.dateRange.endDate instanceof Date ?
                 newFilters.dateRange.endDate :
                 new Date(newFilters.dateRange.endDate)
      }
    };

    setFilters(validatedFilters);
    // Carregar dados automaticamente quando filtros mudarem
    debouncedLoadData();
  }, [debouncedLoadData]);

  const handleRefresh = useCallback(() => {
    loadDashboardData(true);
  }, [loadDashboardData]);

  // Efeitos
  useEffect(() => {
    loadDashboardData(true);
  }, []); // Executar apenas uma vez na montagem

  // Auto-refresh se configurado
  useEffect(() => {
    if (!filters.refreshInterval) return;

    const interval = setInterval(() => {
      loadDashboardData(false); // Refresh silencioso
    }, filters.refreshInterval);

    return () => clearInterval(interval);
  }, [filters.refreshInterval, loadDashboardData]);

  const isLoading = loadingStates.overall;
  const hasData = data !== null;

  return (
    <div className="space-y-6">
      {/* Filtros */}
      <DashboardFilters
        filters={filters}
        onFiltersChange={handleFiltersChange}
        loading={isLoading}
      />

      {/* Erro Global */}
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            {error}
          </AlertDescription>
        </Alert>
      )}

      {/* Navegação Rápida */}
      {/* <TabNavigation
        activeTab={activeTab}
        onTabChange={setActiveTab}
      /> */}

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        {/* Lista de Abas */}
        <TabsList className="grid w-full grid-cols-5 bg-transparent border-b border-border/20 h-auto p-0 rounded-none">
          {dashboardTabs.map((tab) => {
            const IconComponent = tab.icon;
            return (
              <TabsTrigger
                key={tab.id}
                value={tab.id}
                className={cn(
                  "flex items-center justify-center gap-2 py-3 px-4 text-sm font-normal transition-colors",
                  "bg-transparent border-b-2 border-transparent rounded-none",
                  "data-[state=active]:border-tenant-primary data-[state=active]:text-tenant-primary",
                  "hover:text-tenant-primary hover:bg-transparent",
                  "text-muted-foreground"
                )}
              >
                <IconComponent className="h-4 w-4" />
                <span className="hidden sm:inline">{tab.label}</span>
                <span className="sm:hidden">{tab.shortLabel}</span>
              </TabsTrigger>
            );
          })}
        </TabsList>

        {/* Conteúdo das Abas */}
        <div className="mt-8">
          {/* Aba: Visão Geral */}
          <TabsContent value="overview" className="space-y-6 mt-0">
            {isLoading && !hasData ? (
              <LoadingStates.Dashboard />
            ) : hasData ? (
              <DashboardLayout
                kpis={data.kpis}
                data={data}
                loading={isLoading}
                error={error}
              />
            ) : (
              <div className="flex items-center justify-center h-64">
                <div className="text-center">
                  <div className="text-muted-foreground/40 mb-4">
                    <AlertCircle className="h-12 w-12 mx-auto" />
                  </div>
                  <p className="text-muted-foreground">
                    Nenhum dado disponível para o período selecionado
                  </p>
                </div>
              </div>
            )}
          </TabsContent>

          {/* Aba: Receitas */}
          <TabsContent value="revenue" className="space-y-6 mt-0">
            {hasData ? (
              <RevenueSection
                kpis={data.kpis}
                data={data}
                loading={isLoading}
                error={error}
              />
            ) : isLoading ? (
              <LoadingStates.Dashboard />
            ) : (
              <PlaceholderSection
                title="Gráficos de Receita"
                description="Nenhum dado disponível para o período selecionado"
                icon={<DollarSign className="h-12 w-12" />}
              />
            )}
          </TabsContent>

          {/* Aba: Despesas */}
          <TabsContent value="expenses" className="space-y-6 mt-0">
            {hasData ? (
              <ExpenseSection
                kpis={data.kpis}
                data={data}
                loading={isLoading}
                error={error}
              />
            ) : isLoading ? (
              <LoadingStates.Dashboard />
            ) : (
              <PlaceholderSection
                title="Gráficos de Despesas"
                description="Nenhum dado disponível para o período selecionado"
                icon={<TrendingDown className="h-12 w-12" />}
              />
            )}
          </TabsContent>

          {/* Aba: Fluxo de Caixa */}
          <TabsContent value="cashflow" className="space-y-6 mt-0">
            {hasData ? (
              <CashFlowSection
                kpis={data.kpis}
                data={data}
                loading={isLoading}
                error={error}
              />
            ) : isLoading ? (
              <LoadingStates.Dashboard />
            ) : (
              <PlaceholderSection
                title="Análise de Fluxo de Caixa"
                description="Nenhum dado disponível para o período selecionado"
                icon={<TrendingUp className="h-12 w-12" />}
              />
            )}
          </TabsContent>

          {/* Aba: Alunos */}
          <TabsContent value="students" className="space-y-6 mt-0">
            <PlaceholderSection
              title="Análise de Alunos"
              description="Métricas de retenção e análise de cohort serão implementadas nas próximas fases"
              icon={<Users className="h-12 w-12" />}
            />
          </TabsContent>
        </div>
      </Tabs>
    </div>
  );
};

// ============================================================================
// COMPONENTE PLACEHOLDER
// ============================================================================

interface PlaceholderSectionProps {
  title: string;
  description: string;
  icon: React.ReactNode;
}

const PlaceholderSection: React.FC<PlaceholderSectionProps> = ({
  title,
  description,
  icon
}) => (
  <div className="flex items-center justify-center h-64">
    <div className="text-center">
      <div className="text-muted-foreground/40 mb-4">
        {icon}
      </div>
      <h3 className="text-lg font-medium text-foreground mb-2">
        {title}
      </h3>
      <p className="text-muted-foreground max-w-md">
        {description}
      </p>
      <p className="text-sm text-muted-foreground/60 mt-2">
        Será implementado nas próximas fases
      </p>
    </div>
  </div>
);
