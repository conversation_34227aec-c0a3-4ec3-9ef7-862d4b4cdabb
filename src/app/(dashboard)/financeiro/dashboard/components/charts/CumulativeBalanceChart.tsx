"use client";

/**
 * Componente de Gráfico de Saldo Acumulado - Fase 5
 * Exibe a evolução do saldo acumulado ao longo do tempo
 */

import { useEffect, useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { AreaChart, Area, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, ReferenceLine } from 'recharts';
import { TrendingUp, TrendingDown, DollarSign, Calendar } from 'lucide-react';
import { cn } from '@/lib/utils';

import { getCumulativeBalanceData } from '../../actions/metrics/cashflow-actions';
import { CumulativeBalanceData, DateRange } from '../../types/dashboard-types';
import { formatCurrency } from '../../utils/dashboard-utils';

// ============================================================================
// TIPOS
// ============================================================================

interface CumulativeBalanceChartProps {
  dateRange: DateRange;
  className?: string;
}

interface ChartTooltipProps {
  active?: boolean;
  payload?: any[];
  label?: string;
}

// ============================================================================
// COMPONENTE DE TOOLTIP CUSTOMIZADO
// ============================================================================

const CustomTooltip: React.FC<ChartTooltipProps> = ({ active, payload, label }) => {
  if (active && payload && payload.length) {
    const balance = payload[0];
    return (
      <div className="bg-white dark:bg-gray-800 p-3 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg">
        <p className="font-medium text-gray-900 dark:text-gray-100 mb-2">
          {label}
        </p>
        <p className={cn(
          "text-sm font-medium",
          balance.value >= 0 
            ? "text-emerald-600 dark:text-emerald-400" 
            : "text-red-600 dark:text-red-400"
        )}>
          <span>Saldo Acumulado: </span>
          {formatCurrency(balance.value)}
        </p>
      </div>
    );
  }
  return null;
};

// ============================================================================
// COMPONENTE PRINCIPAL
// ============================================================================

export const CumulativeBalanceChart: React.FC<CumulativeBalanceChartProps> = ({
  dateRange,
  className
}) => {
  const [data, setData] = useState<CumulativeBalanceData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Carregar dados
  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true);
        setError(null);

        const result = await getCumulativeBalanceData(dateRange);

        if (!result.success) {
          throw new Error(result.error || 'Erro ao carregar dados');
        }

        // Formatar dados para o gráfico
        const chartData = (result.data || []).map(item => ({
          ...item,
          month: new Date(item.month + '-01').toLocaleDateString('pt-BR', { 
            month: 'short', 
            year: '2-digit' 
          })
        }));

        setData(chartData);

      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Erro desconhecido';
        setError(errorMessage);
        console.error('Erro ao carregar gráfico de saldo acumulado:', err);
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [dateRange]);

  // Calcular estatísticas
  const currentBalance = data.length > 0 ? data[data.length - 1].cumulativeBalance : 0;
  const initialBalance = data.length > 0 ? data[0].cumulativeBalance : 0;
  const balanceChange = currentBalance - initialBalance;
  const trend = balanceChange > 0 ? 'up' : balanceChange < 0 ? 'down' : 'stable';
  
  // Encontrar maior e menor saldo
  const maxBalance = data.length > 0 ? Math.max(...data.map(d => d.cumulativeBalance)) : 0;
  const minBalance = data.length > 0 ? Math.min(...data.map(d => d.cumulativeBalance)) : 0;

  return (
    <Card className={cn("", className)}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-lg font-semibold text-gray-900 dark:text-gray-100">
              Saldo Acumulado
            </CardTitle>
            <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
              Evolução do saldo ao longo do tempo
            </p>
          </div>
          <div className="flex items-center space-x-2">
            <DollarSign className="h-4 w-4 text-gray-400" />
          </div>
        </div>

        {/* Resumo do Saldo */}
        <div className="grid grid-cols-3 gap-4 mt-4">
          <div className="text-center">
            <div className="flex items-center justify-center space-x-1">
              <DollarSign className="h-4 w-4 text-blue-500" />
              <span className="text-xs font-medium text-gray-600 dark:text-gray-400">
                Saldo Atual
              </span>
            </div>
            <p className={cn(
              "text-lg font-semibold",
              currentBalance >= 0 
                ? "text-emerald-600 dark:text-emerald-400" 
                : "text-red-600 dark:text-red-400"
            )}>
              {formatCurrency(currentBalance)}
            </p>
          </div>
          <div className="text-center">
            <div className="flex items-center justify-center space-x-1">
              {trend === 'up' ? (
                <TrendingUp className="h-4 w-4 text-emerald-500" />
              ) : trend === 'down' ? (
                <TrendingDown className="h-4 w-4 text-red-500" />
              ) : (
                <Calendar className="h-4 w-4 text-gray-500" />
              )}
              <span className="text-xs font-medium text-gray-600 dark:text-gray-400">
                Variação
              </span>
            </div>
            <p className={cn(
              "text-lg font-semibold",
              balanceChange >= 0 
                ? "text-emerald-600 dark:text-emerald-400" 
                : "text-red-600 dark:text-red-400"
            )}>
              {balanceChange >= 0 ? '+' : ''}{formatCurrency(balanceChange)}
            </p>
          </div>
          <div className="text-center">
            <div className="flex items-center justify-center space-x-1">
              <TrendingUp className="h-4 w-4 text-purple-500" />
              <span className="text-xs font-medium text-gray-600 dark:text-gray-400">
                Maior Saldo
              </span>
            </div>
            <p className="text-lg font-semibold text-purple-600 dark:text-purple-400">
              {formatCurrency(maxBalance)}
            </p>
          </div>
        </div>
      </CardHeader>

      <CardContent>
        {loading ? (
          <div className="h-80 flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        ) : error ? (
          <div className="h-80 flex items-center justify-center">
            <div className="text-center">
              <p className="text-red-600 dark:text-red-400 mb-2">Erro ao carregar dados</p>
              <p className="text-sm text-gray-500">{error}</p>
            </div>
          </div>
        ) : data.length === 0 ? (
          <div className="h-80 flex items-center justify-center">
            <div className="text-center">
              <DollarSign className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-600 dark:text-gray-400">
                Nenhum dado disponível para o período selecionado
              </p>
            </div>
          </div>
        ) : (
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <AreaChart data={data} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
                <defs>
                  <linearGradient id="balanceGradient" x1="0" y1="0" x2="0" y2="1">
                    <stop offset="5%" stopColor="#3b82f6" stopOpacity={0.3}/>
                    <stop offset="95%" stopColor="#3b82f6" stopOpacity={0.05}/>
                  </linearGradient>
                </defs>
                <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
                <XAxis 
                  dataKey="month" 
                  className="text-xs"
                  tick={{ fontSize: 12 }}
                />
                <YAxis 
                  className="text-xs"
                  tick={{ fontSize: 12 }}
                  tickFormatter={(value) => {
                    // Para valores grandes, usar formatação compacta
                    if (Math.abs(value) >= 1000) {
                      return new Intl.NumberFormat('pt-BR', {
                        style: 'currency',
                        currency: 'BRL',
                        notation: 'compact',
                        maximumFractionDigits: 1
                      }).format(value);
                    }
                    return formatCurrency(value);
                  }}
                />
                <Tooltip content={<CustomTooltip />} />
                <ReferenceLine y={0} stroke="#6b7280" strokeDasharray="2 2" />
                <Area 
                  type="monotone"
                  dataKey="cumulativeBalance" 
                  stroke="#3b82f6" 
                  strokeWidth={2}
                  fill="url(#balanceGradient)"
                  dot={{ fill: '#3b82f6', strokeWidth: 2, r: 4 }}
                  activeDot={{ r: 6, stroke: '#3b82f6', strokeWidth: 2 }}
                />
              </AreaChart>
            </ResponsiveContainer>
          </div>
        )}

        {/* Indicadores de Performance */}
        {data.length > 0 && (
          <div className="mt-4 grid grid-cols-2 gap-4">
            <div className="p-3 bg-gray-50 dark:bg-gray-800/50 rounded-lg">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  Menor Saldo
                </span>
                <span className={cn(
                  "text-sm font-semibold",
                  minBalance >= 0 
                    ? "text-emerald-600 dark:text-emerald-400" 
                    : "text-red-600 dark:text-red-400"
                )}>
                  {formatCurrency(minBalance)}
                </span>
              </div>
            </div>
            <div className="p-3 bg-gray-50 dark:bg-gray-800/50 rounded-lg">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  Amplitude
                </span>
                <span className="text-sm font-semibold text-blue-600 dark:text-blue-400">
                  {formatCurrency(maxBalance - minBalance)}
                </span>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
