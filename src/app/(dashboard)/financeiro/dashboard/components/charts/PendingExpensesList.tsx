"use client";

/**
 * Componente de Lista de Despesas Pendentes - Fase 4
 * Exibe as despesas pendentes e em atraso em formato de lista
 */

import { useEffect, useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Clock, AlertTriangle, Calendar, Building, AlertCircle } from 'lucide-react';
import { cn } from '@/lib/utils';

import { getPendingExpensesList, PendingExpenseData } from '../../actions/charts/expense-chart-actions';
import { formatCurrency } from '../../utils/dashboard-utils';

// ============================================================================
// TIPOS
// ============================================================================

interface PendingExpensesListProps {
  className?: string;
}

// ============================================================================
// COMPONENTE DE ITEM DA LISTA
// ============================================================================

const PendingExpenseItem: React.FC<{ expense: PendingExpenseData }> = ({ expense }) => {
  const isOverdue = expense.status === 'overdue' || expense.daysPastDue > 0;
  
  return (
    <div className={cn(
      "p-4 border rounded-lg transition-colors",
      isOverdue 
        ? "border-red-200 dark:border-red-800 bg-red-50 dark:bg-red-900/20" 
        : "border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800"
    )}>
      <div className="flex items-start justify-between">
        <div className="flex-1 min-w-0">
          {/* Cabeçalho */}
          <div className="flex items-center gap-2 mb-2">
            <div 
              className="w-3 h-3 rounded-full flex-shrink-0"
              style={{ backgroundColor: expense.categoryColor }}
            />
            <h4 className="font-medium text-gray-900 dark:text-gray-100 truncate">
              {expense.supplierName}
            </h4>
            <Badge 
              variant={isOverdue ? "destructive" : "secondary"}
              className="text-xs"
            >
              {isOverdue ? (
                <>
                  <AlertTriangle className="h-3 w-3 mr-1" />
                  {expense.daysPastDue > 0 ? `${expense.daysPastDue}d atraso` : 'Vencido'}
                </>
              ) : (
                <>
                  <Clock className="h-3 w-3 mr-1" />
                  Pendente
                </>
              )}
            </Badge>
          </div>

          {/* Descrição */}
          {expense.description && (
            <p className="text-sm text-gray-600 dark:text-gray-400 mb-2 line-clamp-2">
              {expense.description}
            </p>
          )}

          {/* Informações */}
          <div className="flex items-center gap-4 text-xs text-gray-500 dark:text-gray-400">
            <div className="flex items-center gap-1">
              <Building className="h-3 w-3" />
              <span>{expense.categoryName}</span>
            </div>
            <div className="flex items-center gap-1">
              <Calendar className="h-3 w-3" />
              <span>Venc: {expense.formattedDueDate}</span>
            </div>
          </div>
        </div>

        {/* Valor */}
        <div className="text-right ml-4">
          <div className={cn(
            "font-bold text-lg",
            isOverdue 
              ? "text-red-600 dark:text-red-400" 
              : "text-gray-900 dark:text-gray-100"
          )}>
            {expense.formattedAmount}
          </div>
        </div>
      </div>
    </div>
  );
};

// ============================================================================
// COMPONENTE PRINCIPAL
// ============================================================================

export const PendingExpensesList: React.FC<PendingExpensesListProps> = ({
  className
}) => {
  const [data, setData] = useState<PendingExpenseData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // ============================================================================
  // CARREGAMENTO DE DADOS
  // ============================================================================

  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true);
        setError(null);

        const result = await getPendingExpensesList();

        if (!result.success) {
          throw new Error(result.error || 'Erro ao carregar dados');
        }

        setData(result.data || []);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Erro desconhecido');
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, []);

  // ============================================================================
  // CÁLCULOS AUXILIARES
  // ============================================================================

  const overdueExpenses = data.filter(expense => expense.status === 'overdue' || expense.daysPastDue > 0);
  const pendingExpenses = data.filter(expense => expense.status === 'pending' && expense.daysPastDue === 0);
  const totalAmount = data.reduce((sum, expense) => sum + expense.amount, 0);

  // ============================================================================
  // ESTADOS DE LOADING E ERRO
  // ============================================================================

  if (loading) {
    return (
      <Card className={cn("", className)}>
        <CardHeader className="pb-3">
          <CardTitle className="text-base font-medium flex items-center gap-2">
            <Clock className="h-4 w-4 text-orange-500" />
            Despesas Pendentes
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-64 flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-orange-500"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className={cn("", className)}>
        <CardHeader className="pb-3">
          <CardTitle className="text-base font-medium flex items-center gap-2">
            <Clock className="h-4 w-4 text-orange-500" />
            Despesas Pendentes
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-64 flex items-center justify-center">
            <div className="text-center">
              <AlertCircle className="h-8 w-8 text-red-500 mx-auto mb-2" />
              <p className="text-sm text-gray-600 dark:text-gray-400">{error}</p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!data.length) {
    return (
      <Card className={cn("", className)}>
        <CardHeader className="pb-3">
          <CardTitle className="text-base font-medium flex items-center gap-2">
            <Clock className="h-4 w-4 text-orange-500" />
            Despesas Pendentes
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-64 flex items-center justify-center">
            <div className="text-center">
              <Clock className="h-8 w-8 text-green-500 mx-auto mb-2" />
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Nenhuma despesa pendente
              </p>
              <p className="text-xs text-gray-500 dark:text-gray-500 mt-1">
                Todas as despesas estão em dia!
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  // ============================================================================
  // RENDERIZAÇÃO
  // ============================================================================

  return (
    <Card className={cn("", className)}>
      <CardHeader className="pb-3">
        <CardTitle className="text-base font-medium flex items-center gap-2">
          <Clock className="h-4 w-4 text-orange-500" />
          Despesas Pendentes
        </CardTitle>
        <p className="text-sm text-gray-600 dark:text-gray-400">
          Despesas aguardando pagamento e em atraso
        </p>
      </CardHeader>
      <CardContent>
        {/* Resumo */}
        <div className="grid grid-cols-3 gap-4 mb-6 p-4 bg-gray-50 dark:bg-gray-800/50 rounded-lg">
          <div className="text-center">
            <div className="text-lg font-bold text-gray-900 dark:text-gray-100">
              {data.length}
            </div>
            <div className="text-xs text-gray-600 dark:text-gray-400">Total</div>
          </div>
          <div className="text-center">
            <div className="text-lg font-bold text-red-600 dark:text-red-400">
              {overdueExpenses.length}
            </div>
            <div className="text-xs text-gray-600 dark:text-gray-400">Em Atraso</div>
          </div>
          <div className="text-center">
            <div className="text-lg font-bold text-orange-600 dark:text-orange-400">
              {pendingExpenses.length}
            </div>
            <div className="text-xs text-gray-600 dark:text-gray-400">Pendentes</div>
          </div>
        </div>

        {/* Lista de Despesas */}
        <div className="space-y-3 max-h-96 overflow-y-auto">
          {data.map((expense) => (
            <PendingExpenseItem key={expense.id} expense={expense} />
          ))}
        </div>

        {/* Total */}
        <div className="mt-6 pt-4 border-t border-gray-200 dark:border-gray-700">
          <div className="flex justify-between items-center">
            <span className="font-medium text-gray-900 dark:text-gray-100">
              Valor Total Pendente:
            </span>
            <span className="font-bold text-lg text-red-600 dark:text-red-400">
              {formatCurrency(totalAmount)}
            </span>
          </div>
        </div>

        {/* Ação */}
        {overdueExpenses.length > 0 && (
          <div className="mt-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
            <div className="flex items-center gap-2 text-red-700 dark:text-red-300">
              <AlertTriangle className="h-4 w-4" />
              <span className="text-sm font-medium">
                Atenção: {overdueExpenses.length} despesa(s) em atraso
              </span>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
