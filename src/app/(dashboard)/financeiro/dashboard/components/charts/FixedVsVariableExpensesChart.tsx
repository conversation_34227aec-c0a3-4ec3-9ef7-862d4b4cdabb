"use client";

/**
 * Componente de Gráfico de Despesas Fixas vs Variáveis - Fase 4
 * Exibe a comparação entre despesas fixas e variáveis em formato de barras
 */

import { useEffect, useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Cell } from 'recharts';
import { BarChart3, AlertCircle } from 'lucide-react';
import { cn } from '@/lib/utils';

import { getFixedVsVariableExpensesChart, ExpenseTypeData } from '../../actions/charts/expense-chart-actions';
import { getDateRangeForPeriod, formatCurrency } from '../../utils/dashboard-utils';

// ============================================================================
// TIPOS
// ============================================================================

interface FixedVsVariableExpensesChartProps {
  className?: string;
}

interface ChartTooltipProps {
  active?: boolean;
  payload?: any[];
  label?: string;
}

// ============================================================================
// COMPONENTE DE TOOLTIP CUSTOMIZADO
// ============================================================================

const CustomTooltip: React.FC<ChartTooltipProps> = ({ active, payload }) => {
  if (active && payload && payload.length) {
    const data = payload[0].payload;
    return (
      <div className="bg-white dark:bg-gray-800 p-3 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg">
        <p className="font-medium text-gray-900 dark:text-gray-100">
          {data.label}
        </p>
        <p className="text-sm text-blue-600 dark:text-blue-400">
          <span className="font-medium">Valor: </span>
          {data.formattedAmount}
        </p>
        <p className="text-sm text-gray-600 dark:text-gray-400">
          <span className="font-medium">Quantidade: </span>
          {data.count} despesas
        </p>
        <p className="text-sm text-gray-600 dark:text-gray-400">
          <span className="font-medium">Participação: </span>
          {data.percentage.toFixed(1)}%
        </p>
      </div>
    );
  }
  return null;
};

// ============================================================================
// COMPONENTE PRINCIPAL
// ============================================================================

export const FixedVsVariableExpensesChart: React.FC<FixedVsVariableExpensesChartProps> = ({
  className
}) => {
  const [data, setData] = useState<ExpenseTypeData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // ============================================================================
  // CARREGAMENTO DE DADOS
  // ============================================================================

  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true);
        setError(null);

        // Usar período do mês atual
        const dateRange = getDateRangeForPeriod('month');
        const result = await getFixedVsVariableExpensesChart(dateRange);

        if (!result.success) {
          throw new Error(result.error || 'Erro ao carregar dados');
        }

        setData(result.data || []);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Erro desconhecido');
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, []);

  // ============================================================================
  // CÁLCULOS AUXILIARES
  // ============================================================================

  const totalAmount = data.reduce((sum, item) => sum + item.amount, 0);
  const fixedData = data.find(item => item.type === 'fixed');
  const variableData = data.find(item => item.type === 'variable');

  // ============================================================================
  // ESTADOS DE LOADING E ERRO
  // ============================================================================

  if (loading) {
    return (
      <Card className={cn("", className)}>
        <CardHeader className="pb-3">
          <CardTitle className="text-base font-medium flex items-center gap-2">
            <BarChart3 className="h-4 w-4 text-blue-500" />
            Despesas Fixas vs Variáveis
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-64 flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className={cn("", className)}>
        <CardHeader className="pb-3">
          <CardTitle className="text-base font-medium flex items-center gap-2">
            <BarChart3 className="h-4 w-4 text-blue-500" />
            Despesas Fixas vs Variáveis
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-64 flex items-center justify-center">
            <div className="text-center">
              <AlertCircle className="h-8 w-8 text-red-500 mx-auto mb-2" />
              <p className="text-sm text-gray-600 dark:text-gray-400">{error}</p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!data.length || totalAmount === 0) {
    return (
      <Card className={cn("", className)}>
        <CardHeader className="pb-3">
          <CardTitle className="text-base font-medium flex items-center gap-2">
            <BarChart3 className="h-4 w-4 text-blue-500" />
            Despesas Fixas vs Variáveis
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-64 flex items-center justify-center">
            <div className="text-center">
              <BarChart3 className="h-8 w-8 text-gray-400 mx-auto mb-2" />
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Nenhuma despesa encontrada no período
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  // ============================================================================
  // RENDERIZAÇÃO
  // ============================================================================

  return (
    <Card className={cn("", className)}>
      <CardHeader className="pb-3">
        <CardTitle className="text-base font-medium flex items-center gap-2">
          <BarChart3 className="h-4 w-4 text-blue-500" />
          Despesas Fixas vs Variáveis
        </CardTitle>
        <p className="text-sm text-gray-600 dark:text-gray-400">
          Comparação entre despesas recorrentes e pontuais
        </p>
      </CardHeader>
      <CardContent>
        <div className="h-64 mb-4">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={data} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
              <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
              <XAxis 
                dataKey="label" 
                axisLine={false}
                tickLine={false}
                className="text-xs"
              />
              <YAxis 
                axisLine={false}
                tickLine={false}
                className="text-xs"
                tickFormatter={(value) => formatCurrency(value, { minimumFractionDigits: 0, maximumFractionDigits: 0 })}
              />
              <Tooltip content={<CustomTooltip />} />
              <Bar
                dataKey="amount"
                radius={[4, 4, 0, 0]}
              >
                {data.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Bar>
            </BarChart>
          </ResponsiveContainer>
        </div>

        {/* Resumo Detalhado */}
        <div className="space-y-3">
          {/* Despesas Fixas */}
          {fixedData && (
            <div className="flex items-center justify-between p-3 bg-red-50 dark:bg-red-900/20 rounded-lg">
              <div className="flex items-center gap-3">
                <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                <div>
                  <div className="font-medium text-gray-900 dark:text-gray-100">
                    Despesas Fixas
                  </div>
                  <div className="text-xs text-gray-600 dark:text-gray-400">
                    {fixedData.count} despesas recorrentes
                  </div>
                </div>
              </div>
              <div className="text-right">
                <div className="font-medium text-red-600 dark:text-red-400">
                  {fixedData.formattedAmount}
                </div>
                <div className="text-xs text-gray-600 dark:text-gray-400">
                  {fixedData.percentage.toFixed(1)}% do total
                </div>
              </div>
            </div>
          )}

          {/* Despesas Variáveis */}
          {variableData && (
            <div className="flex items-center justify-between p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
              <div className="flex items-center gap-3">
                <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                <div>
                  <div className="font-medium text-gray-900 dark:text-gray-100">
                    Despesas Variáveis
                  </div>
                  <div className="text-xs text-gray-600 dark:text-gray-400">
                    {variableData.count} despesas pontuais
                  </div>
                </div>
              </div>
              <div className="text-right">
                <div className="font-medium text-blue-600 dark:text-blue-400">
                  {variableData.formattedAmount}
                </div>
                <div className="text-xs text-gray-600 dark:text-gray-400">
                  {variableData.percentage.toFixed(1)}% do total
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Total */}
        <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
          <div className="flex justify-between items-center">
            <span className="font-medium text-gray-900 dark:text-gray-100">Total Geral:</span>
            <span className="font-bold text-lg text-gray-900 dark:text-gray-100">
              {formatCurrency(totalAmount)}
            </span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
