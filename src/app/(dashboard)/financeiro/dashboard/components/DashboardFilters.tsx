"use client";

/**
 * Componente de Filtros do Dashboard Financeiro
 * Permite seleção de período e outras opções de filtro
 */

import { useState, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { CalendarIcon, RefreshCw } from 'lucide-react';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { cn } from '@/lib/utils';

import { 
  DashboardFiltersProps, 
  PeriodType, 
  DateRange 
} from '../types/dashboard-types';
import { 
  getDateRangeForPeriod, 
  formatDateRange 
} from '../utils/dashboard-utils';

// ============================================================================
// OPÇÕES DE PERÍODO PREDEFINIDAS
// ============================================================================

const PERIOD_OPTIONS: { value: PeriodType; label: string }[] = [
  { value: 'month', label: 'Mês Atual' },
  { value: 'quarter', label: 'Trimestre Atual' },
  { value: 'semester', label: 'Semestre Atual' },
  { value: 'year', label: 'Ano Atual' },
  { value: 'custom', label: 'Período Personalizado' }
];

// ============================================================================
// COMPONENTE PRINCIPAL
// ============================================================================

export const DashboardFilters: React.FC<DashboardFiltersProps> = ({
  filters,
  onFiltersChange,
  loading = false,
  className
}) => {
  const [isCustomDateOpen, setIsCustomDateOpen] = useState(false);
  const [customStartDate, setCustomStartDate] = useState<Date | undefined>(
    filters.dateRange.period === 'custom' ? filters.dateRange.startDate : undefined
  );
  const [customEndDate, setCustomEndDate] = useState<Date | undefined>(
    filters.dateRange.period === 'custom' ? filters.dateRange.endDate : undefined
  );

  // ============================================================================
  // HANDLERS
  // ============================================================================

  const handlePeriodChange = useCallback((period: PeriodType) => {
    if (period === 'custom') {
      // Para período customizado, manter datas atuais ou usar mês atual como padrão
      const currentRange = customStartDate && customEndDate 
        ? {
            startDate: customStartDate,
            endDate: customEndDate,
            period: 'custom' as const,
            label: 'Período Personalizado'
          }
        : getDateRangeForPeriod('month');

      onFiltersChange({
        ...filters,
        dateRange: { ...currentRange, period: 'custom' }
      });
    } else {
      // Para períodos predefinidos, calcular automaticamente
      const newRange = getDateRangeForPeriod(period);
      onFiltersChange({
        ...filters,
        dateRange: newRange
      });
    }
  }, [filters, onFiltersChange, customStartDate, customEndDate]);

  const handleCustomDateChange = useCallback((
    startDate: Date | undefined, 
    endDate: Date | undefined
  ) => {
    if (startDate && endDate) {
      const customRange: DateRange = {
        startDate,
        endDate,
        period: 'custom',
        label: `${format(startDate, 'dd/MM/yyyy', { locale: ptBR })} - ${format(endDate, 'dd/MM/yyyy', { locale: ptBR })}`
      };

      onFiltersChange({
        ...filters,
        dateRange: customRange
      });

      setCustomStartDate(startDate);
      setCustomEndDate(endDate);
      setIsCustomDateOpen(false);
    }
  }, [filters, onFiltersChange]);

  const handleRefresh = useCallback(() => {
    // Recarregar dados mantendo os filtros atuais
    onFiltersChange({ ...filters });
  }, [filters, onFiltersChange]);

  // ============================================================================
  // RENDER
  // ============================================================================

  return (
    <Card className={cn("mb-6", className)}>
      <CardHeader className="pb-4">
        <CardTitle className="text-lg font-semibold">Filtros do Dashboard</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center">
          {/* Seletor de Período */}
          <div className="flex-1 min-w-[200px]">
            <label className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 block">
              Período
            </label>
            <Select
              value={filters.dateRange.period}
              onValueChange={handlePeriodChange}
              disabled={loading}
            >
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Selecione o período" />
              </SelectTrigger>
              <SelectContent>
                {PERIOD_OPTIONS.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Seletor de Data Customizada */}
          {filters.dateRange.period === 'custom' && (
            <div className="flex-1 min-w-[250px]">
              <label className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 block">
                Período Personalizado
              </label>
              <Popover open={isCustomDateOpen} onOpenChange={setIsCustomDateOpen}>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      "w-full justify-start text-left font-normal",
                      !customStartDate && !customEndDate && "text-muted-foreground"
                    )}
                    disabled={loading}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {customStartDate && customEndDate ? (
                      `${format(customStartDate, 'dd/MM/yyyy', { locale: ptBR })} - ${format(customEndDate, 'dd/MM/yyyy', { locale: ptBR })}`
                    ) : (
                      "Selecione as datas"
                    )}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <div className="p-4 space-y-4">
                    <div>
                      <label className="text-sm font-medium mb-2 block">Data Inicial</label>
                      <Calendar
                        mode="single"
                        selected={customStartDate}
                        onSelect={setCustomStartDate}
                        locale={ptBR}
                        className="rounded-md border"
                      />
                    </div>
                    <div>
                      <label className="text-sm font-medium mb-2 block">Data Final</label>
                      <Calendar
                        mode="single"
                        selected={customEndDate}
                        onSelect={setCustomEndDate}
                        locale={ptBR}
                        className="rounded-md border"
                        disabled={(date) => 
                          customStartDate ? date < customStartDate : false
                        }
                      />
                    </div>
                    <div className="flex gap-2">
                      <Button
                        size="sm"
                        onClick={() => handleCustomDateChange(customStartDate, customEndDate)}
                        disabled={!customStartDate || !customEndDate}
                        className="flex-1"
                      >
                        Aplicar
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => setIsCustomDateOpen(false)}
                        className="flex-1"
                      >
                        Cancelar
                      </Button>
                    </div>
                  </div>
                </PopoverContent>
              </Popover>
            </div>
          )}

          {/* Informações do Período Atual */}
          <div className="flex-1 min-w-[200px]">
            <label className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 block">
              Período Selecionado
            </label>
            <div className="p-2 bg-gray-50 dark:bg-gray-800 rounded-md text-sm">
              {formatDateRange(filters.dateRange)}
            </div>
          </div>

          {/* Botão de Atualizar */}
          <div className="flex items-end">
            <Button
              variant="outline"
              size="sm"
              onClick={handleRefresh}
              disabled={loading}
              className="flex items-center gap-2"
            >
              <RefreshCw className={cn("h-4 w-4", loading && "animate-spin")} />
              Atualizar
            </Button>
          </div>
        </div>

        {/* Informações Adicionais */}
        <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
          <div className="flex flex-col sm:flex-row gap-4 text-xs text-gray-600 dark:text-gray-400">
            <div>
              <span className="font-medium">Última atualização:</span>{' '}
              {new Date().toLocaleString('pt-BR')}
            </div>
            {filters.refreshInterval && (
              <div>
                <span className="font-medium">Atualização automática:</span>{' '}
                a cada {filters.refreshInterval / 1000}s
              </div>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
