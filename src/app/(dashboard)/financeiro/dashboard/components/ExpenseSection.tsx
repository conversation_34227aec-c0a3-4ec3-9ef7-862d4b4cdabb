"use client";

/**
 * Seção de Despesas do Dashboard Financeiro - Fase 4
 * Contém todos os KPIs, gráficos e análises relacionadas a despesas
 */

import { Alert, AlertDescription } from '@/components/ui/alert';
import { AlertCircle, TrendingDown, Package } from 'lucide-react';
import { cn } from '@/lib/utils';

import { DashboardLayoutProps } from '../types/dashboard-types';
import { LoadingStates } from './LoadingStates';
import { EnhancedKPICard } from './EnhancedKPICard';
import {
  ExpenseCategoryChart,
  MonthlyExpenseChart,
  FixedVsVariableExpensesChart,
  PendingExpensesList
} from './charts';

// ============================================================================
// TIPOS
// ============================================================================

interface ExpenseSectionProps {
  kpis: DashboardLayoutProps['kpis'];
  data?: DashboardLayoutProps['data'];
  loading?: boolean;
  error?: string | null;
  className?: string;
}

// ============================================================================
// SEÇÃO DE KPIs DE DESPESAS
// ============================================================================

interface ExpenseKPISectionProps {
  kpis: DashboardLayoutProps['kpis'];
  loading?: boolean;
}

const ExpenseKPISection: React.FC<ExpenseKPISectionProps> = ({ kpis, loading }) => {
  if (loading) {
    return <LoadingStates.KPIs />;
  }

  // Dados simulados para sparklines (serão implementados com dados reais nas próximas iterações)
  const generateSparklineData = (trend: string) => {
    const baseData = [100, 105, 98, 110, 115, 108, 120];
    if (trend === 'up') return baseData.map((v, i) => v + i * 2);
    if (trend === 'down') return baseData.map((v, i) => v - i * 1.5);
    return baseData;
  };

  return (
    <div className="space-y-6">
      {/* KPIs de Despesas */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <EnhancedKPICard
          title="Total de Despesas"
          metric={kpis.totalExpenses}
          icon={<Package className="h-5 w-5" />}
          description="Total de despesas pagas no período"
          sparklineData={generateSparklineData(kpis.totalExpenses.trend)}
          variant="default"
        />
        <EnhancedKPICard
          title="Lucro Líquido"
          metric={kpis.netProfit}
          icon={<TrendingDown className="h-5 w-5" />}
          description="Receitas menos despesas do período"
          sparklineData={generateSparklineData(kpis.netProfit.trend)}
          variant="default"
        />
        <EnhancedKPICard
          title="Margem de Lucro"
          metric={kpis.profitMargin}
          icon={<TrendingDown className="h-5 w-5" />}
          description="Percentual de lucro sobre a receita"
          sparklineData={generateSparklineData(kpis.profitMargin.trend)}
          variant="default"
        />
      </div>
    </div>
  );
};

// ============================================================================
// COMPONENTE PRINCIPAL DA SEÇÃO DE DESPESAS
// ============================================================================

export const ExpenseSection: React.FC<ExpenseSectionProps> = ({
  kpis,
  data,
  loading = false,
  error = null,
  className
}) => {
  // Mostrar erro se houver
  if (error) {
    return (
      <div className={cn("space-y-6", className)}>
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            Erro ao carregar dados de despesas: {error}
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  // Loading completo
  if (loading && !data) {
    return (
      <div className={cn("space-y-6", className)}>
        <LoadingStates.Dashboard />
      </div>
    );
  }

  return (
    <div className={cn("space-y-8", className)}>
      {/* Seção 1: KPIs de Despesas */}
      <section>
        <div className="mb-6">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
                Indicadores de Despesas
              </h2>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Principais métricas de despesas com comparações e tendências
              </p>
            </div>
            <div className="flex items-center gap-2 text-sm text-gray-500 dark:text-gray-400">
              <div className="flex items-center gap-1">
                <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                <span>Métricas de Despesas</span>
              </div>
            </div>
          </div>
        </div>
        <ExpenseKPISection kpis={kpis} loading={loading} />
      </section>

      {/* Seção 2: Gráficos de Despesas */}
      <section>
        <div className="mb-6">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
                Análise Visual de Despesas
              </h2>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Visualizações detalhadas de despesas, categorias e tendências
              </p>
            </div>
            <div className="flex items-center gap-2 text-sm text-gray-500 dark:text-gray-400">
              <div className="flex items-center gap-1">
                <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                <span>Gráficos de Despesas</span>
              </div>
            </div>
          </div>
        </div>

        {/* Grid de Gráficos */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Gráfico de Evolução de Despesas */}
          <div className="lg:col-span-2">
            <MonthlyExpenseChart />
          </div>

          {/* Gráfico de Despesas por Categoria */}
          <ExpenseCategoryChart />

          {/* Gráfico de Despesas Fixas vs Variáveis */}
          <FixedVsVariableExpensesChart />

          {/* Lista de Despesas Pendentes - Span completo */}
          <div className="lg:col-span-2">
            <PendingExpensesList />
          </div>
        </div>
      </section>

      {/* Seção 3: Insights e Alertas */}
      <section>
        <div className="mb-4">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
            Insights de Despesas
          </h2>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            Análises automáticas e alertas importantes sobre suas despesas
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Alerta de Margem de Lucro */}
          {kpis.profitMargin.current < 10 && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                <strong>Margem de lucro baixa:</strong> Sua margem está em {kpis.profitMargin.formatted.current}. 
                Considere revisar suas despesas ou aumentar suas receitas.
              </AlertDescription>
            </Alert>
          )}

          {/* Alerta de Crescimento de Despesas */}
          {kpis.totalExpenses.growth > 20 && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                <strong>Crescimento alto de despesas:</strong> Suas despesas aumentaram {kpis.totalExpenses.formatted.growth} 
                em relação ao período anterior.
              </AlertDescription>
            </Alert>
          )}

          {/* Insight Positivo */}
          {kpis.totalExpenses.growth < 0 && (
            <Alert className="border-green-200 bg-green-50 dark:bg-green-900/20">
              <AlertCircle className="h-4 w-4 text-green-600" />
              <AlertDescription className="text-green-700 dark:text-green-300">
                <strong>Ótimo controle de gastos!</strong> Suas despesas diminuíram {Math.abs(kpis.totalExpenses.growth)}% 
                em relação ao período anterior.
              </AlertDescription>
            </Alert>
          )}

          {/* Insight de Lucro */}
          {kpis.netProfit.current > 0 && kpis.profitMargin.current > 20 && (
            <Alert className="border-green-200 bg-green-50 dark:bg-green-900/20">
              <AlertCircle className="h-4 w-4 text-green-600" />
              <AlertDescription className="text-green-700 dark:text-green-300">
                <strong>Excelente performance financeira!</strong> Você tem uma margem de lucro saudável 
                de {kpis.profitMargin.formatted.current}.
              </AlertDescription>
            </Alert>
          )}
        </div>
      </section>

      {/* Informações de Atualização */}
      <section className="pt-4 border-t border-gray-200 dark:border-gray-700">
        <div className="text-xs text-gray-500 dark:text-gray-400 text-center">
          Última atualização: {data?.lastUpdated ?
            new Date(data.lastUpdated).toLocaleString('pt-BR') :
            'Carregando...'
          }
        </div>
      </section>
    </div>
  );
};
