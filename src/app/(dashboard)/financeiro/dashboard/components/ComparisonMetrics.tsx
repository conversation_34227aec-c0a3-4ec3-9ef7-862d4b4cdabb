"use client";

/**
 * Componente de Métricas de Comparação - Fase 2
 * Exibe comparações detalhadas entre períodos e análises de tendência
 */

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { TrendingUp, TrendingDown, Calendar, Target, BarChart3 } from 'lucide-react';
import { cn } from '@/lib/utils';

import { FinancialKPIs, MetricWithGrowth } from '../types/dashboard-types';
import { TrendIndicator, StatusBadge } from './TrendIndicator';

// ============================================================================
// TIPOS
// ============================================================================

interface ComparisonMetricsProps {
  kpis: FinancialKPIs;
  periodLabel: string;
  className?: string;
}

interface MetricComparisonRowProps {
  label: string;
  metric: MetricWithGrowth;
  icon?: React.ReactNode;
  target?: number;
  isHighlighted?: boolean;
}

interface TrendSummaryProps {
  kpis: FinancialKPIs;
}

// ============================================================================
// COMPONENTES AUXILIARES
// ============================================================================

const MetricComparisonRow: React.FC<MetricComparisonRowProps> = ({
  label,
  metric,
  icon,
  target,
  isHighlighted = false
}) => {
  const progressValue = target ? Math.min((metric.current / target) * 100, 100) : 0;
  
  return (
    <div className={cn(
      "flex items-center justify-between py-3 px-4 rounded-lg transition-colors",
      isHighlighted ? "bg-blue-50 dark:bg-blue-900/10" : "hover:bg-gray-50 dark:hover:bg-gray-800/50"
    )}>
      <div className="flex items-center gap-3 flex-1">
        {icon && (
          <div className="text-gray-400 dark:text-gray-500">
            {icon}
          </div>
        )}
        <div className="flex-1">
          <div className="flex items-center gap-2">
            <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
              {label}
            </span>
            {isHighlighted && (
              <Badge variant="secondary" className="text-xs">
                Destaque
              </Badge>
            )}
          </div>
          <div className="text-xs text-gray-500 dark:text-gray-400">
            Anterior: {metric.formatted.previous}
          </div>
        </div>
      </div>
      
      <div className="flex items-center gap-4">
        <div className="text-right">
          <div className="text-sm font-semibold text-gray-900 dark:text-gray-100">
            {metric.formatted.current}
          </div>
          <div className="flex items-center gap-1">
            <TrendIndicator 
              trend={metric.trend} 
              growth={metric.growth}
              size="sm"
              showText
            />
          </div>
        </div>
        
        {target && (
          <div className="w-16">
            <Progress 
              value={progressValue} 
              className="h-2"
            />
            <div className="text-xs text-gray-500 dark:text-gray-400 mt-1 text-center">
              {progressValue.toFixed(0)}%
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

const TrendSummary: React.FC<TrendSummaryProps> = ({ kpis }) => {
  const metrics = [
    { key: 'totalRevenue', label: 'Receitas', metric: kpis.totalRevenue },
    { key: 'totalExpenses', label: 'Despesas', metric: kpis.totalExpenses },
    { key: 'netProfit', label: 'Lucro', metric: kpis.netProfit },
    { key: 'activeStudents', label: 'Alunos', metric: kpis.activeStudents },
  ];

  const positiveCount = metrics.filter(m => m.metric.trend === 'up').length;
  const negativeCount = metrics.filter(m => m.metric.trend === 'down').length;
  const stableCount = metrics.filter(m => m.metric.trend === 'stable').length;

  const getOverallTrend = () => {
    if (positiveCount > negativeCount) return 'positive';
    if (negativeCount > positiveCount) return 'negative';
    return 'stable';
  };

  const overallTrend = getOverallTrend();

  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
      <div className="flex items-center gap-3 p-4 bg-green-50 dark:bg-green-900/10 rounded-lg">
        <div className="p-2 bg-green-100 dark:bg-green-900/20 rounded-full">
          <TrendingUp className="h-4 w-4 text-green-600 dark:text-green-400" />
        </div>
        <div>
          <div className="text-lg font-semibold text-green-900 dark:text-green-100">
            {positiveCount}
          </div>
          <div className="text-sm text-green-700 dark:text-green-300">
            Métricas em alta
          </div>
        </div>
      </div>

      <div className="flex items-center gap-3 p-4 bg-red-50 dark:bg-red-900/10 rounded-lg">
        <div className="p-2 bg-red-100 dark:bg-red-900/20 rounded-full">
          <TrendingDown className="h-4 w-4 text-red-600 dark:text-red-400" />
        </div>
        <div>
          <div className="text-lg font-semibold text-red-900 dark:text-red-100">
            {negativeCount}
          </div>
          <div className="text-sm text-red-700 dark:text-red-300">
            Métricas em queda
          </div>
        </div>
      </div>

      <div className="flex items-center gap-3 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
        <div className="p-2 bg-gray-100 dark:bg-gray-700 rounded-full">
          <BarChart3 className="h-4 w-4 text-gray-600 dark:text-gray-400" />
        </div>
        <div>
          <div className="text-lg font-semibold text-gray-900 dark:text-gray-100">
            {stableCount}
          </div>
          <div className="text-sm text-gray-700 dark:text-gray-300">
            Métricas estáveis
          </div>
        </div>
      </div>
    </div>
  );
};

// ============================================================================
// COMPONENTE PRINCIPAL
// ============================================================================

export const ComparisonMetrics: React.FC<ComparisonMetricsProps> = ({
  kpis,
  periodLabel,
  className
}) => {
  // Identificar métrica com maior crescimento
  const metrics = [
    { key: 'totalRevenue', label: 'Receita Total', metric: kpis.totalRevenue, icon: <TrendingUp className="h-4 w-4" /> },
    { key: 'totalExpenses', label: 'Despesas Totais', metric: kpis.totalExpenses, icon: <TrendingDown className="h-4 w-4" /> },
    { key: 'netProfit', label: 'Lucro Líquido', metric: kpis.netProfit, icon: <Target className="h-4 w-4" /> },
    { key: 'activeStudents', label: 'Alunos Ativos', metric: kpis.activeStudents, icon: <BarChart3 className="h-4 w-4" /> },
    { key: 'averageRevenuePerUser', label: 'Receita por Aluno', metric: kpis.averageRevenuePerUser, icon: <TrendingUp className="h-4 w-4" /> },
    { key: 'profitMargin', label: 'Margem de Lucro', metric: kpis.profitMargin, icon: <Target className="h-4 w-4" /> },
  ];

  const bestPerformer = metrics.reduce((best, current) => 
    current.metric.growth > best.metric.growth ? current : best
  );

  return (
    <div className={cn("space-y-6", className)}>
      {/* Resumo de Tendências */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            Resumo de Tendências - {periodLabel}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <TrendSummary kpis={kpis} />
        </CardContent>
      </Card>

      {/* Comparações Detalhadas */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>Comparação Detalhada</CardTitle>
            <StatusBadge 
              status={bestPerformer.metric.trend === 'up' ? 'excellent' : 
                     bestPerformer.metric.trend === 'down' ? 'warning' : 'good'} 
              text={`Melhor: ${bestPerformer.label}`}
            />
          </div>
        </CardHeader>
        <CardContent className="space-y-2">
          {metrics.map((item) => (
            <MetricComparisonRow
              key={item.key}
              label={item.label}
              metric={item.metric}
              icon={item.icon}
              isHighlighted={item.key === bestPerformer.key}
            />
          ))}
        </CardContent>
      </Card>

      {/* Insights e Alertas */}
      <Card>
        <CardHeader>
          <CardTitle>Insights do Período</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {kpis.netProfit.trend === 'up' && (
              <div className="flex items-start gap-3 p-3 bg-green-50 dark:bg-green-900/10 rounded-lg">
                <TrendingUp className="h-5 w-5 text-green-600 dark:text-green-400 mt-0.5" />
                <div>
                  <div className="font-medium text-green-900 dark:text-green-100">
                    Crescimento do Lucro
                  </div>
                  <div className="text-sm text-green-700 dark:text-green-300">
                    O lucro líquido cresceu {kpis.netProfit.formatted.growth} em relação ao período anterior.
                  </div>
                </div>
              </div>
            )}

            {kpis.activeStudents.trend === 'up' && (
              <div className="flex items-start gap-3 p-3 bg-blue-50 dark:bg-blue-900/10 rounded-lg">
                <TrendingUp className="h-5 w-5 text-blue-600 dark:text-blue-400 mt-0.5" />
                <div>
                  <div className="font-medium text-blue-900 dark:text-blue-100">
                    Crescimento de Alunos
                  </div>
                  <div className="text-sm text-blue-700 dark:text-blue-300">
                    Base de alunos ativos cresceu {kpis.activeStudents.formatted.growth}.
                  </div>
                </div>
              </div>
            )}

            {kpis.totalExpenses.trend === 'up' && kpis.totalExpenses.growth > 10 && (
              <div className="flex items-start gap-3 p-3 bg-yellow-50 dark:bg-yellow-900/10 rounded-lg">
                <TrendingUp className="h-5 w-5 text-yellow-600 dark:text-yellow-400 mt-0.5" />
                <div>
                  <div className="font-medium text-yellow-900 dark:text-yellow-100">
                    Atenção: Aumento de Despesas
                  </div>
                  <div className="text-sm text-yellow-700 dark:text-yellow-300">
                    As despesas aumentaram {kpis.totalExpenses.formatted.growth}. Monitore os gastos.
                  </div>
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
