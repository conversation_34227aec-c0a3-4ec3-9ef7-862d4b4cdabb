'use server';

import { z } from 'zod';
import { createTenantServerClient } from '@/services/supabase/server';

/**
 * Schema de validação para ativar/desativar forma de pagamento
 */
const togglePaymentMethodSchema = z.object({
  paymentMethodId: z.string().uuid(),
  enabled: z.boolean(),
});

export type TogglePaymentMethodInput = z.infer<typeof togglePaymentMethodSchema>;

/**
 * togglePaymentMethod – Ativa ou desativa uma forma de pagamento para um tenant
 *
 * @param input {TogglePaymentMethodInput}
 * @returns {Promise<{ success: boolean; error?: string }>} – Status da operação
 */
export async function togglePaymentMethod(input: TogglePaymentMethodInput) {
  const parsed = togglePaymentMethodSchema.safeParse(input);
  if (!parsed.success) {
    return { success: false, error: 'Parâmetros inválidos' } as const;
  }

  const { paymentMethodId, enabled } = parsed.data;

  try {
    // Importar next/headers dinamicamente
    const { headers, cookies } = await import('next/headers');
    const [headersList, cookieStore] = await Promise.all([
      headers(),
      cookies()
    ]);

    // Resolvemos o tenant no próprio servidor para não expor o ID para o cliente
    const extractor = new (await import('@/services/tenant/tenant-extractor-server')).TenantExtractorServer();
    const tenant = await extractor.getTenantIdentifier(headersList, cookieStore);

    if (!tenant.id) {
      console.error('[togglePaymentMethod] Tenant ID não encontrado');
      return { success: false, error: 'Tenant não identificado' } as const;
    }

    const supabase = await createTenantServerClient();

    const { error } = await supabase.rpc('toggle_tenant_payment_method', {
      p_tenant_id: tenant.id,
      p_payment_method_id: paymentMethodId,
      p_enabled: enabled,
    });

    if (error) {
      console.error('[togglePaymentMethod] Supabase RPC error', error);
      return { success: false, error: error.message } as const;
    }

    return { success: true } as const;
  } catch (err) {
    console.error('[togglePaymentMethod] Unexpected error', err);
    return { success: false, error: 'Erro inesperado' } as const;
  }
}

/**
 * getTenantPaymentMethods – Retorna todas as formas de pagamento disponíveis
 * indicando se cada uma está habilitada para o tenant atual.
 *
 * Esta função executa três consultas:
 * 1. Busca todas as formas de pagamento do catálogo (payment_methods)
 * 2. Busca as configurações do tenant (tenant_payment_settings)
 * 3. Busca a contagem de transações por método no mês atual
 *
 * Em seguida faz o merge para indicar quais estão habilitadas e quantas transações tiveram.
 */
export async function getTenantPaymentMethods() {
  try {
    // Importar next/headers dinamicamente
    const { headers, cookies } = await import('next/headers');
    const [headersList, cookieStore] = await Promise.all([
      headers(),
      cookies()
    ]);

    const extractor = new (await import('@/services/tenant/tenant-extractor-server')).TenantExtractorServer();
    const tenant = await extractor.getTenantIdentifier(headersList, cookieStore);

    if (!tenant.id) {
      console.error('[getTenantPaymentMethods] Tenant ID não encontrado');
      return [];
    }

    const supabase = await createTenantServerClient();

    // 1. Todas as formas de pagamento disponíveis na plataforma
    const { data: paymentMethods, error: pmError } = await supabase
      .from('payment_methods')
      .select('id, name, slug, icon_name')
      .order('name');

    if (pmError) {
      console.error('[getTenantPaymentMethods] error fetching payment_methods', pmError);
      throw pmError;
    }

    // 2. Configurações específicas do tenant
    const { data: tenantSettings, error: tsError } = await supabase
      .from('tenant_payment_settings')
      .select('payment_method_id, enabled')
      .eq('tenant_id', tenant.id);

    if (tsError) {
      console.error('[getTenantPaymentMethods] error fetching tenant_payment_settings', tsError);
      throw tsError;
    }

    // 3. Buscar contagem de transações por método no mês atual
    const transactionCounts = await getPaymentMethodTransactionsCount(tenant.id);

    const enabledMap = new Map<string, boolean>(
      (tenantSettings ?? []).map((row) => [row.payment_method_id as string, row.enabled as boolean]),
    );

    return (paymentMethods ?? []).map((pm) => ({
      ...pm,
      enabled: enabledMap.get(pm.id) ?? false,
      transactions: transactionCounts.get(pm.slug || '') || 0,
    }));
  } catch (error) {
    console.error('[getTenantPaymentMethods] Unexpected error', error);
    return [];
  }
}

/**
 * getPaymentMethodTransactionsCount – Retorna a quantidade de transações por método de pagamento no mês atual
 *
 * @param tenantId {string} - ID do tenant
 * @returns {Promise<Map<string, number>>} - Mapa com método de pagamento e quantidade de transações
 */
async function getPaymentMethodTransactionsCount(tenantId: string): Promise<Map<string, number>> {
  try {
    const supabase = await createTenantServerClient();

    // Calcular o primeiro dia do mês atual no timezone do Brasil
    const now = new Date();
    const firstDayOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
    const firstDayOfNextMonth = new Date(now.getFullYear(), now.getMonth() + 1, 1);

    // Buscar pagamentos realizados (status = 'paid') no mês atual
    const { data: transactions, error } = await supabase
      .from('payments')
      .select('payment_method')
      .eq('tenant_id', tenantId)
      .eq('status', 'paid')
      .not('payment_method', 'is', null)
      .gte('paid_at', firstDayOfMonth.toISOString())
      .lt('paid_at', firstDayOfNextMonth.toISOString());

    if (error) {
      console.error('[getPaymentMethodTransactionsCount] Erro ao buscar transações:', error);
      return new Map();
    }

    // Contar transações por método de pagamento
    const transactionCounts = new Map<string, number>();

    (transactions ?? []).forEach((transaction) => {
      const method = transaction.payment_method as string;
      transactionCounts.set(method, (transactionCounts.get(method) || 0) + 1);
    });

    return transactionCounts;
  } catch (error) {
    console.error('[getPaymentMethodTransactionsCount] Erro inesperado:', error);
    return new Map();
  }
}

/**
 * getActiveTenantPaymentMethodsForSelect – Retorna apenas as formas de pagamento
 * ATIVAS para o tenant, formatadas para um componente Select.
 *
 * @returns {Promise<{ value: string; label: string; }[]>}
 */
export async function getActiveTenantPaymentMethodsForSelect() {
  try {
    const extractor = new (await import('@/services/tenant/tenant-extractor-server')).TenantExtractorServer();
    const tenant = await extractor.getTenantIdentifier();

    if (!tenant.id) {
      console.error('[getActiveTenantPaymentMethodsForSelect] Tenant ID não encontrado');
      return [];
    }

    const supabase = await createTenantServerClient();

    const { data: paymentMethods, error: pmError } = await supabase
      .from('payment_methods')
      .select('id, name, slug')
      .order('name');

    if (pmError) {
      console.error('[getActiveTenantPaymentMethodsForSelect] erro ao buscar payment_methods', pmError);
      throw pmError;
    }

    const { data: tenantSettings, error: tsError } = await supabase
      .from('tenant_payment_settings')
      .select('payment_method_id')
      .eq('tenant_id', tenant.id)
      .eq('enabled', true);

    if (tsError) {
      console.error('[getActiveTenantPaymentMethodsForSelect] erro ao buscar tenant_payment_settings', tsError);
      throw tsError;
    }

    const enabledSet = new Set(
      (tenantSettings ?? []).map((s) => s.payment_method_id as string)
    );

    const enabledMethods = (paymentMethods ?? []).filter((pm) => enabledSet.has(pm.id));

    return enabledMethods.map((pm) => ({
      value: pm.slug || pm.id,
      label: pm.name,
    }));

  } catch (error) {
    console.error('[getActiveTenantPaymentMethodsForSelect] Unexpected error', error);
    return [];
  }
}