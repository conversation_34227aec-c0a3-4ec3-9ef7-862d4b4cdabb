import { PaymentMethod } from '../types';
import { FaPix } from "react-icons/fa6";
import { FaRegMoneyBill1 } from "react-icons/fa6";


export const paymentMethodsData: PaymentMethod[] = [
  {
    id: 'pix',
    name: 'P<PERSON>',
    Icon: FaPix,
    iconClassName: 'text-green-600',
    status: 'Ativo',
    fee: 'R$ 0,00',
    transactions: '127 este mês',
    enabled: true,
  },
  // {
  //   id: 'credit-card',
  //   name: 'Cart<PERSON> de Crédito',
  //   Icon: CreditCard,
  //   iconClassName: 'text-blue-600',
  //   status: 'Ativo',
  //   fee: '3,49%',
  //   transactions: '89 este mês',
  //   enabled: true,
  // },
  // {
  //   id: 'debit-card',
  //   name: 'Cart<PERSON> de Débito',
  //   Icon: CreditCard,
  //   iconClassName: 'text-purple-600',
  //   status: 'Ativo',
  //   fee: '1,99%',
  //   transactions: '34 este mês',
  //   enabled: true,
  // },
  {
    id: 'cash',
    name: '<PERSON><PERSON><PERSON>',
    Icon: FaRegMoneyBill1,
    iconClassName: 'text-orange-600',
    status: 'Ativo',
    fee: 'R$ 0,00',
    transactions: '23 este mês',
    enabled: true,
  },
]; 