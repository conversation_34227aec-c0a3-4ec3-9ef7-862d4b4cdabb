'use client';

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { Wallet } from 'lucide-react';

interface GeneralSettingsProps {
    allowInstallments: boolean;
    convenienceFee: boolean;
    pixDiscount: boolean;
  }

export function GeneralSettings({allowInstallments, convenienceFee, pixDiscount}: GeneralSettingsProps) {
  return (
    <Card className="border-0 shadow-lg bg-gradient-to-br from-white to-gray-50/50 dark:from-gray-900 dark:to-gray-800/50 overflow-hidden group hover:shadow-xl transition-all duration-300">
      <CardHeader className="pb-4 bg-gradient-to-r from-indigo-50/50 to-transparent dark:from-indigo-900/20 dark:to-transparent">
        <CardTitle className="text-sm font-semibold text-gray-700 dark:text-gray-300 flex items-center gap-2">
          <div className="p-1 bg-indigo-100 dark:bg-indigo-900/30 rounded-full">
            <Wallet className="w-4 h-4 text-indigo-600 dark:text-indigo-400" />
          </div>
          Configurações Gerais
        </CardTitle>
      </CardHeader>
      <CardContent className="pt-2">
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="font-medium text-gray-900 dark:text-gray-100">Pagamento Parcelado</p>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Permitir parcelamento em até 12x
              </p>
            </div>
            <Switch defaultChecked={allowInstallments} />
          </div>
          <div className="flex items-center justify-between">
            <div>
              <p className="font-medium text-gray-900 dark:text-gray-100">Taxa de Conveniência</p>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Aplicar taxa adicional para cartão
              </p>
            </div>
            <Switch defaultChecked={convenienceFee}/>
          </div>
          <div className="flex items-center justify-between">
            <div>
              <p className="font-medium text-gray-900 dark:text-gray-100">Desconto para PIX</p>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                5% de desconto para pagamento via PIX
              </p>
            </div>
            <Switch defaultChecked={pixDiscount}/>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}