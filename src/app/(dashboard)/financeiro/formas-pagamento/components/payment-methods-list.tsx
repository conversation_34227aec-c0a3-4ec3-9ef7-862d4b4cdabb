'use client';

import { PaymentMethodCard } from './payment-method-card';
import { getPaymentMethodIcon } from '../icon-map';

interface RawPaymentMethod {
  id: string;
  name: string;
  slug: string;
  icon_name: string | null;
  enabled: boolean;
  transactions: number;
}

interface PaymentMethodsListProps {
  methods: RawPaymentMethod[];
}

export function PaymentMethodsList({ methods }: PaymentMethodsListProps) {
  return (
    <div className="grid gap-6 md:grid-cols-2 mb-6">
      {methods.map((pm) => {
        const Icon = pm.icon_name ? getPaymentMethodIcon(pm.icon_name) : undefined;

        // Fallback para quando não houver ícone reconhecido
        if (!Icon) return null;

        const method = {
          id: pm.id,
          name: pm.name,
          slug: pm.slug,
          Icon,
          iconClassName: '',
          enabled: pm.enabled,
          status: pm.enabled ? 'Ativo' : 'Inativo',
          fee: '',
          transactions: `${pm.transactions} este mês`,
        } as const;

        return <PaymentMethodCard key={method.id} method={method} />;
      })}
    </div>
  );
} 