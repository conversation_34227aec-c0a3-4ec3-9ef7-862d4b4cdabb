import { Metadata } from 'next';
import { PaymentPageHeader } from './components/page-header';
import { PaymentMethodsList } from './components/payment-methods-list';
import { getTenantPaymentMethods } from './actions/payment-method-actions';

export const metadata: Metadata = {
  title: 'Formas de Pagamento - Financeiro',
  description: 'Configure métodos de pagamento aceitos pela academia',
};

export default async function FormasPagamentoPage() {
  const paymentMethods = await getTenantPaymentMethods();

  return (
    <>
      <PaymentPageHeader />
      <PaymentMethodsList methods={paymentMethods} />
    </>
  );
} 