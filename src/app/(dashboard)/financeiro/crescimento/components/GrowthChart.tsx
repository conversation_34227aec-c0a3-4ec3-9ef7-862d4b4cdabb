'use client';

import { useEffect, useState } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { getGrowthMetrics, GrowthMetrics } from '../actions/growth-actions';
import { formatCurrency } from '@/utils/format-utils';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Cell } from 'recharts';

interface GrowthChartProps {
  viewMode: 'year-to-date' | 'projection';
}

export function GrowthChart({ viewMode }: GrowthChartProps) {
  const [metrics, setMetrics] = useState<GrowthMetrics | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchMetrics = async () => {
      setLoading(true);
      setError(null);

      const result = await getGrowthMetrics();

      if (result.success && result.data) {
        setMetrics(result.data);
      } else {
        setError(result.error || 'Erro ao carregar dados do gráfico');
      }

      setLoading(false);
    };

    fetchMetrics();
  }, []);

  const CustomTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-white/95 dark:bg-gray-900/95 backdrop-blur-sm p-4 border-0 rounded-xl shadow-2xl ring-1 ring-black/5 dark:ring-white/10">
          <p className="font-semibold text-gray-900 dark:text-gray-100 mb-3 text-sm">
            {data.month} {new Date().getFullYear()}
          </p>
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <div className={`w-3 h-3 rounded-full ${data.isProjected ? 'bg-gradient-to-r from-orange-500 to-amber-500' : 'bg-gradient-to-r from-emerald-500 to-green-500'}`}></div>
              <p className="text-sm text-gray-700 dark:text-gray-300">
                <span className="font-medium">{data.isProjected ? 'Projeção:' : 'Receita:'}</span>
                <span className={`font-semibold ml-1 ${data.isProjected ? 'text-orange-600 dark:text-orange-400' : 'text-emerald-600 dark:text-emerald-400'}`}>
                  {formatCurrency(data.revenue)}
                </span>
              </p>
            </div>
            {data.toReceive > 0 && (
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 rounded-full bg-gradient-to-r from-purple-500 to-violet-500"></div>
                <p className="text-sm text-gray-700 dark:text-gray-300">
                  <span className="font-medium">A Receber:</span>
                  <span className="font-semibold ml-1 text-purple-600 dark:text-purple-400">
                    {formatCurrency(data.toReceive)}
                  </span>
                </p>
              </div>
            )}
          </div>
        </div>
      );
    }
    return null;
  };

  if (loading) {
    return (
      <Card className="border-0 shadow-lg bg-gradient-to-br from-white to-gray-50/50 dark:from-gray-900 dark:to-gray-800/50 h-full flex flex-col">
        <CardHeader className="pb-4">
          <CardTitle className="text-gray-800 dark:text-gray-200 font-semibold">
            {viewMode === 'year-to-date' ? 'Receita Mensal (Ano-a-data)' : 'Projeção de Receita Mensal'}
          </CardTitle>
        </CardHeader>
        <CardContent className="flex-1 flex flex-col">
          <div className={`${viewMode === 'projection' ? 'h-[420px]' : 'h-80'} bg-gradient-to-r from-gray-100 via-gray-50 to-gray-100 dark:from-gray-800 dark:via-gray-700 dark:to-gray-800 animate-pulse rounded-xl relative overflow-hidden`}>
            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-shimmer"></div>
            <div className="flex items-end justify-between h-full p-6">
              {Array.from({ length: 12 }).map((_, i) => (
                <div
                  key={i}
                  className="bg-gray-200 dark:bg-gray-600 rounded-t animate-pulse"
                  style={{
                    height: `${Math.random() * 60 + 20}%`,
                    width: '6%',
                    animationDelay: `${i * 0.1}s`
                  }}
                />
              ))}
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error || !metrics) {
    return (
      <Card className="border-0 shadow-lg bg-gradient-to-br from-white to-gray-50/50 dark:from-gray-900 dark:to-gray-800/50 h-full flex flex-col">
        <CardHeader className="pb-4">
          <CardTitle className="text-gray-800 dark:text-gray-200 font-semibold">
            {viewMode === 'year-to-date' ? 'Receita Mensal (Ano-a-data)' : 'Projeção de Receita Mensal'}
          </CardTitle>
        </CardHeader>
        <CardContent className="flex-1 flex flex-col">
          <div className="flex flex-col items-center justify-center py-12 px-6">
            <div className="w-16 h-16 bg-gradient-to-br from-red-100 to-red-200 dark:from-red-900/30 dark:to-red-800/30 rounded-full flex items-center justify-center mb-4">
              <svg className="w-8 h-8 text-red-500 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
            <p className="text-red-600 dark:text-red-400 text-center font-medium">{error}</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Filtrar dados baseado no modo de visualização
  const currentMonth = new Date().getMonth() + 1;
  const revenueData = viewMode === 'year-to-date'
    ? metrics.monthlyRevenueData.slice(0, currentMonth)
    : metrics.monthlyRevenueData;

  // Combinar dados de receita com dados de "a receber"
  const chartData = revenueData.map((revenueItem) => {
    const toReceiveItem = metrics.monthlyToReceiveData.find(
      item => item.month === revenueItem.month
    );

    return {
      ...revenueItem,
      toReceive: toReceiveItem?.toReceive || 0
    };
  });

  const totalRevenue = chartData.reduce((sum, item) => sum + item.revenue, 0);
  const totalToReceive = chartData.reduce((sum, item) => sum + item.toReceive, 0);
  const realData = chartData.filter(item => !item.isProjected);
  const projectedData = chartData.filter(item => item.isProjected);

  return (
    <Card className="border-0 shadow-lg bg-gradient-to-br from-white to-gray-50/50 dark:from-gray-900 dark:to-gray-800/50 overflow-hidden h-full flex flex-col">
      <CardHeader className="pb-6 bg-gradient-to-r from-gray-50/50 to-transparent dark:from-gray-800/50 dark:to-transparent">
        <CardTitle className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
          <span className="text-xl font-semibold text-gray-800 dark:text-gray-200">
            {viewMode === 'year-to-date'
              ? `Receita e A Receber - ${new Date().getFullYear()} (até agora)`
              : `Projeção de Receita e A Receber - ${new Date().getFullYear()}`
            }
          </span>
          <div className="flex flex-wrap gap-4 text-sm">
            {viewMode === 'projection' && (
              <>
                <div className="flex items-center gap-2 px-3 py-1.5 bg-emerald-50 dark:bg-emerald-900/20 rounded-full">
                  <div className="w-2 h-2 rounded-full bg-gradient-to-r from-emerald-500 to-green-500"></div>
                  <span className="text-emerald-700 dark:text-emerald-300 font-medium">
                    Real: {formatCurrency(realData.reduce((sum, item) => sum + item.revenue, 0))}
                  </span>
                </div>
                <div className="flex items-center gap-2 px-3 py-1.5 bg-orange-50 dark:bg-orange-900/20 rounded-full">
                  <div className="w-2 h-2 rounded-full bg-gradient-to-r from-orange-500 to-amber-500"></div>
                  <span className="text-orange-700 dark:text-orange-300 font-medium">
                    Projeção: {formatCurrency(projectedData.reduce((sum, item) => sum + item.revenue, 0))}
                  </span>
                </div>
              </>
            )}
            <div className="flex items-center gap-2 px-3 py-1.5 bg-blue-50 dark:bg-blue-900/20 rounded-full">
              <div className="w-2 h-2 rounded-full bg-gradient-to-r from-blue-500 to-indigo-500"></div>
              <span className="text-blue-700 dark:text-blue-300 font-medium">
                Receita: {formatCurrency(totalRevenue)}
              </span>
            </div>
            <div className="flex items-center gap-2 px-3 py-1.5 bg-purple-50 dark:bg-purple-900/20 rounded-full">
              <div className="w-2 h-2 rounded-full bg-gradient-to-r from-purple-500 to-violet-500"></div>
              <span className="text-purple-700 dark:text-purple-300 font-medium">
                A Receber: {formatCurrency(totalToReceive)}
              </span>
            </div>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent className="pt-2 flex-1 flex flex-col">
        <div className="relative flex-1">
          <ResponsiveContainer width="100%" height="100%" minHeight={viewMode === 'projection' ? 420 : 350}>
            <BarChart
              data={chartData}
              margin={{ top: 30, right: 40, left: 20, bottom: 20 }}
              barCategoryGap="20%"
            >
              <defs>
                <linearGradient id="realGradient" x1="0" y1="0" x2="0" y2="1">
                  <stop offset="0%" stopColor="#10b981" stopOpacity={0.9} />
                  <stop offset="100%" stopColor="#059669" stopOpacity={0.7} />
                </linearGradient>
                <linearGradient id="projectedGradient" x1="0" y1="0" x2="0" y2="1">
                  <stop offset="0%" stopColor="#f59e0b" stopOpacity={0.9} />
                  <stop offset="100%" stopColor="#d97706" stopOpacity={0.7} />
                </linearGradient>
                <linearGradient id="toReceiveGradient" x1="0" y1="0" x2="0" y2="1">
                  <stop offset="0%" stopColor="#8b5cf6" stopOpacity={0.9} />
                  <stop offset="100%" stopColor="#7c3aed" stopOpacity={0.7} />
                </linearGradient>
              </defs>
              <CartesianGrid
                strokeDasharray="2 4"
                className="opacity-20 dark:opacity-10"
                stroke="#e5e7eb"
                vertical={false}
              />
              <XAxis
                dataKey="month"
                tick={{
                  fontSize: 13,
                  fill: 'currentColor',
                  fontWeight: 500
                }}
                className="text-gray-600 dark:text-gray-400"
                axisLine={false}
                tickLine={false}
                dy={10}
              />
              <YAxis
                tick={{
                  fontSize: 12,
                  fill: 'currentColor',
                  fontWeight: 500
                }}
                className="text-gray-600 dark:text-gray-400"
                axisLine={false}
                tickLine={false}
                dx={-10}
                tickFormatter={(value) => `R$ ${(value / 1000).toFixed(0)}k`}
              />
              <Tooltip
                content={<CustomTooltip />}
                cursor={{ fill: 'rgba(0, 0, 0, 0.05)', radius: 8 }}
              />

              <Bar
                dataKey="revenue"
                radius={[6, 6, 0, 0]}
                className="drop-shadow-sm"
                name="Receita"
                animationDuration={800}
                animationBegin={0}
              >
                {chartData.map((entry, index) => (
                  <Cell
                    key={`cell-revenue-${index}`}
                    fill={entry.isProjected ? "url(#projectedGradient)" : "url(#realGradient)"}
                  />
                ))}
              </Bar>
              <Bar
                dataKey="toReceive"
                radius={[6, 6, 0, 0]}
                className="drop-shadow-sm"
                name="A Receber"
                animationDuration={800}
                animationBegin={200}
                fill="url(#toReceiveGradient)"
              />
            </BarChart>
          </ResponsiveContainer>
        </div>
      </CardContent>
    </Card>
  );
}
