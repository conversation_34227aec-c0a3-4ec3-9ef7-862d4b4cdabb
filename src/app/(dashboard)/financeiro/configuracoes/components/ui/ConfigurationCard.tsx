import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { LucideIcon } from 'lucide-react';

interface ConfigurationCardProps {
  title: string;
  icon: LucideIcon;
  iconColor: string;
  children: React.ReactNode;
  buttonText: string;
  onButtonClick?: () => void;
}

export function ConfigurationCard({
  title,
  icon: Icon,
  iconColor,
  children,
  buttonText,
  onButtonClick
}: ConfigurationCardProps) {
  // Determina a configuração de cores baseada na cor do ícone
  const getColorConfig = () => {
    if (iconColor.includes('blue')) {
      return {
        header: 'from-blue-50/50 to-transparent dark:from-blue-900/20 dark:to-transparent',
        iconBg: 'bg-blue-100 dark:bg-blue-900/30',
        iconColor: 'text-blue-600 dark:text-blue-400'
      };
    } else if (iconColor.includes('green')) {
      return {
        header: 'from-emerald-50/50 to-transparent dark:from-emerald-900/20 dark:to-transparent',
        iconBg: 'bg-emerald-100 dark:bg-emerald-900/30',
        iconColor: 'text-emerald-600 dark:text-emerald-400'
      };
    } else if (iconColor.includes('orange')) {
      return {
        header: 'from-orange-50/50 to-transparent dark:from-orange-900/20 dark:to-transparent',
        iconBg: 'bg-orange-100 dark:bg-orange-900/30',
        iconColor: 'text-orange-600 dark:text-orange-400'
      };
    } else if (iconColor.includes('purple')) {
      return {
        header: 'from-purple-50/50 to-transparent dark:from-purple-900/20 dark:to-transparent',
        iconBg: 'bg-purple-100 dark:bg-purple-900/30',
        iconColor: 'text-purple-600 dark:text-purple-400'
      };
    } else {
      return {
        header: 'from-gray-50/50 to-transparent dark:from-gray-900/20 dark:to-transparent',
        iconBg: 'bg-gray-100 dark:bg-gray-900/30',
        iconColor: 'text-gray-600 dark:text-gray-400'
      };
    }
  };

  const colorConfig = getColorConfig();

  return (
    <Card className="flex flex-col h-full border-0 shadow-lg bg-gradient-to-br from-white to-gray-50/50 dark:from-gray-900 dark:to-gray-800/50 overflow-hidden group hover:shadow-xl transition-all duration-300">
      <CardHeader className={`pb-4 bg-gradient-to-r ${colorConfig.header}`}>
        <CardTitle className="text-sm font-semibold text-gray-700 dark:text-gray-300 flex items-center gap-2">
          <div className={`p-1 ${colorConfig.iconBg} rounded-full`}>
            <Icon className={`w-4 h-4 ${colorConfig.iconColor}`} />
          </div>
          {title}
        </CardTitle>
      </CardHeader>
      <CardContent className="flex flex-col flex-1 space-y-4 pt-2">
        <div className="flex-1 space-y-4">
          {children}
        </div>
        <Button variant="outline" className="w-full mt-auto" onClick={onButtonClick}>
          {buttonText}
        </Button>
      </CardContent>
    </Card>
  );
}