'use client';

import { useState } from 'react';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ConfigurationModal } from './ConfigurationModal';

interface NotificationsModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export function NotificationsModal({ isOpen, onClose }: NotificationsModalProps) {
  const [billingReminder, setBillingReminder] = useState(true);
  const [overduePayments, setOverduePayments] = useState(true);
  const [reminderDays, setReminderDays] = useState('3');
  const [emailNotifications, setEmailNotifications] = useState(true);
  const [isLoading, setIsLoading] = useState(false);

  const handleSave = async () => {
    setIsLoading(true);
    try {
      // TODO: Implementar lógica de salvamento
      await new Promise(resolve => setTimeout(resolve, 1000)); // Simular API call
      console.log('Salvando notificações:', {
        billingReminder,
        overduePayments,
        reminderDays,
        emailNotifications
      });
      onClose();
    } catch (error) {
      console.error('Erro ao salvar notificações:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <ConfigurationModal
      isOpen={isOpen}
      onClose={onClose}
      title="Configurações de Notificações"
      onSave={handleSave}
      isLoading={isLoading}
    >
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div className="space-y-0.5">
            <Label>Lembrete de Cobrança</Label>
            <p className="text-sm text-muted-foreground">
              Enviar lembretes antes do vencimento
            </p>
          </div>
          <Switch
            checked={billingReminder}
            onCheckedChange={setBillingReminder}
          />
        </div>

        {billingReminder && (
          <div className="space-y-2 ml-4">
            <Label htmlFor="reminder-days">Dias antes do vencimento</Label>
            <Select value={reminderDays} onValueChange={setReminderDays}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="1">1 dia</SelectItem>
                <SelectItem value="3">3 dias</SelectItem>
                <SelectItem value="5">5 dias</SelectItem>
                <SelectItem value="7">7 dias</SelectItem>
              </SelectContent>
            </Select>
          </div>
        )}

        <div className="flex items-center justify-between">
          <div className="space-y-0.5">
            <Label>Pagamentos em Atraso</Label>
            <p className="text-sm text-muted-foreground">
              Notificar sobre pagamentos vencidos
            </p>
          </div>
          <Switch
            checked={overduePayments}
            onCheckedChange={setOverduePayments}
          />
        </div>

        <div className="flex items-center justify-between">
          <div className="space-y-0.5">
            <Label>Notificações por Email</Label>
            <p className="text-sm text-muted-foreground">
              Receber notificações por email
            </p>
          </div>
          <Switch
            checked={emailNotifications}
            onCheckedChange={setEmailNotifications}
          />
        </div>
      </div>
    </ConfigurationModal>
  );
}