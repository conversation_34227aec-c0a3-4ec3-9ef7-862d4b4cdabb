import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';

interface ConfigurationModalProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  children: React.ReactNode;
  onSave?: () => void;
  saveButtonText?: string;
  isLoading?: boolean;
}

export function ConfigurationModal({
  isOpen,
  onClose,
  title,
  children,
  onSave,
  saveButtonText = 'Salvar',
  isLoading = false
}: ConfigurationModalProps) {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <div className="flex items-center justify-between">
            <DialogTitle>{title}</DialogTitle>
          </div>
        </DialogHeader>
        
        <div className="py-4">
          {children}
        </div>
        
        {onSave && (
          <div className="flex justify-end space-x-2 pt-4 border-t">
            <Button variant="outline" onClick={onClose} disabled={isLoading}>
              Cancelar
            </Button>
            <Button onClick={onSave} disabled={isLoading}>
              {isLoading ? 'Salvando...' : saveButtonText}
            </Button>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}