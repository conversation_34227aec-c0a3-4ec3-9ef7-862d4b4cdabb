'use client';

import { useState } from 'react';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ConfigurationModal } from './ConfigurationModal';

interface IntegrationsModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export function IntegrationsModal({ isOpen, onClose }: IntegrationsModalProps) {
  const [paymentGateway, setPaymentGateway] = useState('');
  const [apiKey, setApiKey] = useState('');
  const [secretKey, setSecretKey] = useState('');
  const [accountingSystem, setAccountingSystem] = useState('');
  const [accountingApiKey, setAccountingApiKey] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const handleSave = async () => {
    setIsLoading(true);
    try {
      // TODO: Implementar lógica de salvamento
      await new Promise(resolve => setTimeout(resolve, 1000)); // Simular API call
      console.log('Salvando integrações:', {
        paymentGateway,
        apiKey,
        secretKey,
        accountingSystem,
        accountingApiKey
      });
      onClose();
    } catch (error) {
      console.error('Erro ao salvar integrações:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <ConfigurationModal
      isOpen={isOpen}
      onClose={onClose}
      title="Configurações de Integrações"
      onSave={handleSave}
      isLoading={isLoading}
    >
      <Tabs defaultValue="payment" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="payment">Gateway de Pagamento</TabsTrigger>
          <TabsTrigger value="accounting">Sistema Contábil</TabsTrigger>
        </TabsList>
        
        <TabsContent value="payment" className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="payment-gateway">Gateway de Pagamento</Label>
            <Select value={paymentGateway} onValueChange={setPaymentGateway}>
              <SelectTrigger>
                <SelectValue placeholder="Selecione o gateway" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="stripe">Stripe</SelectItem>
                <SelectItem value="mercadopago">Mercado Pago</SelectItem>
                <SelectItem value="pagseguro">PagSeguro</SelectItem>
                <SelectItem value="paypal">PayPal</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {paymentGateway && (
            <>
              <div className="space-y-2">
                <Label htmlFor="api-key">Chave da API</Label>
                <Input
                  id="api-key"
                  type="password"
                  value={apiKey}
                  onChange={(e) => setApiKey(e.target.value)}
                  placeholder="Digite sua chave da API"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="secret-key">Chave Secreta</Label>
                <Input
                  id="secret-key"
                  type="password"
                  value={secretKey}
                  onChange={(e) => setSecretKey(e.target.value)}
                  placeholder="Digite sua chave secreta"
                />
              </div>
            </>
          )}
        </TabsContent>
        
        <TabsContent value="accounting" className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="accounting-system">Sistema Contábil</Label>
            <Select value={accountingSystem} onValueChange={setAccountingSystem}>
              <SelectTrigger>
                <SelectValue placeholder="Selecione o sistema" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="quickbooks">QuickBooks</SelectItem>
                <SelectItem value="xero">Xero</SelectItem>
                <SelectItem value="sage">Sage</SelectItem>
                <SelectItem value="contabilizei">Contabilizei</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {accountingSystem && (
            <div className="space-y-2">
              <Label htmlFor="accounting-api-key">Chave da API</Label>
              <Input
                id="accounting-api-key"
                type="password"
                value={accountingApiKey}
                onChange={(e) => setAccountingApiKey(e.target.value)}
                placeholder="Digite sua chave da API"
              />
            </div>
          )}
        </TabsContent>
      </Tabs>
    </ConfigurationModal>
  );
}