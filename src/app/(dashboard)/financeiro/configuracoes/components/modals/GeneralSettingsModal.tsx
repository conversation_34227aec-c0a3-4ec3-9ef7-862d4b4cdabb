'use client';

import { useState, useEffect } from 'react';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ConfigurationModal } from './ConfigurationModal';
import { saveGeneralSettings, getGeneralSettings, type GeneralSettingsData } from '../../actions';
import { toast } from 'sonner';
import { CURRENCY_OPTIONS, DUE_DAY_OPTIONS } from '../../utils/formatters';

interface GeneralSettingsModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess?: (data: GeneralSettingsData) => void;
}

export function GeneralSettingsModal({ isOpen, onClose, onSuccess }: GeneralSettingsModalProps) {
  const [currency, setCurrency] = useState('BRL');
  const [defaultDueDay, setDefaultDueDay] = useState('enrollment_date');
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingData, setIsLoadingData] = useState(false);

  // Carregar configurações existentes quando o modal abrir
  useEffect(() => {
    if (isOpen) {
      loadSettings();
    }
  }, [isOpen]);

  const loadSettings = async () => {
    setIsLoadingData(true);
    try {
      const result = await getGeneralSettings();
      if (result.success && result.data) {
        const data = result.data as GeneralSettingsData;
        setCurrency(data.currency);
        setDefaultDueDay(data.defaultDueDay);
      } else {
        console.error('Erro ao carregar configurações:', result.errors);
        toast.error('Erro ao carregar configurações');
      }
    } catch (error) {
      console.error('Erro ao carregar configurações:', error);
      toast.error('Erro ao carregar configurações');
    } finally {
      setIsLoadingData(false);
    }
  };

  const handleSave = async () => {
    setIsLoading(true);
    try {
      const result = await saveGeneralSettings({
        currency,
        defaultDueDay,
      });

      if (result.success) {
        toast.success('Configurações salvas com sucesso!');

        // Chamar callback de sucesso com os dados atualizados
        if (onSuccess) {
          onSuccess({
            currency,
            defaultDueDay,
          });
        }

        onClose();
      } else {
        console.error('Erro ao salvar configurações:', result.errors);
        toast.error(result.errors?._form || 'Erro ao salvar configurações');
      }
    } catch (error) {
      console.error('Erro ao salvar configurações:', error);
      toast.error('Erro interno do servidor');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <ConfigurationModal
      isOpen={isOpen}
      onClose={onClose}
      title="Configurações Gerais"
      onSave={handleSave}
      isLoading={isLoading}
    >
      {isLoadingData ? (
        <div className="space-y-4">
          <div className="space-y-2">
            <div className="h-4 bg-gray-200 rounded animate-pulse"></div>
            <div className="h-10 bg-gray-200 rounded animate-pulse"></div>
          </div>
          <div className="space-y-2">
            <div className="h-4 bg-gray-200 rounded animate-pulse"></div>
            <div className="h-10 bg-gray-200 rounded animate-pulse"></div>
          </div>
        </div>
      ) : (
        <div className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="currency">Moeda Padrão</Label>
            <Select value={currency} onValueChange={setCurrency}>
              <SelectTrigger>
                <SelectValue placeholder="Selecione a moeda" />
              </SelectTrigger>
              <SelectContent>
                {CURRENCY_OPTIONS.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="default-due-day">Dia de Vencimento Padrão</Label>
            <Select value={defaultDueDay} onValueChange={setDefaultDueDay}>
              <SelectTrigger>
                <SelectValue placeholder="Selecione o dia do mês" />
              </SelectTrigger>
              <SelectContent>
                {DUE_DAY_OPTIONS.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>
      )}
    </ConfigurationModal>
  );
}