'use client';

import { useState } from 'react';
import { CreditCard } from 'lucide-react';
import { ConfigurationCard } from '../ui/ConfigurationCard';
import { StatusItem } from '../ui/StatusItem';
import { IntegrationsModal } from '../modals/IntegrationsModal';

export function IntegrationsCard() {
    const [isModalOpen, setIsModalOpen] = useState(false);

    const handleConfigureIntegrations = () => {
        setIsModalOpen(true);
    };

    return (
        <>
            <ConfigurationCard
                title="Integrações"
                icon={CreditCard}
                iconColor="text-green-600"
                buttonText="Configurar Integrações"
                onButtonClick={handleConfigureIntegrations}
            >
                <StatusItem
                    label="Gateway de Pagamento"
                    status="Não configurado"
                    statusColor="gray"
                />
                <StatusItem
                    label="Sistema Contábil"
                    status="Não configurado"
                    statusColor="gray"
                />
            </ConfigurationCard>

            <IntegrationsModal
                isOpen={isModalOpen}
                onClose={() => setIsModalOpen(false)}
            />
        </>
    );
}