'use client';

import { useState } from 'react';
import { Bell } from 'lucide-react';
import { ConfigurationCard } from '../ui/ConfigurationCard';
import { StatusItem } from '../ui/StatusItem';
import { NotificationsModal } from '../modals/NotificationsModal';

export function NotificationsCard() {
  const [isModalOpen, setIsModalOpen] = useState(false);

  const handleManageNotifications = () => {
    setIsModalOpen(true);
  };

  return (
    <>
      <ConfigurationCard
        title="Notificações"
        icon={Bell}
        iconColor="text-orange-600"
        buttonText="Gerenciar Notificações"
        onButtonClick={handleManageNotifications}
      >
        <StatusItem 
          label="Lembrete de Cobrança" 
          status="Ativo" 
          statusColor="green" 
        />
        <StatusItem 
          label="Pagamentos em Atraso" 
          status="Ativo" 
          statusColor="green" 
        />
      </ConfigurationCard>

      <NotificationsModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
      />
    </>
  );
}