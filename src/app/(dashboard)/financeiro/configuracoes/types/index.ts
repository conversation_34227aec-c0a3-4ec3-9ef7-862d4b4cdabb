// Importar tipos das actions
import type { GeneralSettingsData } from '../actions/general-settings-actions';

// Re-exportar tipos das actions
export type {
  GeneralSettingsInput,
  GeneralSettingsData
} from '../actions/general-settings-actions';

// Tipos específicos para componentes
export interface GeneralSettingsCardProps {
  className?: string;
}

export interface GeneralSettingsModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess?: (data: GeneralSettingsData) => void;
}

// Tipos para formatadores
export interface CurrencyOption {
  value: string;
  label: string;
}

export interface DueDayOption {
  value: string;
  label: string;
}
