import { Metadata } from 'next';
import { 
  PageHeader, 
  GeneralSettingsCard, 
  NotificationsCard,
  IntegrationsCard,
  ReportsCard 
} from './components';

export const metadata: Metadata = {
  title: 'Configurações - Financeiro',
  description: 'Configurações do módulo financeiro',
};

export default function ConfiguracoesPage() {
  return (
    <>
      <PageHeader 
        title="Configurações Financeiras"
        description="Configure parâmetros financeiros, integrações e preferências do sistema."
      />

      <div className="grid gap-6 md:grid-cols-2">
        <GeneralSettingsCard />
        <NotificationsCard />
        {/* <IntegrationsCard />
        <ReportsCard /> */}
      </div>
    </>
  );
} 