import { useState, useCallback } from 'react';
import { getAllPayments } from '@/services/billing/payment-actions';
import { PaymentFilterState } from '../components/types';

export function usePaymentSearch() {
  const [searchText, setSearchText] = useState('');
  const [isSearching, setIsSearching] = useState(false);

  const performSearch = useCallback(async (
    searchValue: string,
    offset = 0,
    filters?: PaymentFilterState
  ) => {
    try {
      setIsSearching(true);

      // Construir parâmetros de busca com base nos filtros
      const searchParams: any = {
        limit: 20,
        offset,
        searchText: searchValue.trim() || undefined
      };

      // Aplicar filtros se fornecidos
      if (filters) {
        if (filters.status && filters.status.length > 0) {
          // Suporte a múltiplos status
          searchParams.status = filters.status.length === 1 ? filters.status[0] : filters.status;
        }

        if (filters.paymentType && filters.paymentType.length > 0) {
          // Suporte a múltiplos tipos
          searchParams.paymentType = filters.paymentType.length === 1 ? filters.paymentType[0] : filters.paymentType;
        }

        if (filters.paymentMethod && filters.paymentMethod.length > 0) {
          // Suporte a múltiplos métodos
          searchParams.paymentMethod = filters.paymentMethod.length === 1 ? filters.paymentMethod[0] : filters.paymentMethod;
        }

        if (filters.minAmount) {
          searchParams.minAmount = filters.minAmount;
        }

        if (filters.maxAmount) {
          searchParams.maxAmount = filters.maxAmount;
        }

        if (filters.startDate) {
          searchParams.startDate = filters.startDate.toISOString().split('T')[0];
        }

        if (filters.endDate) {
          searchParams.endDate = filters.endDate.toISOString().split('T')[0];
        }
      }
      // Removido o comportamento padrão que forçava apenas status 'paid'
      // Agora permite buscar todos os status quando não há filtros específicos

      const result = await getAllPayments(searchParams);

      return result;
    } catch (error) {
      console.error('Erro ao buscar pagamentos:', error);
      return {
        success: false,
        error: 'Erro interno ao buscar pagamentos',
        data: null,
        hasMore: false
      };
    } finally {
      setIsSearching(false);
    }
  }, []);

  const handleSearchChange = useCallback((value: string) => {
    setSearchText(value);
  }, []);

  const loadMore = useCallback((offset: number, filters?: PaymentFilterState) => {
    return performSearch(searchText, offset, filters);
  }, [searchText, performSearch]);

  return {
    searchText,
    isSearching,
    handleSearchChange,
    loadMore,
    performSearch
  };
}
