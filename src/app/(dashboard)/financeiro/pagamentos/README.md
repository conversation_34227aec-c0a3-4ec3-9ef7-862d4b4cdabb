# Sistema Financeiro - Receitas e Despesas

## 📋 Visão Geral

O sistema financeiro foi expandido para suportar tanto **receitas** (pagamentos de alunos) quanto **despesas** (pagamentos feitos pela academia). A interface utiliza um sistema de tabs para alternar entre os dois tipos de transações.

## 🏗️ Arquitetura

### Componentes Principais

#### 1. **FinancialClient**
- Componente principal que gerencia o estado das tabs
- Controla qual tipo de transação está sendo exibida
- Coordena a comunicação entre os sub-componentes

#### 2. **FinancialTabs**
- Sistema de navegação por tabs (Receitas/Despesas)
- Interface visual consistente com ícones específicos
- Transições suaves entre as seções

#### 3. **FinancialMetrics**
- Componente genérico para exibir métricas financeiras
- Reutilizável para receitas e despesas
- Suporte a loading states e tratamento de erros

#### 4. **FinancialFilters**
- Filtros adaptativos baseados no tipo de transação
- Busca contextual (por aluno ou fornecedor)
- Botão de adicionar despesa quando aplicável

#### 5. **FinancialTransactionItem**
- Item de transação unificado
- Suporte a receitas e despesas
- Exibição de categorias para despesas
- Links para perfis de alunos

### Tipos TypeScript

```typescript
// Tipos base
export type TransactionType = 'income' | 'expense';
export type FinancialTab = 'income' | 'expense';

// Transações
export interface IncomeTransaction extends BaseTransaction {
  transactionType: 'income';
  studentName: string;
  studentUserId?: string;
}

export interface ExpenseTransaction extends BaseTransaction {
  transactionType: 'expense';
  supplierName: string;
  categoryName?: string;
  categoryColor?: string;
}

// União dos tipos
export type Transaction = IncomeTransaction | ExpenseTransaction;
```

## 🎨 Interface do Usuário

### Layout Principal
```
┌─────────────────────────────────────────┐
│ Financeiro                              │
├─────────────────────────────────────────┤
│ [📈 Receitas] [📉 Despesas]             │
├─────────────────────────────────────────┤
│ Métricas Financeiras                    │
│ ┌─────────┐ ┌─────────┐ ┌─────────┐    │
│ │Total    │ │Pagas    │ │Pendentes│    │
│ │R$ 15.000│ │   45    │ │   12    │    │
│ └─────────┘ └─────────┘ └─────────┘    │
├─────────────────────────────────────────┤
│ Filtros e Busca                        │
├─────────────────────────────────────────┤
│ Lista de Transações                     │
└─────────────────────────────────────────┘
```

### Funcionalidades por Tab

#### Tab Receitas
- Lista de pagamentos de alunos
- Métricas: Total recebido, pagamentos realizados, pendentes, em atraso
- Busca por nome do aluno
- Links para perfil do aluno

#### Tab Despesas
- Lista de despesas da academia
- Métricas: Total gasto, despesas pagas, pendentes, vencidas
- Busca por fornecedor
- Categorização visual com cores
- Botão para adicionar nova despesa

## 🔧 Implementação

### Estrutura de Arquivos
```
src/app/(dashboard)/financeiro/pagamentos/
├── components/
│   ├── FinancialClient/
│   ├── FinancialTabs/
│   ├── FinancialMetrics/
│   ├── FinancialFilters/
│   ├── FinancialTransactionItem/
│   ├── types.ts (expandido)
│   └── index.ts (atualizado)
├── mock-data.ts (dados de exemplo)
├── page.tsx (atualizado)
└── README.md
```

### Compatibilidade
- ✅ Mantém compatibilidade total com código existente
- ✅ Componentes antigos continuam funcionando
- ✅ Tipos antigos são estendidos, não substituídos
- ✅ Funcionalidade de receitas permanece inalterada

## 📊 Dados Mock

Para demonstração, foram criados dados mock para despesas incluindo:

### Categorias de Despesas
- **Recursos Humanos**: Salários, encargos e benefícios
- **Infraestrutura**: Aluguel, utilities e manutenção  
- **Equipamentos**: Tatames, uniformes e materiais
- **Marketing**: Publicidade e promoção
- **Operacional**: Seguros, taxas e outros

### Exemplos de Despesas
- Salário de instrutor (R$ 3.500,00)
- Aluguel da academia (R$ 4.500,00)
- Compra de tatames (R$ 2.800,00)
- Publicidade online (R$ 800,00)
- Seguro anual (R$ 450,00)

## 🚀 Próximos Passos

### Fase 2: Backend
- [ ] Criar tabelas `expenses` e `expense_categories`
- [ ] Implementar actions para CRUD de despesas
- [ ] Criar APIs para métricas de despesas

### Fase 3: Funcionalidades Avançadas
- [ ] Formulário de adição/edição de despesas
- [ ] Sistema de categorias personalizáveis
- [ ] Despesas recorrentes
- [ ] Relatórios consolidados
- [ ] Exportação de dados

### Fase 4: Melhorias
- [ ] Dashboard de fluxo de caixa
- [ ] Gráficos e visualizações
- [ ] Notificações de vencimento
- [ ] Integração com sistemas de pagamento

## 🎯 Benefícios

1. **Gestão Completa**: Visão 360° das finanças da academia
2. **Interface Familiar**: Mantém a experiência atual para receitas
3. **Escalabilidade**: Estrutura preparada para futuras funcionalidades
4. **Reutilização**: Máximo aproveitamento de componentes existentes
5. **Flexibilidade**: Fácil expansão para novos tipos de transações
