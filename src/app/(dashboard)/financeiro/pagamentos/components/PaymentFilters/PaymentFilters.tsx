'use client';

import { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Search, Download, Filter } from 'lucide-react';
import { PaymentFiltersProps, PaymentFilterState, PaymentMethodOption } from '../types';
import { PaymentFilterPopover } from './PaymentFilterPopover';

interface ExtendedPaymentFiltersProps extends PaymentFiltersProps {
  filters?: PaymentFilterState;
  onFilterChange?: (filters: PaymentFilterState) => void;
  onClearFilters?: () => void;
  paymentMethods?: PaymentMethodOption[];
}

export function PaymentFilters({
  onFiltersClick,
  onSearchChange,
  onExportClick,
  filters = {},
  onFilterChange,
  onClearFilters,
  paymentMethods = [],
}: ExtendedPaymentFiltersProps = {}) {
  const [searchValue, setSearchValue] = useState('');

  const handleSearchChange = (value: string) => {
    setSearchValue(value);
    onSearchChange?.(value);
  };

  const handleExportClick = () => {
    onExportClick?.() || console.log('Exportar dados');
  };

  const handleFilterChange = (newFilters: PaymentFilterState) => {
    onFilterChange?.(newFilters);
  };

  return (
    <Card className="mb-6 border-0 shadow-lg bg-gradient-to-br from-white to-gray-50/50 dark:from-gray-900 dark:to-gray-800/50 overflow-hidden">
      <CardContent className="p-6 bg-gradient-to-r from-blue-50/30 to-transparent dark:from-blue-900/10 dark:to-transparent">
        <div className="flex flex-col sm:flex-row gap-4 items-center justify-between">
          <div className="flex gap-3 items-center flex-1">
            {onFilterChange ? (
              <PaymentFilterPopover
                filters={filters}
                onFilterChange={handleFilterChange}
                onClearFilters={onClearFilters}
                paymentMethods={paymentMethods}
              />
            ) : (
              <Button
                variant="outline"
                size="sm"
                onClick={onFiltersClick}
                className="bg-gradient-to-r from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 border-blue-200 dark:border-blue-700 text-blue-700 dark:text-blue-300 hover:bg-gradient-to-r hover:from-blue-100 hover:to-blue-200 dark:hover:from-blue-900/30 dark:hover:to-blue-800/30 transition-all duration-200"
              >
                <div className="p-1 bg-blue-100 dark:bg-blue-900/30 rounded-full mr-2">
                  <Filter className="h-3 w-3 text-blue-600 dark:text-blue-400" />
                </div>
                Filtros
              </Button>
            )}
            <div className="relative flex-1 max-w-sm">
              <div className="absolute left-3 top-1/2 transform -translate-y-1/2 p-1 bg-gray-100 dark:bg-gray-700 rounded-full">
                <Search className="h-3 w-3 text-gray-600 dark:text-gray-400" />
              </div>
              <Input
                placeholder="Buscar por aluno"
                value={searchValue}
                onChange={(e) => handleSearchChange(e.target.value)}
                className="pl-12 bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700 focus:border-blue-400 dark:focus:border-blue-500 transition-colors duration-200 shadow-sm"
              />
            </div>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={handleExportClick}
            disabled
            className="bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-800/20 dark:to-gray-700/20 border-gray-200 dark:border-gray-700 text-gray-500 dark:text-gray-400 shadow-sm"
          >
            <div className="p-1 bg-gray-100 dark:bg-gray-700 rounded-full mr-2">
              <Download className="h-3 w-3 text-gray-500 dark:text-gray-400" />
            </div>
            Exportar
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
