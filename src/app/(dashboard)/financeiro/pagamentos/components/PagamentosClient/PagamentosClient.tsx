'use client';

import { useState, useTransition, useEffect } from 'react';
import {
  PaymentFilters,
  TransactionsList,
  PaymentMetric,
  Transaction,
  PaymentMethodOption,
} from '../';
import { usePaymentSearch } from '../../hooks/usePaymentSearch';
import { usePaymentFilters } from '@/hooks/financeiro/use-payment-filters';
import { getPaymentMethods } from '../../actions';

interface PagamentosClientProps {
  metrics: PaymentMetric[];
  transactions: Transaction[];
  hasMore?: boolean;
}

/**
 * Converter dados de pagamentos do banco para o formato do componente
 */
function convertPaymentsToTransactions(payments: any[]): Transaction[] {
  return payments.map((payment, index) => ({
    id: payment.id || index, // Usar ID do payment ou índice como fallback
    studentName: payment.students?.users?.full_name || 'Nome não disponível',
    studentUserId: payment.students?.user_id,
    type: getPaymentTypeLabel(payment.payment_type),
    paymentMethod: getPaymentMethodLabel(payment.payment_method),
    date: formatDate(payment.due_date || payment.created_at), // Usar due_date se disponível, senão created_at
    amount: formatCurrency(parseFloat(payment.amount)),
    status: getStatusLabel(payment.status),
    paidAt: payment.paid_at ? formatDate(payment.paid_at) : undefined, // Data de pagamento
    dueDate: payment.due_date ? formatDate(payment.due_date) : undefined, // Data de vencimento
  }));
}

/**
 * Obter label do tipo de pagamento
 */
function getPaymentTypeLabel(type: string): string {
  const labels: Record<string, string> = {
    recurring: 'Recorrente',
    signup_fee: 'Taxa de Matrícula',
    graduation_fee: 'Taxa de Graduação',
    late_fee: 'Multa por Atraso',
    cancellation_fee: 'Taxa de Cancelamento',
    // manual: 'Cobrança Manual',
    // product: 'Produto/Serviço',
    initial_payment: 'Pagamento Unico'
  };
  return labels[type] || type;
}

/**
 * Obter label do método de pagamento
 */
function getPaymentMethodLabel(method: string | null): string {
  if (!method) return 'Não definido';

  const labels: Record<string, string> = {
    pix: 'PIX',
    credit_card: 'Cartão de Crédito',
    debit_card: 'Cartão de Débito',
    cash: 'Dinheiro',
    bank_transfer: 'Transferência Bancária'
  };
  return labels[method] || method;
}

/**
 * Obter label do status
 */
function getStatusLabel(status: string): string {
  const labels: Record<string, string> = {
    pending: 'Pendente',
    paid: 'Pago',
    overdue: 'Vencido',
    canceled: 'Cancelado',
    awaiting_confirmation: 'Aguardando Confirmação'
  };
  return labels[status] || status;
}

/**
 * Formatar data para exibição (timezone do Brasil)
 */
function formatDate(dateString: string): string {
  if (!dateString) return 'Data não disponível';

  // Se for apenas uma data (YYYY-MM-DD), adicionar horário para evitar problemas de timezone
  const dateToFormat = dateString.includes('T') ? dateString : `${dateString}T12:00:00.000Z`;

  const date = new Date(dateToFormat);

  // Verificar se a data é válida
  if (isNaN(date.getTime())) {
    return 'Data inválida';
  }

  return date.toLocaleDateString('pt-BR', {
    timeZone: 'America/Sao_Paulo',
    day: '2-digit',
    month: '2-digit',
    year: 'numeric'
  });
}

/**
 * Formatar valor monetário para exibição
 */
function formatCurrency(value: number): string {
  return new Intl.NumberFormat('pt-BR', {
    style: 'currency',
    currency: 'BRL',
  }).format(value);
}

export function PagamentosClient({ metrics, transactions: initialTransactions, hasMore: initialHasMore = true }: PagamentosClientProps) {
  const [transactions, setTransactions] = useState<Transaction[]>(initialTransactions);
  const [currentOffset, setCurrentOffset] = useState(20); // Começar do próximo lote
  const [hasMore, setHasMore] = useState(initialHasMore); // Usar valor inicial do servidor
  const [isPending, startTransition] = useTransition();
  const [paymentMethods, setPaymentMethods] = useState<PaymentMethodOption[]>([]);

  const { searchText, handleSearchChange, performSearch } = usePaymentSearch();
  const { filters, updateFilters, clearFilters } = usePaymentFilters();

  // Carregar formas de pagamento
  useEffect(() => {
    const loadPaymentMethods = async () => {
      const methods = await getPaymentMethods();
      setPaymentMethods(methods);
    };
    loadPaymentMethods();
  }, []);

  // Debounce manual para busca
  const [searchTimeout, setSearchTimeout] = useState<NodeJS.Timeout | null>(null);

  const performSearchWithFilters = async (searchValue: string, offset = 0) => {
    return await performSearch(searchValue, offset, filters);
  };

  const handleSearchChangeWithDebounce = (value: string) => {
    handleSearchChange(value);

    // Limpar timeout anterior
    if (searchTimeout) {
      clearTimeout(searchTimeout);
    }

    // Se o valor estiver vazio e não há filtros ativos, restaurar transações iniciais
    const hasActiveFilters = filters.status?.length || filters.paymentMethod?.length ||
                            filters.paymentType?.length || filters.minAmount ||
                            filters.maxAmount || filters.startDate || filters.endDate;

    if (!value.trim() && !hasActiveFilters) {
      setTransactions(initialTransactions);
      setCurrentOffset(20);
      setHasMore(initialHasMore);
      return;
    }

    // Criar novo timeout para busca com filtros
    const newTimeout = setTimeout(() => {
      startTransition(async () => {
        const result = await performSearchWithFilters(value);
        if (result.success && result.data) {
          const newTransactions = convertPaymentsToTransactions(result.data);
          setTransactions(newTransactions);
          setCurrentOffset(20); // Reset para próximo lote
          setHasMore(result.hasMore || false);
        }
      });
    }, 500);

    setSearchTimeout(newTimeout);
  };

  const handleLoadMore = () => {
    startTransition(async () => {
      try {
        console.log('Carregando mais com searchText:', searchText, 'offset:', currentOffset, 'filtros:', filters);
        // Usar performSearchWithFilters que inclui os filtros atuais
        const result = await performSearchWithFilters(searchText, currentOffset);
        console.log('Resultado do carregar mais:', result);

        if (result.success && result.data) {
          const newTransactions = convertPaymentsToTransactions(result.data);
          console.log('Novas transações carregadas:', newTransactions);
          setTransactions(prev => [...prev, ...newTransactions]);
          setCurrentOffset(prev => prev + 20);
          setHasMore(result.hasMore || false);
        }
      } catch (error) {
        console.error('Erro ao carregar mais pagamentos:', error);
        setHasMore(false);
      }
    });
  };

  const handleFilterChange = (newFilters: any) => {
    updateFilters(newFilters);

    // Aplicar filtros imediatamente
    startTransition(async () => {
      try {
        const result = await performSearch(searchText, 0, newFilters);
        if (result.success && result.data) {
          const newTransactions = convertPaymentsToTransactions(result.data);
          setTransactions(newTransactions);
          setCurrentOffset(20); // Reset para próximo lote
          setHasMore(result.hasMore || false);
        }
      } catch (error) {
        console.error('Erro ao aplicar filtros:', error);
      }
    });
  };

  const handleClearFilters = () => {
    clearFilters();

    // Recarregar dados com filtros padrão
    startTransition(async () => {
      try {
        const result = await performSearch('', 0, { status: ['paid'] });
        if (result.success && result.data) {
          const newTransactions = convertPaymentsToTransactions(result.data);
          setTransactions(newTransactions);
          setCurrentOffset(20);
          setHasMore(result.hasMore || false);
        }
      } catch (error) {
        console.error('Erro ao limpar filtros:', error);
      }
    });
  };

  const handleExportClick = () => {
    console.log('Exportar relatório de pagamentos');
  };

  return (
    <>
      <PaymentFilters
        filters={filters}
        onFilterChange={handleFilterChange}
        onClearFilters={handleClearFilters}
        paymentMethods={paymentMethods}
        onSearchChange={handleSearchChangeWithDebounce}
        onExportClick={handleExportClick}
      />

      <TransactionsList
        transactions={transactions}
        onLoadMore={handleLoadMore}
        isLoading={isPending}
        hasMore={hasMore}
      />
    </>
  );
}
