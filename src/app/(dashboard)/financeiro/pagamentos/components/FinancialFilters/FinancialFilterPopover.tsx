"use client";

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Calendar } from "@/components/ui/calendar"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Filter, CalendarIcon, DollarSign } from "lucide-react"
import { useState, useEffect } from "react"
import { Badge } from "@/components/ui/badge"
import { cn } from "@/lib/utils"
import { format } from "date-fns"
import { ptBR } from "date-fns/locale"
import { DateRange } from "react-day-picker"
import { FinancialFilterState, PaymentMethodOption, TransactionType, ExpenseCategory } from "../types"

interface FinancialFilterPopoverProps {
  filters: FinancialFilterState;
  onFilterChange: (filters: FinancialFilterState) => void;
  onClearFilters?: () => void;
  paymentMethods: PaymentMethodOption[];
  transactionType: TransactionType;
  expenseCategories?: ExpenseCategory[];
}

// Labels para os filtros de receita
const incomeFilterLabels = {
  status: {
    pending: "Pendente",
    paid: "Pago",
    overdue: "Vencido",
    canceled: "Cancelado",
    awaiting_confirmation: "Aguardando Confirmação",
  },
  paymentType: {
    recurring: "Recorrente",
    signup_fee: "Taxa de Inscrição",
    graduation_fee: "Taxa de Graduação",
    late_fee: "Multa por Atraso",
    cancellation_fee: "Taxa de Cancelamento",
    initial_payment: "Pagamento Único",
  },
}

// Labels para os filtros de despesa
const expenseFilterLabels = {
  status: {
    pending: "Pendente",
    paid: "Pago",
    overdue: "Vencido",
    canceled: "Cancelado",
  },
  paymentType: {
    operational: "Operacional",
    equipment: "Equipamentos",
    maintenance: "Manutenção",
    marketing: "Marketing",
    utilities: "Utilidades",
    rent: "Aluguel",
    salary: "Salário",
    other: "Outros",
  },
}

export function FinancialFilterPopover({
  filters,
  onFilterChange,
  onClearFilters,
  paymentMethods,
  transactionType,
  expenseCategories = []
}: FinancialFilterPopoverProps) {
  const [open, setOpen] = useState(false);
  const [dateRange, setDateRange] = useState<DateRange | undefined>(
    filters.startDate && filters.endDate
      ? {
          from: filters.startDate,
          to: filters.endDate,
        }
      : undefined
  );
  const [minAmount, setMinAmount] = useState<string>(filters.minAmount?.toString() || '');
  const [maxAmount, setMaxAmount] = useState<string>(filters.maxAmount?.toString() || '');

  // Selecionar os labels baseado no tipo de transação
  const filterLabels = transactionType === 'income' ? incomeFilterLabels : expenseFilterLabels;

  // Atualizar o dateRange quando os filtros mudarem
  useEffect(() => {
    setDateRange(
      filters.startDate && filters.endDate
        ? {
            from: filters.startDate,
            to: filters.endDate,
          }
        : undefined
    );
  }, [filters]);

  // Atualizar valores de amount quando os filtros mudarem
  useEffect(() => {
    setMinAmount(filters.minAmount?.toString() || '');
    setMaxAmount(filters.maxAmount?.toString() || '');
  }, [filters.minAmount, filters.maxAmount]);

  const activeFiltersCount = (() => {
    let count = 0;

    // Contar cada status selecionado individualmente
    if (filters.status && filters.status.length > 0) {
      count += filters.status.length;
    }

    // Contar cada tipo de pagamento selecionado individualmente
    if (filters.paymentType && filters.paymentType.length > 0) {
      count += filters.paymentType.length;
    }

    // Contar cada método de pagamento selecionado individualmente
    if (filters.paymentMethod && filters.paymentMethod.length > 0) {
      count += filters.paymentMethod.length;
    }

    // Contar cada categoria selecionada individualmente (para despesas)
    if (filters.categories && filters.categories.length > 0) {
      count += filters.categories.length;
    }

    // Contar filtro de range de valor (conta como 1 se há pelo menos um valor definido)
    if (filters.minAmount || filters.maxAmount) {
      count++;
    }

    // Contar filtro de período (conta como 1 se há pelo menos uma data definida)
    if (filters.startDate || filters.endDate) {
      count++;
    }

    // Contar filtro de busca (conta como 1 se há texto de busca)
    if (filters.search && filters.search.trim()) {
      count++;
    }

    return count;
  })();

  const handleClearFilters = () => {
    if (onClearFilters) {
      // Usar a função do hook se disponível (recomendado)
      onClearFilters();
    } else {
      // Fallback: resetar manualmente para o estado padrão
      const defaultFilters: FinancialFilterState = {
        search: '',
        status: ['paid'], // Manter o filtro padrão "Pago" para receitas e despesas
        paymentMethod: [],
        paymentType: [],
        categories: [],
        page: 1,
        limit: 20
      };
      onFilterChange(defaultFilters);
    }

    // Limpar estados locais
    setDateRange(undefined);
    setMinAmount('');
    setMaxAmount('');
  };

  const handleToggleValue = (type: keyof Omit<FinancialFilterState, 'startDate' | 'endDate' | 'search' | 'page' | 'limit' | 'minAmount' | 'maxAmount'>, value: string) => {
    const currentValues = filters[type] || [];
    // Garantir que estamos tratando um array
    const currentArray = Array.isArray(currentValues) ? currentValues : [];
    
    const newValues = currentArray.includes(value)
      ? currentArray.filter((v: string) => v !== value)
      : [...currentArray, value];
    
    onFilterChange({
      ...filters,
      [type]: newValues.length ? newValues : undefined,
    });
  };

  const handleDateRangeSelect = (range: DateRange | undefined) => {
    setDateRange(range);
    onFilterChange({
      ...filters,
      startDate: range?.from,
      endDate: range?.to,
    });
  };

  const handleAmountChange = () => {
    const min = minAmount ? parseFloat(minAmount) : undefined;
    const max = maxAmount ? parseFloat(maxAmount) : undefined;
    
    onFilterChange({
      ...filters,
      minAmount: min,
      maxAmount: max,
    });
  };

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          size="sm"
          className={cn(
            "h-9 gap-2 bg-gradient-to-r from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 border-blue-200 dark:border-blue-700 text-blue-700 dark:text-blue-300 hover:bg-gradient-to-r hover:from-blue-100 hover:to-blue-200 dark:hover:from-blue-900/30 dark:hover:to-blue-800/30 transition-all duration-200 shadow-sm",
            activeFiltersCount > 0 && "border-blue-400 dark:border-blue-500 shadow-md"
          )}
        >
          <div className="p-1 bg-blue-100 dark:bg-blue-900/30 rounded-full">
            <Filter className="h-3 w-3 text-blue-600 dark:text-blue-400" />
          </div>
          Filtros
          {activeFiltersCount > 0 && (
            <Badge
              variant="secondary"
              className="h-5 rounded-full px-2 font-medium bg-gradient-to-r from-blue-200 to-blue-300 dark:from-blue-800 dark:to-blue-700 text-blue-800 dark:text-blue-200 border-0"
            >
              {activeFiltersCount}
            </Badge>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-80 p-0 max-h-[min(60vh,500px)] overflow-hidden border-0 shadow-xl bg-gradient-to-br from-white to-gray-50/50 dark:from-gray-900 dark:to-gray-800/50" align="start">
        {/* Cabeçalho */}
        <div className="p-4 border-b border-gray-200 dark:border-gray-700 flex-shrink-0 bg-gradient-to-r from-blue-50/50 to-transparent dark:from-blue-900/20 dark:to-transparent">
          <div className="flex items-center justify-between">
            <h4 className="font-semibold text-gray-900 dark:text-gray-100 flex items-center gap-2">
              <div className="p-1 bg-blue-100 dark:bg-blue-900/30 rounded-full">
                <Filter className="h-4 w-4 text-blue-600 dark:text-blue-400" />
              </div>
              Filtros Avançados
            </h4>
            {activeFiltersCount > 0 && (
              <Button
                variant="ghost"
                size="sm"
                className="h-auto p-2 text-gray-600 dark:text-gray-400 hover:text-red-600 dark:hover:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-md transition-all duration-200"
                onClick={handleClearFilters}
              >
                Limpar filtros
              </Button>
            )}
          </div>
        </div>
        
        {/* Conteúdo com scroll */}
        <div className="overflow-y-auto flex-1 max-h-[400px]">
          <div className="p-4 space-y-6">
            {/* Status */}
            <div className="space-y-3">
              <label className="text-sm font-semibold text-gray-900 dark:text-gray-100 flex items-center gap-2">
                <div className="w-2 h-2 bg-emerald-500 rounded-full"></div>
                Status
              </label>
              <div className="grid grid-cols-1 gap-2">
                {Object.entries(filterLabels.status).map(([value, label]) => (
                  <Button
                    key={value}
                    variant="outline"
                    size="sm"
                    className={cn(
                      "justify-start gap-3 h-9 bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-all duration-200",
                      filters.status?.includes(value) && "border-emerald-400 dark:border-emerald-500 bg-emerald-50 dark:bg-emerald-900/20 text-emerald-700 dark:text-emerald-300 shadow-sm"
                    )}
                    onClick={() => handleToggleValue('status', value)}
                  >
                    <div className={cn(
                      "h-3 w-3 rounded-full transition-colors duration-200",
                      filters.status?.includes(value) ? "bg-emerald-500" : "bg-gray-400 dark:bg-gray-500"
                    )} />
                    {label}
                  </Button>
                ))}
              </div>
            </div>

            {/* Tipo de Transação */}
            <div className="space-y-3">
              <label className="text-sm font-semibold text-gray-900 dark:text-gray-100 flex items-center gap-2">
                <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                {transactionType === 'income' ? 'Tipo de Receita' : 'Tipo de Despesa'}
              </label>
              <div className="grid grid-cols-1 gap-2">
                {Object.entries(filterLabels.paymentType).map(([value, label]) => (
                  <Button
                    key={value}
                    variant="outline"
                    size="sm"
                    className={cn(
                      "justify-start gap-3 h-9 bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-all duration-200",
                      filters.paymentType?.includes(value) && "border-blue-400 dark:border-blue-500 bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300 shadow-sm"
                    )}
                    onClick={() => handleToggleValue('paymentType', value)}
                  >
                    <div className={cn(
                      "h-3 w-3 rounded-full transition-colors duration-200",
                      filters.paymentType?.includes(value) ? "bg-blue-500" : "bg-gray-400 dark:bg-gray-500"
                    )} />
                    {label}
                  </Button>
                ))}
              </div>
            </div>

            {/* Forma de Pagamento */}
            <div className="space-y-3">
              <label className="text-sm font-semibold text-gray-900 dark:text-gray-100 flex items-center gap-2">
                <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                Forma de Pagamento
              </label>
              <div className="grid grid-cols-2 gap-2">
                {paymentMethods.map((method) => (
                  <Button
                    key={method.id}
                    variant="outline"
                    size="sm"
                    className={cn(
                      "justify-start gap-2 h-9 bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-all duration-200",
                      filters.paymentMethod?.includes(method.slug) && "border-purple-400 dark:border-purple-500 bg-purple-50 dark:bg-purple-900/20 text-purple-700 dark:text-purple-300 shadow-sm"
                    )}
                    onClick={() => handleToggleValue('paymentMethod', method.slug)}
                  >
                    <div className={cn(
                      "h-3 w-3 rounded-full transition-colors duration-200",
                      filters.paymentMethod?.includes(method.slug) ? "bg-purple-500" : "bg-gray-400 dark:bg-gray-500"
                    )} />
                    {method.name}
                  </Button>
                ))}
              </div>
            </div>

            {/* Categorias (apenas para despesas) */}
            {transactionType === 'expense' && expenseCategories.length > 0 && (
              <div className="space-y-3">
                <label className="text-sm font-semibold text-gray-900 dark:text-gray-100 flex items-center gap-2">
                  <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                  Categorias
                </label>
                <div className="grid grid-cols-1 gap-2">
                  {expenseCategories.map((category) => (
                    <Button
                      key={category.id}
                      variant="outline"
                      size="sm"
                      className={cn(
                        "justify-start gap-3 h-9 bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-all duration-200",
                        filters.categories?.includes(category.id) && "border-orange-400 dark:border-orange-500 bg-orange-50 dark:bg-orange-900/20 text-orange-700 dark:text-orange-300 shadow-sm"
                      )}
                      onClick={() => handleToggleValue('categories', category.id)}
                    >
                      <div
                        className={cn(
                          "h-3 w-3 rounded-full transition-colors duration-200",
                          filters.categories?.includes(category.id) ? category.color : "bg-gray-400 dark:bg-gray-500"
                        )}
                        style={{ backgroundColor: filters.categories?.includes(category.id) ? category.color : undefined }}
                      />
                      {category.name}
                    </Button>
                  ))}
                </div>
              </div>
            )}

            {/* Range de Valor */}
            <div className="space-y-3">
              <label className="text-sm font-semibold text-gray-900 dark:text-gray-100 flex items-center gap-2">
                <div className="p-1 bg-green-100 dark:bg-green-900/30 rounded-full">
                  <DollarSign className="h-3 w-3 text-green-600 dark:text-green-400" />
                </div>
                Range de Valor
              </label>
              <div className="grid grid-cols-2 gap-3">
                <div className="space-y-2">
                  <Label htmlFor="min-amount" className="text-xs font-medium text-gray-600 dark:text-gray-400">
                    Valor mínimo
                  </Label>
                  <Input
                    id="min-amount"
                    type="number"
                    placeholder="0,00"
                    value={minAmount}
                    onChange={(e) => setMinAmount(e.target.value)}
                    onBlur={handleAmountChange}
                    className="h-9 bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700 focus:border-green-400 dark:focus:border-green-500 transition-colors duration-200"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="max-amount" className="text-xs font-medium text-gray-600 dark:text-gray-400">
                    Valor máximo
                  </Label>
                  <Input
                    id="max-amount"
                    type="number"
                    placeholder="1000,00"
                    value={maxAmount}
                    onChange={(e) => setMaxAmount(e.target.value)}
                    onBlur={handleAmountChange}
                    className="h-9 bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700 focus:border-green-400 dark:focus:border-green-500 transition-colors duration-200"
                  />
                </div>
              </div>
            </div>

            {/* Data */}
            <div className="space-y-3">
              <label className="text-sm font-semibold text-gray-900 dark:text-gray-100 flex items-center gap-2">
                <div className="p-1 bg-indigo-100 dark:bg-indigo-900/30 rounded-full">
                  <CalendarIcon className="h-3 w-3 text-indigo-600 dark:text-indigo-400" />
                </div>
                Período
              </label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      "w-full justify-start text-left font-normal h-9 bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700 transition-all duration-200",
                      !dateRange && "text-gray-500 dark:text-gray-400",
                      dateRange && "border-indigo-400 dark:border-indigo-500 bg-indigo-50 dark:bg-indigo-900/20 text-indigo-700 dark:text-indigo-300"
                    )}
                  >
                    <div className="p-1 bg-indigo-100 dark:bg-indigo-900/30 rounded-full mr-2">
                      <CalendarIcon className="h-3 w-3 text-indigo-600 dark:text-indigo-400" />
                    </div>
                    {dateRange?.from ? (
                      dateRange.to ? (
                        <>
                          {format(dateRange.from, "dd/MM/yyyy", { locale: ptBR })} -{" "}
                          {format(dateRange.to, "dd/MM/yyyy", { locale: ptBR })}
                        </>
                      ) : (
                        format(dateRange.from, "dd/MM/yyyy", { locale: ptBR })
                      )
                    ) : (
                      <span>Selecionar período</span>
                    )}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0 border-0 shadow-xl bg-white dark:bg-gray-900" align="start">
                  <Calendar
                    mode="range"
                    defaultMonth={dateRange?.from}
                    selected={dateRange}
                    onSelect={handleDateRangeSelect}
                    numberOfMonths={2}
                    locale={ptBR}
                  />
                </PopoverContent>
              </Popover>
            </div>

          </div>
        </div>
      </PopoverContent>
    </Popover>
  );
}
