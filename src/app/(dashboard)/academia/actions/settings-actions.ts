'use server';

import { z } from 'zod';
import { revalidatePath } from 'next/cache';
import { createClient } from '@/services/supabase/server';
import {
  definirSettingTenantSchema,
  permitirMultiplasMembershipsSchema,
  type DefinirSettingTenantData,
  type PermitirMultiplasMembershipsData,
} from '@/schemas/settings-schemas';

interface ActionResult<T = unknown> {
  success: boolean;
  data?: T;
  errors?: Record<string, string>;
}

export async function setTenantSetting(data: unknown): Promise<ActionResult> {
  try {
    const result = definirSettingTenantSchema.safeParse(data);
    
    if (!result.success) {
      return {
        success: false,
        errors: result.error.format() as unknown as Record<string, string>,
      };
    }

    const validatedData = result.data;
    const supabase = await createClient();

    // Chamar função RPC set_tenant_setting
    const { data: settingData, error } = await supabase.rpc('set_tenant_setting', {
      p_setting_key: validatedData.chaveSetting,
      p_setting_value: validatedData.valorSetting,
    });

    if (error) {
      return {
        success: false,
        errors: { _form: `Erro ao configurar setting: ${error.message}` },
      };
    }

    revalidatePath('/academia/configuracoes');
    
    return {
      success: true,
      data: settingData,
    };
  } catch (error) {
    console.error('Erro ao configurar setting do tenant:', error);
    return {
      success: false,
      errors: { _form: 'Erro interno do servidor' },
    };
  }
}

export async function setMultipleMembershipsAllowed(data: unknown): Promise<ActionResult> {
  try {
    const result = permitirMultiplasMembershipsSchema.safeParse(data);
    
    if (!result.success) {
      return {
        success: false,
        errors: result.error.format() as unknown as Record<string, string>,
      };
    }

    const validatedData = result.data;
    const supabase = await createClient();

    // Chamar função RPC set_tenant_setting especificamente para múltiplas matrículas
    const { data: settingData, error } = await supabase.rpc('set_tenant_setting', {
      p_setting_key: 'allow_multiple_memberships',
      p_setting_value: validatedData.permitirMultiplasMemberships,
    });

    if (error) {
      return {
        success: false,
        errors: { _form: `Erro ao configurar múltiplas matrículas: ${error.message}` },
      };
    }

    revalidatePath('/academia/configuracoes');
    revalidatePath('/alunos');
    
    return {
      success: true,
      data: settingData,
    };
  } catch (error) {
    console.error('Erro ao configurar múltiplas matrículas:', error);
    return {
      success: false,
      errors: { _form: 'Erro interno do servidor' },
    };
  }
}

export async function getTenantSettings(): Promise<ActionResult> {
  try {
    const supabase = await createClient();

    // Chamar função RPC get_tenant_settings
    const { data: settingsData, error } = await supabase.rpc('get_tenant_settings');

    if (error) {
      return {
        success: false,
        errors: { _form: `Erro ao buscar configurações: ${error.message}` },
      };
    }

    return {
      success: true,
      data: settingsData,
    };
  } catch (error) {
    console.error('Erro ao buscar configurações do tenant:', error);
    return {
      success: false,
      errors: { _form: 'Erro interno do servidor' },
    };
  }
} 