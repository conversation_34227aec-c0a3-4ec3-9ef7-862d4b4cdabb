'use server';

import { z } from 'zod';
import { revalidatePath } from 'next/cache';
import { createClient, createAdminClient } from '@/services/supabase/server';
import { getCurrentUser } from '@/services/auth/actions/auth-actions';
import {
  criarPlanoSchema,
  publicarPlanoSchema,
  duplicarPlanoSchema,
  validarConfiguracaoPlanoSchema,
  type CriarPlanoData,
  type PublicarPlanoData,
  type DuplicarPlanoData,
  type ValidarConfiguracaoPlanoData,
} from '@/schemas/plan-schemas';

interface ActionResult<T = unknown> {
  success: boolean;
  data?: T;
  errors?: Record<string, string>;
}

// Funções de transformação para compatibilidade com RPC
function transformPricingConfig(pricing: any): any {
  const transformed = { ...pricing };

  // Debug log
  console.log('transformPricingConfig input:', pricing);

  // Transformar campos do português para inglês
  if (transformed.tipo) {
    transformed.type = transformed.tipo;
    delete transformed.tipo;
  }

  if (transformed.custo !== undefined) {
    transformed.amount = transformed.custo;
    delete transformed.custo;
  }

  if (transformed.valor !== undefined) {
    transformed.amount = transformed.valor;
    delete transformed.valor;
  }

  if (transformed.frequencia) {
    transformed.frequency = transformed.frequencia;
    delete transformed.frequencia;
  }

  if (transformed.numeroFrequencia) {
    transformed.frequency_number = transformed.numeroFrequencia;
    delete transformed.numeroFrequencia;
  }

  if (transformed.maxPagamentos !== undefined) {
    transformed.max_payments = transformed.maxPagamentos;
    delete transformed.maxPagamentos;
  }

  if (transformed.moeda) {
    transformed.currency = transformed.moeda;
    delete transformed.moeda;
  }

  // Transformar taxas
  if (transformed.taxaInscricao !== undefined) {
    transformed.signup_fee = transformed.taxaInscricao;
    delete transformed.taxaInscricao;
  }

  if (transformed.taxaAtraso !== undefined) {
    transformed.late_fee = transformed.taxaAtraso;
    delete transformed.taxaAtraso;
  }

  if (transformed.diasAtraso !== undefined) {
    transformed.late_days = transformed.diasAtraso;
    delete transformed.diasAtraso;
  }

  // Debug log
  console.log('transformPricingConfig output:', transformed);

  return transformed;
}

function transformDurationConfig(duration: any): any {
  const transformed = { ...duration };
  
  // Transformar campos do português para inglês
  if (transformed.tipo) {
    transformed.type = transformed.tipo;
    delete transformed.tipo;
  }
  
  if (transformed.duracao !== undefined) {
    transformed.value = transformed.duracao;
    delete transformed.duracao;
  }
  
  if (transformed.unidadeTempo) {
    transformed.unit = transformed.unidadeTempo;
    delete transformed.unidadeTempo;
  }
  
  if (transformed.dataInicio) {
    transformed.start_date = transformed.dataInicio;
    delete transformed.dataInicio;
  }
  
  if (transformed.dataFim) {
    transformed.end_date = transformed.dataFim;
    delete transformed.dataFim;
  }
  
  return transformed;
}

function transformAccessConfig(access: any): any {
  const transformed = { ...access };
  
  // Transformar campos do português para inglês
  if (transformed.frequencia) {
    transformed.frequency = transformed.frequencia;
    delete transformed.frequencia;
  }
  
  if (transformed.quantidade !== undefined) {
    transformed.quantity = transformed.quantidade;
    delete transformed.quantidade;
  }
  
  if (transformed.capacidade) {
    transformed.capacity = transformed.capacidade;
    delete transformed.capacidade;
  }
  
  if (transformed.capacidadeMaxima !== undefined) {
    transformed.max_capacity = transformed.capacidadeMaxima;
    delete transformed.capacidadeMaxima;
  }
  
  if (transformed.modalidades) {
    transformed.modalities = transformed.modalidades;
    delete transformed.modalidades;
  }
  
  if (transformed.todasSessoes !== undefined) {
    transformed.all_sessions = transformed.todasSessoes;
    delete transformed.todasSessoes;
  }
  
  return transformed;
}

function transformPricingFromDB(pricing: any): any {
  if (!pricing) return {}

  const transformed = {
    tipo: pricing.type,
    valor: pricing.type === 'recurring' ? String(pricing.amount) : undefined,
    frequencia: pricing.type === 'recurring' ? pricing.frequency : undefined,
    numeroFrequencia: pricing.type === 'recurring' ? String(pricing.frequency_number) : undefined,
    maxPagamentos: pricing.max_payments !== undefined ? pricing.max_payments : undefined,
    taxaInscricao: pricing.signup_fee !== undefined ? pricing.signup_fee : undefined,
    taxaAtraso: pricing.late_fee !== undefined ? pricing.late_fee : undefined,
    diasAtraso: pricing.late_days !== undefined ? pricing.late_days : undefined,
    custo: pricing.type === 'one-time' || pricing.type === 'per-session' ? String(pricing.amount) : undefined,
    renovacaoAutomatica: pricing.type === 'one-time' ? pricing.auto_renews : false,

    // trial
    valorDuranteTrial: pricing.type === 'trial' ? String(pricing.trial_price) : undefined,
    valorAposTrial: pricing.type === 'trial' ? String(pricing.amount) : undefined,
    frequenciaAposTrial: pricing.type === 'trial' ? pricing.frequency : undefined,
  };

  if (pricing.type === 'trial' && pricing.trial_duration) {
    (transformed as any).duracao = pricing.trial_duration.value;
    (transformed as any).unidadeTempo = pricing.trial_duration.unit;
  }

  return transformed;
}

function transformDurationFromDB(duration: any): any {
  if (!duration) return { tipo: 'ongoing' }

  const base = {
    tipo: duration.type,
  }

  switch (duration.type) {
    case 'limited':
      return {
        ...base,
        duracao: duration.value,
        unidadeTempo: duration.unit,
        adicionarDuracaoRenovacao: duration.adicionarDuracaoRenovacao,
        cobrarTaxaRenovacao: duration.cobrarTaxaRenovacao,
        opcaoRenovacao: duration.opcaoRenovacao,
        taxaCancelamento: duration.taxaCancelamento,
      }
    case 'specific':
      return {
        ...base,
        dataInicio: duration.start_date,
        dataFim: duration.end_date,
        periodosAdicionais: duration.periodosAdicionais || [],
        mostrarPeriodosAtivos: duration.mostrarPeriodosAtivos,
        agendarPrimeiroPagamento: duration.agendarPrimeiroPagamento,
        taxaCancelamento: duration.taxaCancelamento,
      }
    case 'ongoing':
    default:
      return {
        ...base,
        renovacaoAutomatica: duration.renovacaoAutomatica,
      }
  }
}

function transformAccessFromDB(access: any): any {
  if (!access) return {}
  return {
    frequencia: access.frequency,
    quantidade: access.quantity,
    capacidade: access.capacity,
    capacidadeMaxima: access.max_capacity,
    modalidades: access.modalities || [],
    todasSessoes: access.all_sessions,
  };
}


export async function createPlan(data: unknown): Promise<ActionResult> {
  try {
    const result = criarPlanoSchema.safeParse(data);
    
    if (!result.success) {
      return {
        success: false,
        errors: result.error.format() as unknown as Record<string, string>,
      };
    }

    const validatedData = result.data;
    const supabase = await createClient();
    const user = await getCurrentUser();

    if (!user) {
      return {
        success: false,
        errors: { _form: 'Usuário não autenticado' },
      };
    }

    const tenantId = user.app_metadata?.tenant_id;

    if (!tenantId) {
      return {
        success: false,
        errors: { _form: 'Tenant não identificado para o usuário' },
      };
    }

    // Transformar dados do português para inglês para compatibilidade com RPC
    const transformedPricingConfig = transformPricingConfig(validatedData.pricing);
    const transformedDurationConfig = transformDurationConfig(validatedData.duration);
    const transformedAccessConfig = transformAccessConfig(validatedData.academyAccess);

    // Chamar função RPC create_plan
    const { data: planData, error } = await supabase.rpc('create_plan', {
      p_tenant_id: tenantId,
      p_title: validatedData.details.titulo,
      p_plan_type: validatedData.details.tipo,
      p_pricing_config: transformedPricingConfig,
      p_duration_config: transformedDurationConfig,
      p_access_config: transformedAccessConfig,
      p_benefits: validatedData.benefits,
      p_metadata: validatedData.metadata,
    });

    if (error) {
      return {
        success: false,
        errors: { _form: `Erro ao criar plano: ${error.message}` },
      };
    }

    revalidatePath('/academia');
    
    return {
      success: true,
      data: planData,
    };
  } catch (error) {
    console.error('Erro ao criar plano:', error);
    return {
      success: false,
      errors: { _form: 'Erro interno do servidor' },
    };
  }
}

export async function updatePlan(id: string, data: unknown): Promise<ActionResult> {
  try {
    const result = criarPlanoSchema.safeParse(data);
    
    if (!result.success) {
      return {
        success: false,
        errors: result.error.format() as unknown as Record<string, string>,
      };
    }

    const validatedData = result.data;
    const supabase = await createClient();
    const user = await getCurrentUser();

    if (!user) {
      return {
        success: false,
        errors: { _form: 'Usuário não autenticado' },
      };
    }

    const tenantId = user.app_metadata?.tenant_id;

    if (!tenantId) {
      return {
        success: false,
        errors: { _form: 'Tenant não identificado para o usuário' },
      };
    }

    // Transformar dados para o formato do banco de dados (inglês)
    const transformedPricingConfig = transformPricingConfig(validatedData.pricing);
    const transformedDurationConfig = transformDurationConfig(validatedData.duration);
    const transformedAccessConfig = transformAccessConfig(validatedData.academyAccess);

    // Chamar função RPC update_plan
    const { data: planData, error } = await supabase
      .from('plans')
      .update({
        title: validatedData.details.titulo,
        plan_type: validatedData.details.tipo,
        pricing_config: transformedPricingConfig,
        duration_config: transformedDurationConfig,
        access_config: transformedAccessConfig,
        benefits: validatedData.benefits,
        metadata: validatedData.metadata,
      })
      .eq('id', id)
      .eq('tenant_id', tenantId)
      .eq('status', 'draft') // Apenas rascunhos podem ser atualizados
      .select()
      .single();

    if (error) {
      return {
        success: false,
        errors: { _form: `Erro ao atualizar plano: ${error.message}` },
      };
    }

    if (!planData) {
      return {
        success: false,
        errors: { _form: 'Plano não encontrado ou não é um rascunho.' },
      };
    }
    
    revalidatePath('/financeiro/recorrentes/planos');
    revalidatePath(`/financeiro/recorrentes/planos/${id}/editar`);
    
    return {
      success: true,
      data: planData,
    };
  } catch (error) {
    console.error('Erro ao atualizar plano:', error);
    return {
      success: false,
      errors: { _form: 'Erro interno do servidor' },
    };
  }
}

export async function publishPlan(data: unknown): Promise<ActionResult> {
  try {
    const result = publicarPlanoSchema.safeParse(data);
    
    if (!result.success) {
      return {
        success: false,
        errors: result.error.format() as unknown as Record<string, string>,
      };
    }

    const validatedData = result.data;
    const supabase = await createClient();
    const user = await getCurrentUser();

    if (!user) {
      return {
        success: false,
        errors: { _form: 'Usuário não autenticado' },
      };
    }

    const tenantId = user.app_metadata?.tenant_id;

    if (!tenantId) {
      return {
        success: false,
        errors: { _form: 'Tenant não identificado para o usuário' },
      };
    }

    const { data: publishData, error } = await supabase.rpc('publish_plan', {
      p_plan_id: validatedData.planoId,
      p_tenant_id: tenantId,
    });

    if (error) {
      return {
        success: false,
        errors: { _form: `Erro ao publicar plano: ${error.message}` },
      };
    }

    revalidatePath('/academia');
    revalidatePath('/financeiro/recorrentes/planos');
    
    return {
      success: true,
      data: publishData,
    };
  } catch (error) {
    console.error('Erro ao publicar plano:', error);
    return {
      success: false,
      errors: { _form: 'Erro interno do servidor' },
    };
  }
}

export async function pausePlan(data: unknown): Promise<ActionResult> {
  try {
    // Reutiliza o mesmo schema do publicarPlano pois a estrutura é idêntica
    const result = publicarPlanoSchema.safeParse(data);

    if (!result.success) {
      return {
        success: false,
        errors: result.error.format() as unknown as Record<string, string>,
      };
    }

    const validatedData = result.data;
    const supabase = await createAdminClient();
    const user = await getCurrentUser();

    if (!user) {
      return {
        success: false,
        errors: { _form: 'Usuário não autenticado' },
      };
    }

    const tenantId = user.app_metadata?.tenant_id;

    if (!tenantId) {
      return {
        success: false,
        errors: { _form: 'Tenant não identificado para o usuário' },
      };
    }

    // Verificar se o plano existe e está ativo
    const { data: planData, error: planError } = await supabase
      .from('plans')
      .select('id, status, title')
      .eq('id', validatedData.planoId)
      .eq('tenant_id', tenantId)
      .eq('status', 'active')
      .single();

    if (planError || !planData) {
      return {
        success: false,
        errors: { _form: 'Plano não encontrado ou não está ativo' },
      };
    }

    // Verificar se existem memberships ativas para este plano
    const { count: activeMembershipsCount, error: countError } = await supabase
      .from('memberships')
      .select('id', { count: 'exact', head: true })
      .eq('plan_id', validatedData.planoId)
      .eq('tenant_id', tenantId)
      .eq('status', 'active');

    if (countError) {
      return {
        success: false,
        errors: { _form: `Erro ao verificar matrículas ativas: ${countError.message}` },
      };
    }

    // Aplicar regra de negócio:
    // - Se tem alunos matriculados: status = 'paused'
    // - Se não tem alunos matriculados: status = 'draft'
    const newStatus = (activeMembershipsCount && activeMembershipsCount > 0) ? 'paused' : 'draft';

    // Atualizar o status do plano
    const { data: updateData, error } = await supabase
      .from('plans')
      .update({
        status: newStatus,
        updated_at: new Date().toISOString()
      })
      .eq('id', validatedData.planoId)
      .eq('tenant_id', tenantId)
      .select();

    if (error) {
      return {
        success: false,
        errors: { _form: `Erro ao pausar plano: ${error.message}` },
      };
    }

    revalidatePath('/academia');
    revalidatePath('/financeiro/recorrentes/planos');

    // Mensagem explicativa baseada na regra aplicada
    const message = newStatus === 'paused'
      ? `Plano pausado com sucesso. Status definido como "pausado" pois existem ${activeMembershipsCount} aluno(s) matriculado(s).`
      : 'Plano pausado com sucesso. Status definido como "rascunho" pois não há alunos matriculados.';

    return {
      success: true,
      data: {
        ...updateData,
        appliedStatus: newStatus,
        activeMembershipsCount: activeMembershipsCount || 0,
        message
      },
    };
  } catch (error) {
    console.error('Erro ao pausar plano:', error);
    return {
      success: false,
      errors: { _form: 'Erro interno do servidor' },
    };
  }
}

export async function duplicatePlan(data: unknown): Promise<ActionResult> {
  try {
    const result = duplicarPlanoSchema.safeParse(data);
    
    if (!result.success) {
      return {
        success: false,
        errors: result.error.format() as unknown as Record<string, string>,
      };
    }

    const validatedData = result.data;
    const supabase = await createClient();
    const user = await getCurrentUser();

    if (!user) {
      return {
        success: false,
        errors: { _form: 'Usuário não autenticado' },
      };
    }

    const tenantId = user.app_metadata?.tenant_id;

    if (!tenantId) {
      return {
        success: false,
        errors: { _form: 'Tenant não identificado para o usuário' },
      };
    }

    // Chamar função RPC duplicate_plan
    const { data: duplicateData, error } = await supabase.rpc('duplicate_plan', {
      p_plan_id: validatedData.planoId,
      p_tenant_id: tenantId,
    });

    if (error) {
      return {
        success: false,
        errors: { _form: `Erro ao duplicar plano: ${error.message}` },
      };
    }

    revalidatePath('/academia');
    
    return {
      success: true,
      data: duplicateData,
    };
  } catch (error) {
    console.error('Erro ao duplicar plano:', error);
    return {
      success: false,
      errors: { _form: 'Erro interno do servidor' },
    };
  }
}

export async function getActivePlans(): Promise<ActionResult> {
  try {
    const supabase = await createClient();
    const user = await getCurrentUser();

    if (!user) {
      return {
        success: false,
        errors: { _form: 'Usuário não autenticado' },
      };
    }

    const tenantId = user.app_metadata?.tenant_id;

    if (!tenantId) {
      return {
        success: false,
        errors: { _form: 'Tenant não identificado para o usuário' },
      };
    }

    // Chamar função RPC get_active_plans
    const { data: plansData, error } = await supabase.rpc('get_active_plans', {
      p_tenant_id: tenantId,
    });

    if (error) {
      return {
        success: false,
        errors: { _form: `Erro ao buscar planos ativos: ${error.message}` },
      };
    }

    return {
      success: true,
      data: plansData,
    };
  } catch (error) {
    console.error('Erro ao buscar planos ativos:', error);
    return {
      success: false,
      errors: { _form: 'Erro interno do servidor' },
    };
  }
}

export async function getAllPlans(): Promise<ActionResult> {
  try {
    const supabase = await createClient()
    const user = await getCurrentUser()

    if (!user) {
      return {
        success: false,
        errors: { _form: 'Usuário não autenticado' },
      }
    }

    const tenantId = user.app_metadata?.tenant_id

    if (!tenantId) {
      return {
        success: false,
        errors: { _form: 'Tenant não identificado para o usuário' },
      }
    }

    // Buscar planos com contagem de memberships ativas
    const { data, error } = await supabase
      .from('plans')
      .select(`
        *,
        memberships!left(
          id,
          status
        )
      `)
      .eq('tenant_id', tenantId)

    if (error) {
      return {
        success: false,
        errors: { _form: `Erro ao buscar planos: ${error.message}` },
      }
    }

    // Buscar todas as modalidades do tenant para fazer o mapeamento
    const { data: modalities, error: modalitiesError } = await supabase
      .from('modalities')
      .select('id, name')
      .eq('tenant_id', tenantId)

    if (modalitiesError) {
      console.error('Erro ao buscar modalidades:', modalitiesError)
    }

    // Criar um mapa de ID -> nome das modalidades
    const modalitiesMap = new Map(
      (modalities || []).map((m: any) => [m.id, m.name])
    )

    // Calcular receita mensal do mês atual para cada plano
    const currentMonth = new Date().getMonth() + 1
    const currentYear = new Date().getFullYear()

    const { data: revenueData, error: revenueError } = await supabase
      .from('payments')
      .select(`
        amount,
        memberships!inner(
          plan_id
        )
      `)
      .eq('tenant_id', tenantId)
      .eq('status', 'paid')
      .gte('due_date', `${currentYear}-${currentMonth.toString().padStart(2, '0')}-01`)
      .lte('due_date', `${currentYear}-${currentMonth.toString().padStart(2, '0')}-${new Date(currentYear, currentMonth, 0).getDate()}`)

    if (revenueError) {
      console.error('Erro ao buscar receita mensal:', revenueError)
    }

    // Criar mapa de receita por plano
    const revenueByPlan = new Map<string, number>()
    if (revenueData) {
      revenueData.forEach((payment: any) => {
        const planId = payment.memberships.plan_id
        const amount = parseFloat(payment.amount) || 0
        revenueByPlan.set(planId, (revenueByPlan.get(planId) || 0) + amount)
      })
    }

    const plansData = data.map((p: any) => {
      // Contar apenas memberships ativas
      const activeMemberships = p.memberships?.filter((m: any) => m.status === 'active') || []

      return {
        ...p,
        students_count: activeMemberships.length,
        monthly_revenue: revenueByPlan.get(p.id) || 0,
        memberships: undefined, // remove the old field
        modalitiesMap, // adicionar o mapa para uso no adapter
      }
    })

    return {
      success: true,
      data: plansData,
    }
  } catch (error) {
    console.error('Erro ao buscar planos:', error)
    return {
      success: false,
      errors: { _form: 'Erro interno do servidor' },
    }
  }
}

export async function getPlanLatestVersion(basePlanId: string): Promise<ActionResult> {
  try {
    if (!basePlanId) {
      return {
        success: false,
        errors: { _form: 'ID do plano base é obrigatório' },
      };
    }

    const supabase = await createClient();

    // Chamar função RPC get_plan_latest_version
    const { data: planData, error } = await supabase.rpc('get_plan_latest_version', {
      p_base_plan_id: basePlanId,
    });

    if (error) {
      return {
        success: false,
        errors: { _form: `Erro ao buscar versão mais recente: ${error.message}` },
      };
    }

    return {
      success: true,
      data: planData,
    };
  } catch (error) {
    console.error('Erro ao buscar versão mais recente do plano:', error);
    return {
      success: false,
      errors: { _form: 'Erro interno do servidor' },
    };
  }
}

export async function validatePlanConfig(data: unknown): Promise<ActionResult> {
  try {
    const result = validarConfiguracaoPlanoSchema.safeParse(data);
    
    if (!result.success) {
      return {
        success: false,
        errors: result.error.format() as unknown as Record<string, string>,
      };
    }

    const validatedData = result.data;
    const supabase = await createClient();

    // Transformar dados do português para inglês para compatibilidade com RPC
    const transformedPricingConfig = transformPricingConfig(validatedData.pricing);
    const transformedDurationConfig = transformDurationConfig(validatedData.duration);
    const transformedAccessConfig = transformAccessConfig(validatedData.academyAccess);

    // Chamar função RPC validate_plan_config
    const { data: validationData, error } = await supabase.rpc('validate_plan_config', {
      p_pricing_config: transformedPricingConfig,
      p_duration_config: transformedDurationConfig,
      p_access_config: transformedAccessConfig,
    });

    if (error) {
      return {
        success: false,
        errors: { _form: `Erro ao validar configuração: ${error.message}` },
      };
    }

    return {
      success: true,
      data: validationData,
    };
  } catch (error) {
    console.error('Erro ao validar configuração do plano:', error);
    return {
      success: false,
      errors: { _form: 'Erro interno do servidor' },
    };
  }
}

export async function getPlanById(
  id: string
): Promise<ActionResult<CriarPlanoData>> {
  try {
    const supabase = await createClient()
    const user = await getCurrentUser()

    if (!user) {
      return { success: false, errors: { _form: 'Usuário não autenticado' } }
    }

    const tenantId = user.app_metadata?.tenant_id
    if (!tenantId) {
      return { success: false, errors: { _form: 'Tenant não identificado' } }
    }

    const { data: plan, error } = await supabase
      .from('plans')
      .select('*')
      .eq('id', id)
      .eq('tenant_id', tenantId)
      .single()

    if (error) {
      console.error('Error fetching plan by id:', error)
      return {
        success: false,
        errors: { _form: 'Plano não encontrado ou erro ao buscar.' }
      }
    }

    if (!plan) {
      return { success: false, errors: { _form: 'Plano não encontrado.' } }
    }

    const transformedPricing = transformPricingFromDB(plan.pricing_config);

    const formData: CriarPlanoData & { id: string } = {
      id: plan.id,
      details: {
        titulo: plan.title,
        tipo: plan.plan_type as 'individual', // Cast to fit the form type
      },
      benefits: plan.benefits || [],
      pricing: transformedPricing,
      duration: transformDurationFromDB(plan.duration_config),
      academyAccess: transformAccessFromDB(plan.access_config),
      metadata: plan.metadata || {}
    }

    // Note: Form components now read directly from pricing config,
    // so no need to populate separate perSession/trial properties

    return { success: true, data: formData }
  } catch (error) {
    console.error('Erro ao buscar plano por ID:', error)
    return { success: false, errors: { _form: 'Erro interno do servidor' } }
  }
}

export interface PlanMetrics {
  activePlansCount: number;
  totalEnrolledStudents: number;
  averageTicket: number;
  mostPopularPlan: string;
}

export async function getPlanMetrics(): Promise<ActionResult<PlanMetrics>> {
  try {
    const supabase = await createClient();
    const user = await getCurrentUser();

    if (!user) {
      return {
        success: false,
        errors: { _form: 'Usuário não autenticado' },
      };
    }

    const tenantId = user.app_metadata?.tenant_id;

    if (!tenantId) {
      return {
        success: false,
        errors: { _form: 'Tenant não identificado para o usuário' },
      };
    }

    // Buscar planos ativos
    const { count: activePlansCount, error: plansError } = await supabase
      .from('plans')
      .select('id', { count: 'exact', head: true })
      .eq('tenant_id', tenantId)
      .eq('status', 'active');

    if (plansError) {
      return {
        success: false,
        errors: { _form: `Erro ao buscar planos ativos: ${plansError.message}` },
      };
    }

    // Buscar total de alunos matriculados em planos ativos
    const { count: totalEnrolledStudents, error: membershipsError } = await supabase
      .from('memberships')
      .select('id', { count: 'exact', head: true })
      .eq('tenant_id', tenantId)
      .eq('status', 'active');

    if (membershipsError) {
      return {
        success: false,
        errors: { _form: `Erro ao buscar matrículas ativas: ${membershipsError.message}` },
      };
    }

    // Buscar dados para calcular ticket médio e plano mais popular
    // Primeiro, tentar usar a função RPC se disponível
    let averageTicket = 0;
    let mostPopularPlan = 'Nenhum';

    try {
      const { data: planAnalytics, error: analyticsError } = await supabase.rpc('get_plan_usage_analytics');

      if (!analyticsError && planAnalytics && planAnalytics.length > 0) {
        // Filtrar apenas planos ativos para o cálculo
        const activePlansAnalytics = planAnalytics.filter((plan: any) => plan.status === 'active');

        if (activePlansAnalytics.length > 0) {
          // Calcular ticket médio baseado na receita total dividida pelo número de matrículas ativas
          const totalRevenue = activePlansAnalytics.reduce((sum: number, plan: any) => sum + (plan.total_revenue || 0), 0);
          const totalActiveMembers = activePlansAnalytics.reduce((sum: number, plan: any) => sum + (plan.active_memberships || 0), 0);

          if (totalActiveMembers > 0) {
            averageTicket = totalRevenue / totalActiveMembers;
          }

          // Encontrar plano mais popular (com mais matrículas ativas)
          const mostPopular = activePlansAnalytics.reduce((prev: any, current: any) =>
            (current.active_memberships > prev.active_memberships) ? current : prev
          );

          if (mostPopular && mostPopular.active_memberships > 0) {
            mostPopularPlan = mostPopular.title;
          }
        }
      } else {
        // Fallback: buscar dados diretamente das tabelas
        const { data: plansWithMemberships, error: fallbackError } = await supabase
          .from('plans')
          .select(`
            id,
            title,
            status,
            pricing_config,
            memberships(
              id,
              status
            )
          `)
          .eq('tenant_id', tenantId)
          .eq('status', 'active');

        if (!fallbackError && plansWithMemberships && plansWithMemberships.length > 0) {
          let maxMemberships = 0;
          let popularPlan = '';

          plansWithMemberships.forEach((plan: any) => {
            const activeMemberships = plan.memberships?.filter((m: any) => m.status === 'active').length || 0;

            // Verificar se é o plano mais popular
            if (activeMemberships > maxMemberships) {
              maxMemberships = activeMemberships;
              popularPlan = plan.title;
            }
          });

          if (maxMemberships > 0) {
            mostPopularPlan = popularPlan;
          }

          // Para o ticket médio, vamos usar uma estimativa baseada no pricing_config dos planos ativos
          const plansWithPricing = plansWithMemberships.filter((plan: any) =>
            plan.pricing_config?.amount && plan.memberships?.some((m: any) => m.status === 'active')
          );

          if (plansWithPricing.length > 0) {
            const totalAmount = plansWithPricing.reduce((sum: number, plan: any) => {
              const amount = parseFloat(plan.pricing_config?.amount || '0');
              const activeMembers = plan.memberships?.filter((m: any) => m.status === 'active').length || 0;
              return sum + (amount * activeMembers);
            }, 0);

            const totalMembers = plansWithPricing.reduce((sum: number, plan: any) => {
              return sum + (plan.memberships?.filter((m: any) => m.status === 'active').length || 0);
            }, 0);

            if (totalMembers > 0) {
              averageTicket = totalAmount / totalMembers;
            }
          }
        }
      }
    } catch (error) {
      console.warn('Erro ao buscar analytics, usando valores padrão:', error);
      // Continuar com valores padrão
    }

    const metrics: PlanMetrics = {
      activePlansCount: activePlansCount || 0,
      totalEnrolledStudents: totalEnrolledStudents || 0,
      averageTicket: Math.round(averageTicket * 100) / 100, // Arredondar para 2 casas decimais
      mostPopularPlan,
    };

    return {
      success: true,
      data: metrics,
    };
  } catch (error) {
    console.error('Erro ao buscar métricas dos planos:', error);
    return {
      success: false,
      errors: { _form: 'Erro interno do servidor' },
    };
  }
}