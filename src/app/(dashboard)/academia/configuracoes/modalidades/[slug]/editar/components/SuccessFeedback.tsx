'use client';

import { useEffect, useState } from 'react';
import { Check, Sparkles } from 'lucide-react';

interface SuccessFeedbackProps {
  show: boolean;
  onComplete?: () => void;
}

export function SuccessFeedback({ show, onComplete }: SuccessFeedbackProps) {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    if (show) {
      setIsVisible(true);
      const timer = setTimeout(() => {
        setIsVisible(false);
        onComplete?.();
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [show, onComplete]);

  if (!isVisible) return null;

  return (
    <div className="fixed top-4 right-4 z-50 animate-in slide-in-from-right-full duration-500">
      {/* Barra de progresso */}
      <div className="mt-3 w-full bg-green-100 dark:bg-green-800 rounded-full h-1 overflow-hidden">
        <div
          className="bg-green-500 dark:bg-green-400 h-1 rounded-full ease-linear"
          style={{
            width: isVisible ? '0%' : '100%',
            transition: 'width 3s linear'
          }}
        />
      </div>
    </div>
  );
}
