'use client'

import { useState, useEffect } from 'react'
import { toast } from 'sonner'
import { CheckoutFormProps, CheckoutState } from '../types/checkout-types'
import { getCheckoutData, generatePixQRCode, confirmPayment } from '../actions/checkout-actions'
import { PaymentInfo } from './PaymentInfo'
import { PixQRCode } from './PixQRCode'
import { PaymentConfirmation } from './PaymentConfirmation'
import { CheckoutSkeleton } from './CheckoutSkeleton'
import { ErrorState } from './ErrorState'
import { SuccessState } from './SuccessState'
import { AlertCircle } from 'lucide-react'
import { Alert, AlertDescription } from '@/components/ui/alert'

export function CheckoutForm({ paymentId, userId }: CheckoutFormProps) {
  const [state, setState] = useState<CheckoutState>({
    step: 'loading'
  })

  // Carregar dados do checkout
  useEffect(() => {
    loadCheckoutData()
  }, [paymentId, userId])

  const loadCheckoutData = async () => {
    try {
      setState({ step: 'loading' })

      const result = await getCheckoutData({ paymentId, userId })
      
      if (!result.success) {
        setState({
          step: 'error',
          error: result.error
        })
        return
      }

      // Gerar QR Code PIX
      const qrCodeResult = await generatePixQRCode({ paymentId })
      
      if (!qrCodeResult.success) {
        setState({
          step: 'error',
          error: qrCodeResult.error
        })
        return
      }

      setState({
        step: 'ready',
        data: result.data,
        qrCodeData: qrCodeResult.data
      })

    } catch (error) {
      console.error('Erro ao carregar checkout:', error)
      setState({
        step: 'error',
        error: 'Erro inesperado ao carregar dados'
      })
    }
  }

  const handleConfirmPayment = async () => {
    if (state.step !== 'ready') return

    try {
      setState(prev => ({ ...prev, step: 'confirming' }))

      const result = await confirmPayment({ paymentId })
      
      if (!result.success) {
        toast.error(result.error || 'Erro ao confirmar pagamento')
        setState(prev => ({ ...prev, step: 'ready' }))
        return
      }

      setState(prev => ({ ...prev, step: 'confirmed' }))
      toast.success(result.message || 'Pagamento confirmado com sucesso!')

    } catch (error) {
      console.error('Erro ao confirmar pagamento:', error)
      toast.error('Erro inesperado ao confirmar pagamento')
      setState(prev => ({ ...prev, step: 'ready' }))
    }
  }

  // Estado de loading
  if (state.step === 'loading') {
    return <CheckoutSkeleton />
  }

  // Estado de erro
  if (state.step === 'error') {
    return (
      <ErrorState
        message={state.error || 'Ocorreu um erro inesperado'}
        onRetry={loadCheckoutData}
      />
    )
  }

  // Estado confirmado
  if (state.step === 'confirmed') {
    return (
      <SuccessState
        message="Sua confirmação foi enviada. O administrador da academia será notificado para validar o pagamento em breve."
        paymentAmount={state.data?.payment.amount}
        studentName={state.data?.student.name}
        showViewPayments={true}
      />
    )
  }

  // Estado pronto para pagamento ou confirmando
  if ((state.step === 'ready' || state.step === 'confirming') && state.data && state.qrCodeData) {
    return (
      <div className="space-y-6">
        {/* Layout Horizontal - Desktop e Tablet */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Coluna Esquerda - Informações do Pagamento */}
          <div className="space-y-6">
            <PaymentInfo
              payment={state.data.payment}
              plan={state.data.plan}
              student={state.data.student}
            />

            {/* Alerta informativo */}
            <Alert className="border-l-4 border-l-blue-500 bg-blue-50 dark:bg-blue-950/30 border-blue-200 dark:border-blue-800">
              <AlertCircle className="h-4 w-4 text-blue-600 dark:text-blue-400" />
              <AlertDescription className="text-blue-800 dark:text-blue-200">
                Após realizar o pagamento via PIX, clique no botão "Confirmar Pagamento"
                abaixo para notificar a academia. O pagamento será validado manualmente
                pela academia.
              </AlertDescription>
            </Alert>

            {/* Confirmação de Pagamento - Visível apenas no desktop */}
            <div className="hidden lg:block">
              <PaymentConfirmation
                paymentId={paymentId}
                onConfirm={handleConfirmPayment}
                loading={state.step === 'confirming'}
                disabled={state.step !== 'ready'}
              />
            </div>

            {/* Confirmação de Pagamento - Visível apenas no mobile */}
            <div className="lg:hidden">
              <PaymentConfirmation
                paymentId={paymentId}
                onConfirm={handleConfirmPayment}
                loading={state.step === 'confirming'}
                disabled={state.step !== 'ready'}
              />
            </div>
            
          </div>

          {/* Coluna Direita - QR Code PIX */}
          <div className="space-y-6">
            <PixQRCode
              qrCodeData={state.qrCodeData}
              loading={false}
            />

            
          </div>
        </div>
      </div>
    )
  }

  return null
}
