import { Card } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { CheckCircle, Loader2, AlertTriangle } from 'lucide-react'
import { PaymentConfirmationProps } from '../types/checkout-types'

export function PaymentConfirmation({
  paymentId,
  onConfirm,
  loading = false,
  disabled = false
}: PaymentConfirmationProps) {
  return (
    <Card className="p-6 border border-gray-200 dark:border-gray-700 shadow-sm">
      <div className="space-y-4">
        {/* Header */}
        <div className="flex items-center gap-3">
          <div className="p-2 bg-gradient-to-br from-orange-100 to-orange-200 dark:from-orange-900/30 dark:to-orange-800/30 rounded-full">
            <CheckCircle className="w-5 h-5 text-orange-600 dark:text-orange-400" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
              Confirmar <PERSON>
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Clique no botão abaixo após realizar o pagamento
            </p>
          </div>
        </div>

        {/* Instruções - Mais compactas */}
        <div className="bg-gradient-to-r from-amber-50 to-orange-50 dark:from-amber-900/20 dark:to-orange-900/20 border border-amber-200 dark:border-amber-800 rounded-lg p-4">
          <div className="flex gap-3">
            <AlertTriangle className="w-5 h-5 text-amber-600 dark:text-amber-400 flex-shrink-0 mt-0.5" />
            <div className="space-y-2 text-sm">
              <p className="font-medium text-gray-800 dark:text-gray-200">
                Passos importantes:
              </p>
              <ul className="space-y-1 text-gray-700 dark:text-gray-300 text-xs">
                <li>• Realize o pagamento via PIX</li>
                <li>• Aguarde confirmação no seu banco</li>
                <li>• Clique em "Confirmar" após processamento</li>
                <li>• Aguarde validação do administrador</li>
              </ul>
            </div>
          </div>
        </div>

        {/* Botão de confirmação */}
        <div className="flex justify-center pt-2">
          <Button
            onClick={onConfirm}
            disabled={disabled || loading}
            size="lg"
            className="w-full"
          >
            {loading ? (
              <>
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                Confirmando...
              </>
            ) : (
              <>
                <CheckCircle className="w-4 h-4 mr-2" />
                Confirmar Pagamento
              </>
            )}
          </Button>
        </div>

        {/* Texto de apoio */}
        <p className="text-xs text-center text-gray-500 dark:text-gray-400 bg-gray-50 dark:bg-gray-800/50 rounded-lg py-2 px-3 border border-gray-200 dark:border-gray-700">
          Ao confirmar, você declara que realizou o pagamento via PIX.
          <br />
          O status será atualizado após validação do administrador.
        </p>
      </div>
    </Card>
  )
}
