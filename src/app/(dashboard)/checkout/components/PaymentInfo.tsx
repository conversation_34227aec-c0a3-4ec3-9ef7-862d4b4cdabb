import { Card } from '@/components/ui/card'
import { Separator } from '@/components/ui/separator'
import { Badge } from '@/components/ui/badge'
import { Receipt, User, Calendar, CreditCard } from 'lucide-react'
import { format } from 'date-fns'
import { ptBR } from 'date-fns/locale'
import { PaymentInfoProps } from '../types/checkout-types'

export function PaymentInfo({ payment, plan, student }: PaymentInfoProps) {
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(amount)
  }

  const formatDate = (dateString: string | null | undefined) => {
    if (!dateString) return 'Não definido'
    try {
      return format(new Date(dateString), "dd 'de' MMMM 'de' yyyy", { locale: ptBR })
    } catch {
      return 'Data inválida'
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'pending':
        return <Badge variant="outline" className="text-yellow-600 border-yellow-600">Pendente</Badge>
      case 'awaiting_confirmation':
        return <Badge variant="outline" className="text-blue-600 border-blue-600">Aguardando Confirmação</Badge>
      case 'paid':
        return <Badge variant="outline" className="text-green-600 border-green-600">Pago</Badge>
      case 'overdue':
        return <Badge variant="outline" className="text-red-600 border-red-600">Vencido</Badge>
      case 'cancelled':
        return <Badge variant="outline" className="text-gray-600 border-gray-600">Cancelado</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  return (
    <Card className="p-6 border border-gray-200 dark:border-gray-700 shadow-sm">
      <div className="space-y-4">
        {/* Header */}
        <div className="flex items-center gap-3">
          <div className="p-2 bg-gradient-to-br from-blue-100 to-blue-200 dark:from-blue-900/30 dark:to-blue-800/30 rounded-full">
            <Receipt className="w-5 h-5 text-blue-600 dark:text-blue-400" />
          </div>
          <div className="flex-1">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
              Detalhes do Pagamento
            </h2>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Confirme as informações antes de prosseguir
            </p>
          </div>
          {getStatusBadge(payment.status)}
        </div>

        <Separator />

        {/* Valor em destaque */}
        <div className="bg-gray-50 dark:bg-gray-800/50 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <CreditCard className="w-5 h-5 text-gray-600 dark:text-gray-400" />
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Valor Total</span>
            </div>
            <div className="text-2xl font-bold text-gray-900 dark:text-gray-100">
              {formatCurrency(payment.amount)}
            </div>
          </div>
        </div>

        {/* Payment Details Grid - Mais compacto */}
        <div className="grid grid-cols-1 gap-4">
          {/* Vencimento */}
          <div className="flex items-center justify-between py-2 border-b border-gray-200 dark:border-gray-700">
            <div className="flex items-center gap-2 text-sm font-medium text-gray-700 dark:text-gray-300">
              <Calendar className="w-4 h-4" />
              Vencimento
            </div>
            <div className="text-sm text-gray-900 dark:text-gray-100 font-medium">
              {formatDate(payment.due_date)}
            </div>
          </div>

          {/* Aluno */}
          <div className="flex items-center justify-between py-2 border-b border-gray-200 dark:border-gray-700">
            <div className="flex items-center gap-2 text-sm font-medium text-gray-700 dark:text-gray-300">
              <User className="w-4 h-4" />
              Aluno
            </div>
            <div className="text-right">
              <div className="text-sm text-gray-900 dark:text-gray-100 font-medium">
                {student.name}
              </div>
              <div className="text-xs text-gray-600 dark:text-gray-400">
                {student.email}
              </div>
            </div>
          </div>

          {/* Plano */}
          <div className="flex items-center justify-between py-2">
            <div className="flex items-center gap-2 text-sm font-medium text-gray-700 dark:text-gray-300">
              <Receipt className="w-4 h-4" />
              Plano
            </div>
            <div className="text-right">
              <div className="text-sm text-gray-900 dark:text-gray-100 font-medium">
                {plan.title}
              </div>
              <div className="text-xs text-gray-600 dark:text-gray-400">
                {plan.billing_period}
              </div>
            </div>
          </div>
        </div>

        {/* Descrição */}
        {payment.description && (
          <>
            <Separator />
            <div className="bg-gray-50 dark:bg-gray-800/50 rounded-lg p-3 border border-gray-200 dark:border-gray-700">
              <div className="text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
                Descrição
              </div>
              <div className="text-sm text-gray-900 dark:text-gray-100">
                {payment.description}
              </div>
            </div>
          </>
        )}
      </div>
    </Card>
  )
}
