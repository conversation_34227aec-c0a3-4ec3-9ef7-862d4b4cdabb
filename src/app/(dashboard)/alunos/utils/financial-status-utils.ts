import { FinancialStatus } from "../server/financial-status";

// Type for Badge variants
type BadgeVariant = "statusActive" | "statusInactive" | "statusSuspended" | "statusCompleted" | "outline" | "default" | "destructive" | "secondary";

/**
 * Mapeia o status financeiro para um label legível
 */
export function getFinancialStatusLabel(status: FinancialStatus | string): string {
  switch (status) {
    case "up_to_date":
      return "Em dia";
    case "pending":
      return "Pendente";
    case "overdue":
      return "Atrasado";
    case "no_data":
      return "Sem dados";
    default:
      return "Sem dados";
  }
}

/**
 * Mapeia o status financeiro para uma variante de badge
 */
export function getFinancialStatusBadgeVariant(status: FinancialStatus | string): BadgeVariant {
  switch (status) {
    case "up_to_date":
      return "statusActive";
    case "pending":
      return "statusSuspended";
    case "overdue":
      return "destructive";
    case "no_data":
      return "outline";
    default:
      return "outline";
  }
}