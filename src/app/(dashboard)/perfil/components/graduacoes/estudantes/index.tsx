'use client'

import { useState, useEffect } from 'react'
import { Card } from '@/components/ui/card'
import { Progress } from '@/components/ui/progress'
import { Button } from '@/components/ui/button'
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog";
import { BeltWithDetails } from '@/components/belt/BeltWithDetails'
import { Award, Calendar, Clock, Trophy, GraduationCap, Edit, Wrench } from 'lucide-react'
import { useAdminStatus } from '@/hooks/user/Permissions'
import { GraduationForm } from './GraduationForm'
import { EditGraduationForm } from './EditGraduationForm'
import { formatDistanceStrict, parseISO } from 'date-fns'
import { ptBR } from 'date-fns/locale'
import { useQuery } from '@tanstack/react-query'
import { CACHE_KEYS } from '@/constants/cache-keys'
import { cacheService } from '@/services/cache'
import { InstructorGraduacoesTab } from '../instrutores'
import { fixStudentsWithNullCurrentBelt } from "@/app/(dashboard)/perfil/actions/graduation-actions";
import { toast } from "sonner";
import { useRouter } from "next/navigation";

export type BeltColor = 'white' | 'blue' | 'purple' | 'brown' | 'black' | string

export interface Graduacao {
  id: string
  faixa: BeltColor
  data: Date
  professor: string
  tempoNaFaixa: string
  graus: number | null
  modalidade: string
  faixaLabel: string
  stripe_color?: string | null
  show_center_line?: boolean | null
  center_line_color?: string | null
  sort_order?: number
}

export type Requisito = {
  id: string
  nome: string
  descricao: string
  concluido: boolean
  dataConlusao?: string
  categoria: string
  progresso: number
}

export interface ProximaConquista {
  titulo: string
  descricao: string
  icon: any
  tempoEstimado: string
}

export type GraduacaoStats = {
  currentBelt: BeltColor | null
  grauAtual?: number
  proximaGraduacao: string | null
  tempoMinimo?: number
  tempoAtual?: number
  progressoCalculado?: number
  progressoTempo?: number
  tempoTreino?: string
  modalidade?: string | null
  faixaLabel?: string | null
  stripe_color?: string | null
  show_center_line?: boolean | null
  center_line_color?: string | null
  sort_order?: number
}

interface GraduacoesTabProps {
  userId: string
}

export function GraduacoesTab({ userId }: GraduacoesTabProps) {
  const [proximasConquistas, setProximasConquistas] = useState<ProximaConquista[]>([])
  const [graduationModalOpen, setGraduationModalOpen] = useState(false);
  const [editGraduationModalOpen, setEditGraduationModalOpen] = useState(false);
  const [selectedGraduation, setSelectedGraduation] = useState<Graduacao | null>(null);
  const [isFixing, setIsFixing] = useState(false);
  const router = useRouter();

  // Hook para verificar se o usuário é admin
  const { isAdmin } = useAdminStatus();

  // Sempre executar todas as queries para manter a ordem dos hooks
  const { data: userRole } = useQuery({
    queryKey: ['user-role', userId],
    queryFn: async () => {
      try {
        const response = await fetch(`/api/user/${userId}/role`)
        const data = await response.json()
        return data.role
      } catch (error) {
        console.error('Erro ao verificar role do usuário:', error)
        return 'student' // fallback para estudante
      }
    },
    staleTime: 10 * 60 * 1000, // 10 minutos
    enabled: !!userId
  })

  const { data: stats, isLoading: statsLoading } = useQuery<GraduacaoStats>({
    queryKey: CACHE_KEYS.BELTS.STATS(userId),
    queryFn: async () => {
      console.log(`[API] Buscando estatísticas de graduação para o usuário ${userId}`)

      const cachedStats = cacheService.getData<GraduacaoStats>(CACHE_KEYS.BELTS.STATS(userId))
      if (cachedStats) {
        console.log(`[CACHE] Usando estatísticas de graduação do cache: ${userId}`)
        return cachedStats
      }

      try {
        const response = await fetch(`/api/user/${userId}/belts/stats`)
        const statsData = await response.json()

        if (statsData.error) {
          throw new Error(statsData.error)
        }

        const tempoNaFaixa = statsData.time_in_belt || 0
        const tempoParaProxima = statsData.time_to_next_belt || 0
        const progressoCalculado = tempoParaProxima > 0 ? (tempoNaFaixa / tempoParaProxima) * 100 : 0

        const formattedStats = {
          tempoMinimo: tempoParaProxima,
          tempoAtual: tempoNaFaixa,
          progressoTempo: Math.min(progressoCalculado, 100),
          tempoTreino: statsData.training_time,
          grauAtual: statsData.current_degree,
          proximaGraduacao: statsData.next_promotion,
          currentBelt: statsData.current_belt || '#FFFFFF',
          modalidade: statsData.modality_name,
          faixaLabel: statsData.current_belt_label,
          stripe_color: statsData.stripe_color,
          show_center_line: statsData.show_center_line,
          center_line_color: statsData.center_line_color,
          sort_order: statsData.sort_order
        } as GraduacaoStats & { currentBelt: BeltColor }

        cacheService.setData(CACHE_KEYS.BELTS.STATS(userId), formattedStats)

        return formattedStats
      } catch (error) {
        console.error('[API] Erro ao buscar estatísticas de graduação:', error)
        const cachedStats = cacheService.getData<GraduacaoStats>(CACHE_KEYS.BELTS.STATS(userId))
        if (cachedStats) {
          console.log(`[CACHE] Usando estatísticas de graduação do cache após erro: ${userId}`)
          return cachedStats
        }
        throw error
      }
    },
    staleTime: Infinity,
    gcTime: 30 * 60 * 1000,
    refetchOnMount: false,
    refetchOnWindowFocus: false,
    enabled: !!userId && userRole !== 'instructor' && userRole !== undefined
  })

  const { data: historicoGraduacoes, isLoading: historicoLoading } = useQuery<Graduacao[]>({
    queryKey: CACHE_KEYS.BELTS.HISTORY(userId),
    queryFn: async () => {
      console.log(`[API] Buscando histórico de graduações para o usuário ${userId}`)

      const cachedHistorico = cacheService.getData<Graduacao[]>(CACHE_KEYS.BELTS.HISTORY(userId))
      if (cachedHistorico) {
        console.log(`[CACHE] Usando histórico de graduações do cache: ${userId}`)
        return cachedHistorico
      }

      try {
        const historicoResponse = await fetch(`/api/user/${userId}/belts/history`)
        const historicoData = await historicoResponse.json()

        if (historicoData.error) {
          throw new Error(historicoData.error)
        }

        const formattedHistorico = historicoData.map((graduacao: any) => ({
          id: graduacao.id,
          faixa: graduacao.belt_color,
          data: parseISO(graduacao.awarded_at),
          professor: graduacao.instructor_name,
          tempoNaFaixa: formatDistanceStrict(new Date(), parseISO(graduacao.awarded_at), { locale: ptBR, addSuffix: false }),
          graus: graduacao.degree,
          modalidade: graduacao.modality_name,
          faixaLabel: graduacao.belt_label,
          stripe_color: graduacao.stripe_color,
          show_center_line: graduacao.show_center_line,
          center_line_color: graduacao.center_line_color,
          sort_order: graduacao.sort_order
        })) as Graduacao[]

        cacheService.setData(CACHE_KEYS.BELTS.HISTORY(userId), formattedHistorico)
        console.log(`[API] Histórico de graduações atualizado para ${userId}`)

        return formattedHistorico
      } catch (error) {
        console.error('[API] Erro ao buscar histórico de graduações:', error)

        const cachedHistorico = cacheService.getData<Graduacao[]>(CACHE_KEYS.BELTS.HISTORY(userId))
        if (cachedHistorico) {
          console.log(`[CACHE] Usando histórico de graduações do cache após erro: ${userId}`)
          return cachedHistorico
        }
        throw error
      }
    },
    staleTime: Infinity,
    gcTime: 30 * 60 * 1000,
    refetchOnMount: false,
    refetchOnWindowFocus: false,
    enabled: !!userId && userRole !== 'instructor' && userRole !== undefined
  })

  const { data: requisitosFaixa, isLoading: requisitosLoading } = useQuery<Requisito[]>({
    queryKey: CACHE_KEYS.BELTS.REQUIREMENTS(userId),
    queryFn: async () => {
      console.log(`[API] Buscando requisitos de graduação para o usuário ${userId}`)

      const cachedRequisitos = cacheService.getData<Requisito[]>(CACHE_KEYS.BELTS.REQUIREMENTS(userId))
      if (cachedRequisitos) {
        console.log(`[CACHE] Usando requisitos de graduação do cache: ${userId}`)
        return cachedRequisitos
      }

      try {
        const requisitosResponse = await fetch(`/api/user/${userId}/belts/requirements`)
        const requisitosData = await requisitosResponse.json()

        if (requisitosData.error) {
          console.log('[API] Erro ao buscar requisitos:', requisitosData.error)
          return [] as Requisito[]
        }

        cacheService.setData(CACHE_KEYS.BELTS.REQUIREMENTS(userId), requisitosData)

        return requisitosData as Requisito[]
      } catch (error) {
        console.error('[API] Erro ao buscar requisitos de graduação:', error)
        const cachedRequisitos = cacheService.getData<Requisito[]>(CACHE_KEYS.BELTS.REQUIREMENTS(userId))
        if (cachedRequisitos) {
          console.log(`[CACHE] Usando requisitos de graduação do cache após erro: ${userId}`)
          return cachedRequisitos
        }
        return [] as Requisito[]
      }
    },
    staleTime: Infinity,
    gcTime: 30 * 60 * 1000,
    refetchOnMount: false,
    refetchOnWindowFocus: false,
    enabled: !!userId && userRole !== 'instructor' && userRole !== undefined
  })

  const loading = statsLoading || historicoLoading || requisitosLoading
  const faixaAtual = stats?.currentBelt || null

  useEffect(() => {
    if (stats && userRole === 'student') {
      gerarProximasConquistas()
    }
  }, [faixaAtual, stats, userRole])

  // Renderização condicional apenas no JSX, não nos hooks
  if (userRole === 'instructor') {
    return <InstructorGraduacoesTab userId={userId} />
  }

  if (userRole === undefined) {
    return (
      <div className="p-6 space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {[...Array(4)].map((_, i) => (
            <Card key={i} className="p-6">
              <div className="animate-pulse space-y-4">
                <div className="h-4 bg-gray-200 rounded w-1/3"></div>
                <div className="h-8 bg-gray-200 rounded"></div>
              </div>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  // Funções para o formulário de graduação
  const handleGraduationSuccess = () => {
    setGraduationModalOpen(false);
    // Invalidar cache para recarregar dados
    cacheService.invalidateQueries(CACHE_KEYS.BELTS.STATS(userId), true);
    cacheService.invalidateQueries(CACHE_KEYS.BELTS.HISTORY(userId), true);
    cacheService.invalidateQueries(CACHE_KEYS.BELTS.REQUIREMENTS(userId), true);
    // Forçar re-fetch das queries
    // window.location.reload(); // Solução simples para recarregar os dados
  };

  const handleGraduationCancel = () => {
    setGraduationModalOpen(false);
  };

  // Funções para o formulário de edição de graduação
  const handleEditGraduation = (graduation: Graduacao) => {
    setSelectedGraduation(graduation);
    setEditGraduationModalOpen(true);
  };

  const handleEditGraduationSuccess = () => {
    setEditGraduationModalOpen(false);
    setSelectedGraduation(null);
    // Invalidar cache para recarregar dados
    cacheService.invalidateQueries(CACHE_KEYS.BELTS.STATS(userId), true);
    cacheService.invalidateQueries(CACHE_KEYS.BELTS.HISTORY(userId), true);
    cacheService.invalidateQueries(CACHE_KEYS.BELTS.REQUIREMENTS(userId), true);
    // Forçar re-fetch das queries
    // window.location.reload(); // Solução simples para recarregar os dados
  };

  const handleEditGraduationCancel = () => {
    setEditGraduationModalOpen(false);
    setSelectedGraduation(null);
  };

  const gerarProximasConquistas = () => {
    const proximasConq: ProximaConquista[] = []

    // Próximas conquistas baseadas nos dados do banco
    if (stats?.proximaGraduacao) {
      proximasConq.push({
        titulo: 'Próxima Graduação',
        descricao: stats.proximaGraduacao,
        icon: Trophy,
        tempoEstimado: 'Baseado no progresso'
      })
    }

    setProximasConquistas(proximasConq)
  }

  const handleFixNullBelts = async () => {
    setIsFixing(true);
    try {
      const result = await fixStudentsWithNullCurrentBelt();
      if (result.success) {
        toast.success(result.data?.message || 'Correção executada com sucesso');
        console.log('Detalhes da correção:', result.data);
        
        // Recarregar os dados
        await cacheService.invalidateQueries(CACHE_KEYS.BELTS.STATS(userId), true);
        await cacheService.invalidateQueries(CACHE_KEYS.BELTS.HISTORY(userId), true);
        await cacheService.invalidateQueries(CACHE_KEYS.BELTS.REQUIREMENTS(userId), true);
        
        router.refresh();
      } else {
        toast.error(result.errors?._form || 'Erro ao executar correção');
      }
    } catch (error) {
      console.error('Erro ao executar correção:', error);
      toast.error('Erro inesperado ao executar correção');
    } finally {
      setIsFixing(false);
    }
  };

  const renderizarHistorico = () => {
    if (loading) {
      return (
        <Card className="p-6 bg-white dark:bg-slate-800 animate-pulse">
          <div className="h-32 bg-slate-200 dark:bg-slate-700 rounded-md"></div>
        </Card>
      )
    }

    if (!historicoGraduacoes || historicoGraduacoes.length === 0) {
      return (
        <Card className="p-6 bg-white dark:bg-slate-800">
          <p className="text-center text-slate-500 dark:text-slate-400 py-8">Nenhum histórico de graduação encontrado.</p>
        </Card>
      )
    }

    return (
      <Card className="p-6 bg-white dark:bg-slate-800">
        <h3 className="text-lg font-semibold mb-4 text-gray-900 dark:text-gray-100">Histórico de Graduações</h3>
        <div className="space-y-6">
          {historicoGraduacoes.map((graduacao) => (
            <div key={graduacao.id} className="flex items-start gap-6 pb-6 border-b last:border-0 border-gray-200 dark:border-gray-700">
              <BeltWithDetails color={graduacao.faixa} degree={graduacao.graus || 0} label={graduacao.faixaLabel} stripeColor={graduacao.stripe_color} showCenterLine={graduacao.show_center_line} centerLineColor={graduacao.center_line_color} size="lg" className="w-32 h-8" />

              <div className="flex-grow">
                <h4 className="text-base font-semibold text-gray-900 dark:text-gray-100">{graduacao.modalidade && graduacao.faixaLabel ? `${graduacao.modalidade} - ${graduacao.faixaLabel}` : 'Graduação não disponível'}</h4>

                <div className="mt-2 space-y-1">
                  <div className="flex items-center text-sm text-gray-500 dark:text-gray-400">
                    <Calendar className="w-4 h-4 mr-1" />
                    {graduacao.data.toLocaleDateString('pt-BR')}
                    <span className="inline-block mx-2">•</span>
                    <span>{graduacao.tempoNaFaixa}</span>
                  </div>

                  <div className="flex items-center text-sm text-gray-500 dark:text-gray-400">
                    <Award className="w-4 h-4 mr-1" />
                    {graduacao.professor}
                  </div>
                </div>
              </div>

              {/* Botão Editar - Visível apenas para admins */}
              {isAdmin && (
                <div className="flex-shrink-0">
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-8 px-3 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
                    onClick={() => handleEditGraduation(graduacao)}
                  >
                    <Edit className="w-4 h-4" />
                  </Button>
                </div>
              )}
            </div>
          ))}
        </div>
      </Card>
    )
  }

  const renderizarRequisitos = () => {
    if (loading) {
      return (
        <Card className="p-6 bg-white dark:bg-slate-800 animate-pulse">
          <div className="h-32 bg-slate-200 dark:bg-slate-700 rounded-md"></div>
        </Card>
      )
    }

    if (!requisitosFaixa || requisitosFaixa.length === 0) {
      return (
        <Card className="p-6 bg-white dark:bg-slate-800">
          <p className="text-center text-slate-500 dark:text-slate-400 py-8">
            Nenhum requisito configurado para a próxima graduação.
          </p>
        </Card>
      )
    }

    // Agrupar requisitos por categoria
    const requisitosPorCategoria = requisitosFaixa.reduce((acc, requisito) => {
      const categoria = requisito.categoria || 'geral'
      if (!acc[categoria]) {
        acc[categoria] = []
      }
      acc[categoria].push(requisito)
      return acc
    }, {} as Record<string, Requisito[]>)

    const categoriaNomes: Record<string, string> = {
      aula: 'Aulas',
      tempo: 'Tempo na Faixa',
      presenca: 'Presença',
      tecnica: 'Técnicas',
      financeiro: 'Financeiro',
      geral: 'Outros Requisitos'
    }

    const categoriaIcones: Record<string, any> = {
      aula: Clock,
      tempo: Calendar,
      presenca: Award,
      tecnica: Trophy,
      financeiro: Award,
      geral: Award
    }

    return (
      <Card className="p-6 bg-white dark:bg-slate-800">
        <h3 className="text-lg font-semibold mb-6 text-gray-900 dark:text-gray-100">
          Requisitos para {stats?.proximaGraduacao || 'Próxima Graduação'}
        </h3>
        
        <div className="space-y-6">
          {Object.entries(requisitosPorCategoria).map(([categoria, requisitos]) => {
            const Icon = categoriaIcones[categoria] || Award
            
            return (
              <div key={categoria} className="space-y-4">
                <div className="flex items-center gap-2 pb-2 border-b border-gray-200 dark:border-gray-700">
                  <Icon className="w-4 h-4 text-gray-600 dark:text-gray-400" />
                  <h4 className="font-medium text-gray-900 dark:text-gray-100">
                    {categoriaNomes[categoria] || categoria}
                  </h4>
                </div>
                
                <div className="space-y-4">
                  {requisitos.map((requisito) => (
                    <div key={requisito.id} className="space-y-2 pl-6">
                      <div className="flex justify-between items-start">
                        <div className="flex-1">
                          <div className="flex items-center gap-2">
                            <p className="text-sm font-medium text-gray-700 dark:text-gray-300">
                              {requisito.nome}
                            </p>
                            {requisito.concluido && (
                              <div className="w-4 h-4 bg-green-500 rounded-full flex items-center justify-center">
                                <Award className="w-2.5 h-2.5 text-white" />
                              </div>
                            )}
                          </div>
                          {requisito.descricao && (
                            <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                              {requisito.descricao}
                            </p>
                          )}
                        </div>
                        <span className={`text-xs font-medium px-2 py-1 rounded ${
                          requisito.concluido 
                            ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                            : requisito.progresso >= 80
                            ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
                            : 'bg-gray-100 text-gray-600 dark:bg-gray-800 dark:text-gray-400'
                        }`}>
                          {requisito.progresso}%
                        </span>
                      </div>
                      <Progress 
                        value={requisito.progresso} 
                        className={`h-2 ${
                          requisito.concluido 
                            ? 'bg-green-200 dark:bg-green-800' 
                            : 'bg-slate-200 dark:bg-slate-700'
                        }`}
                      />
                    </div>
                  ))}
                </div>
              </div>
            )
          })}
        </div>
      </Card>
    )
  }

  return (
    <div className="space-y-6 p-6 bg-slate-50 dark:bg-slate-900 rounded-b-lg">
      {/* Debug/Admin Tools - temporário */}
      {/* {isAdmin && !faixaAtual && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <h3 className="font-medium text-yellow-800 mb-2">Ferramentas de Administrador</h3>
          <p className="text-sm text-yellow-700 mb-3">
            Este aluno não possui graduação definida. Use o botão abaixo para corrigir ou o botão "Graduar" para criar uma nova graduação.
          </p>
          <AlertDialog>
            <AlertDialogTrigger asChild>
              <Button variant="outline" size="sm" disabled={isFixing}>
                <Wrench className="w-4 h-4 mr-2" />
                {isFixing ? 'Corrigindo...' : 'Corrigir Current Belt Null'}
              </Button>
            </AlertDialogTrigger>
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle>Corrigir Current Belt Null</AlertDialogTitle>
                <AlertDialogDescription>
                  Esta ação irá buscar todos os estudantes com current_belt_id null e 
                  definir para a graduação mais recente de cada um. Deseja continuar?
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel>Cancelar</AlertDialogCancel>
                <AlertDialogAction onClick={handleFixNullBelts}>
                  Executar Correção
                </AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>
        </div>
      )} */}

      {/* Card de Progresso para a Próxima Graduação */}
      <Card className="p-6 bg-white dark:bg-slate-800">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-6">
          <div className="flex items-center gap-6">
            {faixaAtual ? (
              <BeltWithDetails 
                color={faixaAtual} 
                degree={stats?.grauAtual || 0} 
                label={stats?.faixaLabel} 
                stripeColor={stats?.stripe_color} 
                showCenterLine={stats?.show_center_line} 
                centerLineColor={stats?.center_line_color} 
                size="lg" 
                className="w-48 h-12" 
              />
            ) : (
              <div className="w-48 h-12 bg-gray-200 dark:bg-gray-700 rounded-md flex items-center justify-center">
                <span className="text-xs text-gray-500 dark:text-gray-400">Sem graduação</span>
              </div>
            )}
            <div>
              <h3 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
                {!faixaAtual 
                  ? 'Aluno sem graduação'
                  : stats?.modalidade && stats?.faixaLabel 
                    ? `${stats.modalidade} - ${stats.faixaLabel}` 
                    : 'Graduação não disponível'
                }
              </h3>
              <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                {stats?.tempoTreino ? `Treinando há ${stats.tempoTreino}` : 'Iniciando treinos'}
              </p>
            </div>
          </div>
          
          {/* Botão Graduar - Visível apenas para admins */}
          {isAdmin && (
            <div className="flex-shrink-0">
              <Button
                variant="default"
                size="sm"
                className="bg-blue-600 hover:bg-blue-700 text-white"
                onClick={() => setGraduationModalOpen(true)}
              >
                <GraduationCap className="w-4 h-4 mr-2" />
                Graduar
              </Button>
            </div>
          )}
        </div>
      </Card>

      {/* Histórico de Graduações */}
      {renderizarHistorico()}

      {/* Requisitos para próxima graduação */}
      {renderizarRequisitos()}

      {/* Modal de Graduação */}
      <GraduationForm
        userId={userId}
        isOpen={graduationModalOpen}
        currentRequirements={requisitosFaixa}
        onSuccess={handleGraduationSuccess}
        onCancel={handleGraduationCancel}
      />

      {/* Modal de Edição de Graduação */}
      {selectedGraduation && (
        <EditGraduationForm
          graduation={selectedGraduation}
          isOpen={editGraduationModalOpen}
          onSuccess={handleEditGraduationSuccess}
          onCancel={handleEditGraduationCancel}
        />
      )}
    </div>
  )
}
