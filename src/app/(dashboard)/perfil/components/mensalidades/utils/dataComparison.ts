import { PaymentData, PaymentUpdateEvent } from '../types/realtime'

/**
 * Detects changes between old and new payment data
 */
export function detectPaymentChanges(
    oldData: PaymentData,
    newData: PaymentData
): PaymentUpdateEvent[] {
    const changes: PaymentUpdateEvent[] = []

    // Check for changes in payment history
    const historyChanges = detectArrayChanges(
        oldData.historicoPagamentos || [],
        newData.historicoPagamentos || [],
        'payment'
    )
    changes.push(...historyChanges)

    // Check for changes in upcoming payments
    const upcomingChanges = detectArrayChanges(
        oldData.proximosPagamentos || [],
        newData.proximosPagamentos || [],
        'payment'
    )
    changes.push(...upcomingChanges)

    // Check for payment info changes
    if (hasPaymentInfoChanged(oldData.pagamentoInfo, newData.pagamentoInfo)) {
        changes.push({
            type: 'status',
            paymentId: newData.pagamentoInfo?.id || 'unknown',
            userId: newData.pagamentoInfo?.userId || 'unknown',
            timestamp: new Date().toISOString(),
            changes: {
                old: oldData.pagamentoInfo,
                new: newData.pagamentoInfo
            }
        })
    }

    // Check for plan changes (if valorMensalidade changed)
    if (oldData.valorMensalidade !== newData.valorMensalidade) {
        changes.push({
            type: 'plan',
            paymentId: 'plan-change',
            userId: newData.pagamentoInfo?.userId || 'unknown',
            timestamp: new Date().toISOString(),
            changes: {
                oldValue: oldData.valorMensalidade,
                newValue: newData.valorMensalidade
            }
        })
    }

    return changes
}

/**
 * Detects changes in payment arrays (additions, updates, removals)
 */
function detectArrayChanges(
    oldArray: any[],
    newArray: any[],
    changeType: 'payment' | 'plan' | 'status'
): PaymentUpdateEvent[] {
    const changes: PaymentUpdateEvent[] = []

    // Create maps for efficient lookup
    const oldMap = new Map(oldArray.map(item => [item.id, item]))
    const newMap = new Map(newArray.map(item => [item.id, item]))

    // Check for new items
    newMap.forEach((newItem, id) => {
        if (!oldMap.has(id)) {
            changes.push({
                type: changeType,
                paymentId: id,
                userId: newItem.userId || 'unknown',
                timestamp: new Date().toISOString(),
                changes: {
                    action: 'created',
                    data: newItem
                }
            })
        }
    })

    // Check for updated items
    newMap.forEach((newItem, id) => {
        const oldItem = oldMap.get(id)
        if (oldItem && hasItemChanged(oldItem, newItem)) {
            changes.push({
                type: changeType,
                paymentId: id,
                userId: newItem.userId || 'unknown',
                timestamp: new Date().toISOString(),
                changes: {
                    action: 'updated',
                    old: oldItem,
                    new: newItem,
                    diff: getItemDifferences(oldItem, newItem)
                }
            })
        }
    })

    // Check for removed items
    oldMap.forEach((oldItem, id) => {
        if (!newMap.has(id)) {
            changes.push({
                type: changeType,
                paymentId: id,
                userId: oldItem.userId || 'unknown',
                timestamp: new Date().toISOString(),
                changes: {
                    action: 'removed',
                    data: oldItem
                }
            })
        }
    })

    return changes
}

/**
 * Checks if payment info has changed
 */
function hasPaymentInfoChanged(oldInfo: any, newInfo: any): boolean {
    if (!oldInfo && !newInfo) return false
    if (!oldInfo || !newInfo) return true

    // Compare key fields that indicate status changes
    const keyFields = [
        'status',
        'dueDate',
        'paidDate',
        'amount',
        'overdueDays',
        'isOverdue'
    ]

    return keyFields.some(field => oldInfo[field] !== newInfo[field])
}

/**
 * Checks if an individual item has changed
 */
function hasItemChanged(oldItem: any, newItem: any): boolean {
    if (!oldItem || !newItem) return true

    // Compare key fields that indicate changes
    const keyFields = [
        'status',
        'amount',
        'dueDate',
        'paidDate',
        'description',
        'updatedAt'
    ]

    return keyFields.some(field => {
        const oldValue = oldItem[field]
        const newValue = newItem[field]

        // Handle date comparisons
        if (field.includes('Date') || field.includes('At')) {
            return new Date(oldValue).getTime() !== new Date(newValue).getTime()
        }

        return oldValue !== newValue
    })
}

/**
 * Gets specific differences between two items
 */
function getItemDifferences(oldItem: any, newItem: any): Record<string, { old: any; new: any }> {
    const differences: Record<string, { old: any; new: any }> = {}

    const fieldsToCheck = [
        'status',
        'amount',
        'dueDate',
        'paidDate',
        'description',
        'updatedAt'
    ]

    fieldsToCheck.forEach(field => {
        const oldValue = oldItem[field]
        const newValue = newItem[field]

        if (oldValue !== newValue) {
            differences[field] = { old: oldValue, new: newValue }
        }
    })

    return differences
}

/**
 * Utility to create a hash of payment data for quick comparison
 */
export function createPaymentDataHash(data: PaymentData): string {
    const hashData = {
        historyCount: data.historicoPagamentos?.length || 0,
        upcomingCount: data.proximosPagamentos?.length || 0,
        paymentInfoStatus: data.pagamentoInfo?.status,
        valorMensalidade: data.valorMensalidade,
        lastUpdated: data.pagamentoInfo?.updatedAt
    }

    return JSON.stringify(hashData)
}

/**
 * Checks if payment data has any changes using hash comparison
 */
export function hasPaymentDataChanged(oldData: PaymentData, newData: PaymentData): boolean {
    const oldHash = createPaymentDataHash(oldData)
    const newHash = createPaymentDataHash(newData)
    return oldHash !== newHash
}