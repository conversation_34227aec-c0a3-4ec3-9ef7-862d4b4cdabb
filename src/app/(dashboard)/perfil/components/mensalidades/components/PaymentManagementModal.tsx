'use client'

import { useState, useEffect } from "react"
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  <PERSON>alogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Calendar } from "@/components/ui/calendar"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import {
  Settings,
  Calendar as CalendarIcon,
  DollarSign,
  CreditCard,
  Loader2,
  Save,
  X,
  CheckCircle
} from "lucide-react"
import { format, parse } from "date-fns"
import { ptBR } from "date-fns/locale"
import { cn } from "@/lib/utils"
import { toast } from "sonner"
import { Skeleton } from "@/components/ui/skeleton"
import { getActiveTenantPaymentMethodsForSelect } from "@/app/(dashboard)/financeiro/formas-pagamento/actions/payment-method-actions"
import {
  paymentManagementModalSchema,
  PaymentManagementModalData,
} from "@/services/billing/payment-schemas"
import { ZodError } from "zod"

interface PaymentData {
  id: string
  amount: number
  currency: string
  status: string
  payment_method?: string
  paid_at?: string
  due_date?: string
  description?: string
}

interface PaymentManagementModalProps {
  isOpen: boolean
  onClose: () => void
  paymentData: PaymentData | null
  onSave: (updatedData: Partial<PaymentData>) => Promise<void>
  onConfirm?: (paymentId: string) => Promise<void>
  onUpdate?: () => void
  loading?: boolean
}

interface PaymentMethodOption {
  value: string
  label: string
}

const statusOptions = [
  { value: 'pending', label: 'Pendente', color: 'bg-yellow-100 text-yellow-800' },
  { value: 'paid', label: 'Pago', color: 'bg-green-100 text-green-800' },
  { value: 'awaiting_confirmation', label: 'Aguardando Confirmação', color: 'bg-blue-100 text-blue-800' },
  { value: 'overdue', label: 'Atrasado', color: 'bg-red-100 text-red-800' },
  { value: 'cancelled', label: 'Cancelado', color: 'bg-gray-100 text-gray-800' },
]

type StatusType = 'pending' | 'paid' | 'awaiting_confirmation' | 'overdue' | 'cancelled'

function PaymentManagementSkeleton() {
  return (
    <div className="space-y-6 py-4">
      {/* Cabeçalho */}
      <div className="space-y-2">
        <Skeleton className="h-6 w-56" />
        <Skeleton className="h-4 w-64" />
      </div>

      {/* Cartão de status atual */}
      <Skeleton className="h-16 w-full rounded-lg" />

      <Separator />

      {/* Valor */}
      <Skeleton className="h-12 w-full" />

      {/* Status */}
      <Skeleton className="h-12 w-full" />

      {/* Data de Vencimento */}
      <Skeleton className="h-12 w-full" />

      {/* Data de Pagamento */}
      <Skeleton className="h-12 w-full" />

      {/* Método de Pagamento */}
      <Skeleton className="h-12 w-full" />

      {/* Descrição */}
      <Skeleton className="h-24 w-full" />
    </div>
  )
}

export function PaymentManagementModal({
  isOpen,
  onClose,
  paymentData,
  onSave,
  onConfirm,
  onUpdate,
  loading = false
}: PaymentManagementModalProps) {
  const [formData, setFormData] = useState<Partial<PaymentManagementModalData>>({
    amount: '',
    status: 'pending',
    payment_method: '',
    paid_at: undefined,
    due_date: undefined,
    description: '',
  })
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isConfirming, setIsConfirming] = useState(false)
  const [paymentMethodOptions, setPaymentMethodOptions] = useState<PaymentMethodOption[]>([])
  const [showPaidAtCalendar, setShowPaidAtCalendar] = useState(false)
  const [showDueDateCalendar, setShowDueDateCalendar] = useState(false)

  useEffect(() => {
    async function fetchOptions() {
      if (isOpen) {
        const options = await getActiveTenantPaymentMethodsForSelect();
        setPaymentMethodOptions(options);
      }
    }
    fetchOptions();
  }, [isOpen]);

  const formatToCurrency = (value: number) =>
    new Intl.NumberFormat('pt-BR', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(value)

  const handleCurrencyChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const numericValue = e.target.value.replace(/\D/g, '')
    const valueInReais = Number(numericValue) / 100
    setFormData((prev) => ({ ...prev, amount: formatToCurrency(valueInReais) }))
  }

  // Preencher formulário quando paymentData mudar
  useEffect(() => {
    if (paymentData) {
      setFormData({
        amount: formatToCurrency(paymentData.amount),
        status: paymentData.status as StatusType,
        payment_method: paymentData.payment_method || '',
        paid_at: paymentData.paid_at ? new Date(paymentData.paid_at) : undefined,
        due_date: paymentData.due_date
          ? parse(paymentData.due_date, 'yyyy-MM-dd', new Date())
          : undefined,
        description: paymentData.description || '',
      })
      setErrors({})
    }
  }, [paymentData])

  const handleStatusChange = (newStatus: StatusType) => {
    setFormData(prev => {
      const updatedState = { ...prev, status: newStatus };

      if (newStatus !== 'paid') {
        updatedState.paid_at = undefined;
        updatedState.payment_method = '';
      }
      
      return updatedState;
    });
  };

  const validate = (): boolean => {
    try {
      const dataToValidate = { ...formData };
      if (dataToValidate.status !== 'paid') {
        delete dataToValidate.paid_at;
        delete dataToValidate.payment_method;
      }
      paymentManagementModalSchema.parse(dataToValidate)
      setErrors({})
      return true
    } catch (error) {
      if (error instanceof ZodError) {
        const fieldErrors: Record<string, string> = {}
        error.errors.forEach((err) => {
          if (err.path[0]) {
            fieldErrors[err.path[0]] = err.message
          }
        })
        setErrors(fieldErrors)
      }
      return false
    }
  }

  const handleSubmit = async () => {
    if (!paymentData || !validate()) return

    try {
      setIsSubmitting(true)
      
      const updatedData: Partial<PaymentData> = {
        amount: parseFloat(
          (formData.amount || '')
            .replace(/\./g, '') // remove thousands separators
            .replace(',', '.')   // replace decimal comma with dot
        ),
        status: formData.status,
        payment_method: formData.status === 'paid' ? formData.payment_method || undefined : undefined,
        paid_at: formData.status === 'paid' ? (formData.paid_at ? formData.paid_at.toISOString() : undefined) : undefined,
        due_date: formData.due_date ? formData.due_date.toISOString().split('T')[0] : undefined,
        description: formData.description || undefined,
      }

      await onSave(updatedData)
      toast.success('Pagamento atualizado com sucesso!')
      onUpdate?.()
      onClose()
    } catch (error) {
      console.error('Erro ao salvar pagamento:', error)
      toast.error('Erro ao atualizar pagamento')
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleConfirmPayment = async () => {
    if (!paymentData || !onConfirm) return

    try {
      setIsConfirming(true)
      await onConfirm(paymentData.id)
      toast.success('Pagamento confirmado com sucesso!')
      onUpdate?.()
      onClose()
    } catch (error) {
      console.error('Erro ao confirmar pagamento:', error)
      toast.error('Erro ao confirmar pagamento')
    } finally {
      setIsConfirming(false)
    }
  }

  const handleClose = () => {
    if (!isSubmitting && !isConfirming) {
      setErrors({})
      onClose()
    }
  }

  const getStatusBadge = (status: string) => {
    const statusOption = statusOptions.find(opt => opt.value === status)
    if (!statusOption) return null
    
    return (
      <Badge className={cn("text-xs", statusOption.color)}>
        {statusOption.label}
      </Badge>
    )
  }

  // Exibir skeleton se estiver carregando e dados não disponíveis
  if (!paymentData && !loading) return null

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        {loading ? (
          <PaymentManagementSkeleton />
        ) : (
          <>
            <DialogHeader>
              <DialogTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5" />
                Gerenciar Pagamento
              </DialogTitle>
              <DialogDescription>
                Altere as propriedades do pagamento conforme necessário. As alterações serão aplicadas imediatamente.
              </DialogDescription>
            </DialogHeader>

            <div className="space-y-6 py-4">
              {/* Status Atual */}
              <div className="flex items-center justify-between p-4 bg-slate-50 dark:bg-slate-800 rounded-lg">
                <div>
                  <p className="text-sm font-medium">Status Atual</p>
                  <p className="text-xs text-muted-foreground">ID: {paymentData!.id}</p>
                </div>
                {getStatusBadge(paymentData!.status)}
              </div>

              <Separator />

              {/* Valor */}
              <div className="space-y-2">
                <Label htmlFor="amount" className="text-sm font-medium">
                  Valor
                </Label>
                <div className="relative">
                  <span className="absolute left-4 top-1/2 -translate-y-1/2 text-muted-foreground">R$</span>
                  <Input
                    id="amount"
                    type="text"
                    value={formData.amount || ''}
                    onChange={handleCurrencyChange}
                    placeholder="0,00"
                    className="pl-12"
                  />
                </div>
                {errors.amount && <p className="text-sm text-red-500 mt-1">{errors.amount}</p>}
              </div>

              {/* Status */}
              <div className="space-y-2">
                <Label>Status do Pagamento</Label>
                <Select value={formData.status} onValueChange={(value) => handleStatusChange(value as StatusType)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Selecione o status" />
                  </SelectTrigger>
                  <SelectContent>
                    {statusOptions.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        <div className="flex items-center gap-2">
                          <Badge className={cn("text-xs", option.color)}>
                            {option.label}
                          </Badge>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {errors.status && <p className="text-sm text-red-500 mt-1">{errors.status}</p>}
              </div>

              {/* Data de Vencimento */}
              <div className="space-y-2">
                <Label className="flex items-center gap-2">
                  <CalendarIcon className="h-4 w-4" />
                  Data para Vencimento
                </Label>
                <Popover open={showDueDateCalendar} onOpenChange={setShowDueDateCalendar}>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className={cn(
                        "w-full justify-start text-left font-normal",
                        !formData.due_date && "text-muted-foreground"
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {formData.due_date ? format(formData.due_date, "dd/MM/yyyy", { locale: ptBR }) : "Selecionar data"}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      mode="single"
                      selected={formData.due_date}
                      onSelect={(date) => {
                        setFormData(prev => ({ ...prev, due_date: date || undefined }))
                        setShowDueDateCalendar(false)
                      }}
                      locale={ptBR}
                    />
                  </PopoverContent>
                </Popover>
                {errors.due_date && <p className="text-sm text-red-500 mt-1">{errors.due_date}</p>}
              </div>

              {/* Data de Pagamento (só aparece se status for 'paid') */}
              {formData.status === 'paid' && (
                <div className="space-y-2">
                  <Label className="flex items-center gap-2">
                    <CalendarIcon className="h-4 w-4" />
                    Data do Pagamento
                  </Label>
                  <Popover open={showPaidAtCalendar} onOpenChange={setShowPaidAtCalendar}>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        className={cn(
                          "w-full justify-start text-left font-normal",
                          !formData.paid_at && "text-muted-foreground"
                        )}
                      >
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {formData.paid_at ? format(formData.paid_at, "dd/MM/yyyy", { locale: ptBR }) : "Selecionar data"}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0" align="start">
                      <Calendar
                        mode="single"
                        selected={formData.paid_at}
                        onSelect={(date) => {
                          setFormData(prev => ({ ...prev, paid_at: date || undefined }))
                          setShowPaidAtCalendar(false)
                        }}
                        locale={ptBR}
                      />
                    </PopoverContent>
                  </Popover>
                  {errors.paid_at && <p className="text-sm text-red-500 mt-1">{errors.paid_at}</p>}
                </div>
              )}

              {/* Método de Pagamento (exibido apenas quando status for 'paid') */}
              {formData.status === 'paid' && (
                <div className="space-y-2">
                  <Label className="flex items-center gap-2">
                    <CreditCard className="h-4 w-4" />
                    Método de Pagamento
                  </Label>
                  <Select value={formData.payment_method} onValueChange={(value) => setFormData(prev => ({ ...prev, payment_method: value }))}>
                    <SelectTrigger>
                      <SelectValue placeholder="Selecione o método" />
                    </SelectTrigger>
                    <SelectContent>
                      {paymentMethodOptions.map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {errors.payment_method && <p className="text-sm text-red-500 mt-1">{errors.payment_method}</p>}
                </div>
              )}

              {/* Descrição */}
              <div className="space-y-2">
                <Label htmlFor="description">Descrição/Observações</Label>
                <Textarea
                  id="description"
                  value={formData.description || ''}
                  onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                  placeholder="Adicione observações sobre este pagamento..."
                  rows={3}
                />
                {errors.description && <p className="text-sm text-red-500 mt-1">{errors.description}</p>}
              </div>
            </div>

            <DialogFooter>
              <Button
                variant="outline"
                onClick={handleClose}
                disabled={isSubmitting || isConfirming}
              >
                <X className="mr-2 h-4 w-4" />
                Cancelar
              </Button>

              {/* Botão de confirmar pagamento - só aparece se status for awaiting_confirmation */}
              {paymentData?.status === 'awaiting_confirmation' && onConfirm && (
                <Button
                  onClick={handleConfirmPayment}
                  disabled={isSubmitting || isConfirming || loading}
                  className="bg-green-600 hover:bg-green-700"
                >
                  {isConfirming ? (
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  ) : (
                    <CheckCircle className="mr-2 h-4 w-4" />
                  )}
                  Confirmar Pagamento
                </Button>
              )}

              <Button
                onClick={handleSubmit}
                disabled={isSubmitting || isConfirming || loading}
              >
                {isSubmitting ? (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                ) : (
                  <Save className="mr-2 h-4 w-4" />
                )}
                Salvar Alterações
              </Button>
            </DialogFooter>
          </>
        )}
      </DialogContent>
    </Dialog>
  )
}
