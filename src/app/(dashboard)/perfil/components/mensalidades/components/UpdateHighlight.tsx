'use client'

import { useEffect, useState } from 'react'
import { cn } from "@/lib/utils"

interface UpdateHighlightProps {
  children: React.ReactNode
  isUpdated: boolean
  duration?: number
  className?: string
  highlightColor?: 'blue' | 'green' | 'yellow' | 'red'
}

export function UpdateHighlight({ 
  children, 
  isUpdated, 
  duration = 2000,
  className,
  highlightColor = 'blue'
}: UpdateHighlightProps) {
  const [showHighlight, setShowHighlight] = useState(false)

  useEffect(() => {
    if (isUpdated) {
      setShowHighlight(true)
      const timer = setTimeout(() => {
        setShowHighlight(false)
      }, duration)

      return () => clearTimeout(timer)
    }
  }, [isUpdated, duration])

  const getHighlightClasses = () => {
    const baseClasses = "transition-all duration-500 ease-in-out"
    
    if (!showHighlight) return baseClasses

    switch (highlightColor) {
      case 'green':
        return `${baseClasses} bg-green-100 dark:bg-green-900/20 border-green-200 dark:border-green-800 shadow-sm`
      case 'yellow':
        return `${baseClasses} bg-yellow-100 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800 shadow-sm`
      case 'red':
        return `${baseClasses} bg-red-100 dark:bg-red-900/20 border-red-200 dark:border-red-800 shadow-sm`
      case 'blue':
      default:
        return `${baseClasses} bg-blue-100 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800 shadow-sm`
    }
  }

  return (
    <div className={cn(getHighlightClasses(), className)}>
      {children}
      {showHighlight && (
        <div className="absolute top-0 right-0 -mt-1 -mr-1">
          <div className="flex h-2 w-2">
            <span className="animate-ping absolute inline-flex h-full w-full rounded-full bg-blue-400 opacity-75"></span>
            <span className="relative inline-flex rounded-full h-2 w-2 bg-blue-500"></span>
          </div>
        </div>
      )}
    </div>
  )
}

/**
 * Hook to detect when data has been updated
 */
export function useUpdateAnimation(data: any, dependencies: any[] = []) {
  const [isUpdated, setIsUpdated] = useState(false)
  
  useEffect(() => {
    if (data) {
      setIsUpdated(true)
      const timer = setTimeout(() => setIsUpdated(false), 2000)
      return () => clearTimeout(timer)
    }
  }, dependencies)
  
  return isUpdated
}