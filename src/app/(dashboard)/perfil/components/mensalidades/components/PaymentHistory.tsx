import { Card } from "@/components/ui/card"
import { PaymentHistoryProps } from "../types/types"
import { PaymentItem } from "./PaymentItem"
import { LoadingState } from "./LoadingState"
import { EmptyState } from "./EmptyState"

export function PaymentHistory({
  historicoPagamentos,
  loading,
  onUpdate,
}: PaymentHistoryProps) {
  if (loading) {
    return <LoadingState />
  }

  if (historicoPagamentos.length === 0) {
    return <EmptyState message="Nenhum histórico de pagamento encontrado." />
  }

  return (
    <Card className="p-6 bg-white dark:bg-slate-800">
      <h3 className="text-lg font-semibold mb-4 text-gray-900 dark:text-gray-100">
        Histórico de Pagamentos
      </h3>
      <div className="space-y-4">
        {historicoPagamentos.map((pagamento) => (
          <PaymentItem
            key={pagamento.id}
            pagamento={pagamento}
            showReceipt={true}
            onUpdate={onUpdate}
          />
        ))}
      </div>
    </Card>
  )
}
