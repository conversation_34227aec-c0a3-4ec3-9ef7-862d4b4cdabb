import { Card } from "@/components/ui/card"
import { StatCardProps } from "../types/types"

export function StatCard({ icon, title, value, subtitle, iconBgColor }: StatCardProps) {
  return (
    <Card className="p-4 bg-white dark:bg-slate-800">
      <div className="flex items-center gap-4">
        <div className={`p-3 ${iconBgColor} rounded-lg`}>
          {icon}
        </div>
        <div>
          <p className="text-sm text-gray-500">{title}</p>
          <p className="text-lg font-semibold text-gray-900 dark:text-gray-100">
            {value}
          </p>
          <p className="text-xs text-gray-500">
            {subtitle}
          </p>
        </div>
      </div>
    </Card>
  )
}
