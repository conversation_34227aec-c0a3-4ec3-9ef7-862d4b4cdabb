'use client'

import { useState } from "react"
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Separator } from "@/components/ui/separator"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import {
  CreditCard,
  Users,
  Check,
  ArrowRight,
  Loader2,
  RefreshCw
} from "lucide-react"
import { PlanChangeModalProps } from "../types/types"

// Função para extrair informações de preço baseado no tipo de plano
const getPlanPriceInfo = (plan: any) => {
  // Se o plano tem pricing_config, usar essa estrutura
  if (plan.pricing_config) {
    const pricingConfig = plan.pricing_config
    // Suportar tanto formato português (tipo) quanto inglês (type)
    const tipo = pricingConfig.tipo || pricingConfig.type || 'recurring'

    if (tipo === 'one-time') {
      // Suportar tanto formato português (custo) quanto inglês (amount)
      const price = pricingConfig.custo || pricingConfig.amount || 0
      return {
        price: price,
        billing_period: 'Pagamento único',
        type: 'one-time',
        icon: CreditCard,
        iconColor: 'text-purple-600',
        bgColor: 'bg-purple-50 dark:bg-purple-900/20',
        borderColor: 'border-purple-200 dark:border-purple-800'
      }
    } else if (tipo === 'recurring') {
      // Suportar tanto formato português (valor) quanto inglês (amount)
      const price = pricingConfig.valor || pricingConfig.amount || 0

      // Determinar billing_period baseado na frequência
      let billingPeriod = plan.billing_period || 'Periodo'

      if (pricingConfig.frequency || pricingConfig.frequencia) {
        const frequency = pricingConfig.frequency || pricingConfig.frequencia
        const freqNumber = pricingConfig.frequency_number || pricingConfig.numeroFrequencia || 1

        const frequencyMap: Record<string, string> = {
          'day': freqNumber === 1 ? 'Diário' : `${freqNumber} dias`,
          'week': freqNumber === 1 ? 'Semanal' : `${freqNumber} semanas`,
          'month': freqNumber === 1 ? 'Mensal' : `${freqNumber} meses`,
          'year': freqNumber === 1 ? 'Anual' : `${freqNumber} anos`,
        }

        billingPeriod = frequencyMap[frequency] || billingPeriod
      }

      return {
        price: price,
        billing_period: billingPeriod,
        type: 'recurring',
        icon: RefreshCw,
        iconColor: 'text-blue-600',
        bgColor: 'bg-blue-50 dark:bg-blue-900/20',
        borderColor: 'border-blue-200 dark:border-blue-800'
      }
    }
  }

  // Fallback para estrutura antiga
  return {
    price: plan.price || 0,
    billing_period: plan.billing_period || 'Periodo',
    type: 'recurring',
    icon: RefreshCw,
    iconColor: 'text-blue-600',
    bgColor: 'bg-blue-50 dark:bg-blue-900/20',
    borderColor: 'border-blue-200 dark:border-blue-800'
  }
}

export function PlanChangeModal({
  isOpen,
  onClose,
  currentPlan,
  availablePlans,
  onPlanChange,
  loading = false
}: PlanChangeModalProps) {
  const [selectedPlanId, setSelectedPlanId] = useState<string | null>(null)
  const [reason, setReason] = useState('')

  const handleConfirm = async () => {
    if (selectedPlanId) {
      await onPlanChange(selectedPlanId, reason.trim() || undefined)
    }
  }

  const handleClose = () => {
    setSelectedPlanId(null)
    setReason('')
    onClose()
  }

  const activePlans = availablePlans.filter(plan => plan.status === 'active')
  const selectedPlan = activePlans.find(plan => plan.id === selectedPlanId)

  // Separar planos por tipo
  const recurringPlans = activePlans.filter(plan => {
    const planInfo = getPlanPriceInfo(plan)
    return planInfo.type === 'recurring'
  })

  const oneTimePlans = activePlans.filter(plan => {
    const planInfo = getPlanPriceInfo(plan)
    return planInfo.type === 'one-time'
  })

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-4xl max-h-[85vh] sm:max-h-[90vh] w-[95vw] sm:w-full mx-4 sm:mx-auto flex flex-col">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <CreditCard className="h-5 w-5" />
            {currentPlan ? 'Alterar Plano' : 'Atribuir Plano'}
          </DialogTitle>
          <DialogDescription>
            {currentPlan 
              ? 'Selecione um novo plano para este aluno. A alteração será aplicada imediatamente.'
              : 'Selecione um plano para atribuir a este aluno.'
            }
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-3 sm:space-y-4 px-1 sm:px-0 flex-1 overflow-hidden">
          {/* Plano Atual */}
          {currentPlan && (() => {
            const currentPlanInfo = getPlanPriceInfo(currentPlan)
            const CurrentIcon = currentPlanInfo.icon

            return (
              <div className={`p-3 sm:p-4 rounded-lg border ${currentPlanInfo.bgColor} ${currentPlanInfo.borderColor}`}>
                <h3 className="font-medium text-gray-900 dark:text-gray-100 mb-3">
                  Plano Atual
                </h3>
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-2">
                      <CurrentIcon className={`h-4 w-4 ${currentPlanInfo.iconColor}`} />
                      <h4 className="font-semibold text-gray-900 dark:text-gray-100">
                        {currentPlan.title}
                      </h4>
                      <Badge variant="outline" className="text-xs">
                        Atual
                      </Badge>
                      <Badge
                        variant="secondary"
                        className={`text-xs ${currentPlanInfo.iconColor} bg-transparent`}
                      >
                        {currentPlanInfo.type === 'one-time' ? 'Único' : 'Recorrente'}
                      </Badge>
                    </div>
                    <p className="text-lg font-bold text-gray-900 dark:text-gray-100">
                      R$ {currentPlanInfo.price.toFixed(2)}
                      {currentPlanInfo.type === 'recurring' && (
                        <span className="text-sm font-normal text-gray-600 dark:text-gray-400 ml-1">
                          / {currentPlanInfo.billing_period}
                        </span>
                      )}
                      {currentPlanInfo.type === 'one-time' && (
                        <span className="text-sm font-normal text-gray-600 dark:text-gray-400 ml-1">
                          (pagamento único)
                        </span>
                      )}
                    </p>
                  </div>
                </div>
              </div>
            )
          })()}

          {/* Lista de Planos Disponíveis */}
          <div>
            <h3 className="font-medium text-gray-900 dark:text-gray-100 mb-4">
              Planos Disponíveis
            </h3>

            {activePlans.length === 0 ? (
              <div className="text-center py-8 text-gray-500 dark:text-gray-400">
                <CreditCard className="h-12 w-12 mx-auto mb-3 opacity-50" />
                <p>Nenhum plano ativo disponível</p>
              </div>
            ) : (
              <ScrollArea className="h-[180px] sm:h-[300px] pr-2 sm:pr-4">
                <div className="space-y-4">
                  {/* Planos Recorrentes */}
                  {recurringPlans.length > 0 && (
                    <div>
                      <div className="flex items-center gap-2 mb-3">
                        <RefreshCw className="h-4 w-4 text-blue-600" />
                        <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">
                          Planos Recorrentes
                        </h4>
                      </div>
                      <div className="space-y-2 sm:space-y-3">
                        {recurringPlans.map((plan) => {
                          const isCurrentPlan = currentPlan?.id === plan.id
                          const isSelected = selectedPlanId === plan.id
                          const planInfo = getPlanPriceInfo(plan)
                          const PlanIcon = planInfo.icon

                          return (
                            <div
                              key={plan.id}
                              className={`
                                p-3 sm:p-4 rounded-lg border cursor-pointer transition-all
                                ${isCurrentPlan
                                  ? `${planInfo.borderColor} ${planInfo.bgColor} opacity-50 cursor-not-allowed`
                                  : isSelected
                                    ? 'border-green-300 bg-green-100 dark:border-green-600 dark:bg-green-900/20'
                                    : 'border-gray-200 hover:border-gray-300 dark:border-gray-700 dark:hover:border-gray-600'
                                }
                              `}
                              onClick={() => {
                                if (!isCurrentPlan) {
                                  setSelectedPlanId(plan.id)
                                }
                              }}
                            >
                              <div className="flex items-start justify-between">
                                <div className="flex-1">
                                  <div className="flex items-center gap-2 mb-2">
                                    <PlanIcon className={`h-4 w-4 ${planInfo.iconColor}`} />
                                    <h4 className="font-semibold text-gray-900 dark:text-gray-100">
                                      {plan.title}
                                    </h4>
                                    {isCurrentPlan && (
                                      <Badge variant="outline" className="text-xs">
                                        Atual
                                      </Badge>
                                    )}
                                    {isSelected && !isCurrentPlan && (
                                      <Check className="h-4 w-4 text-green-600 dark:text-green-400" />
                                    )}
                                  </div>

                                  <p className="text-lg font-bold text-gray-900 dark:text-gray-100 mb-2">
                                    R$ {planInfo.price.toFixed(2)}
                                    <span className="text-sm font-normal text-gray-600 dark:text-gray-400 ml-1">
                                      / {planInfo.billing_period}
                                    </span>
                                  </p>

                                  {/* Modalidades */}
                                  {plan.modalities && plan.modalities.length > 0 && (
                                    <div className="mb-3">
                                      <p className="text-xs font-medium text-gray-700 dark:text-gray-300 mb-1 flex items-center gap-1">
                                        <Users className="h-3 w-3" />
                                        Modalidades
                                      </p>
                                      <div className="flex flex-wrap gap-1">
                                        {plan.modalities.map((modality, index) => (
                                          <Badge key={index} variant="secondary" className="text-xs">
                                            {modality}
                                          </Badge>
                                        ))}
                                      </div>
                                    </div>
                                  )}

                                  {/* Benefícios */}
                                  {plan.benefits && plan.benefits.length > 0 && (
                                    <div>
                                      <p className="text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
                                        Benefícios
                                      </p>
                                      <ul className="text-xs text-gray-600 dark:text-gray-400 space-y-1">
                                        {plan.benefits.slice(0, 3).map((benefit, index) => (
                                          <li key={index} className="flex items-center gap-1">
                                            <Check className="h-3 w-3 text-green-600 dark:text-green-400 flex-shrink-0" />
                                            {benefit}
                                          </li>
                                        ))}
                                        {plan.benefits.length > 3 && (
                                          <li className="text-gray-500 dark:text-gray-500">
                                            +{plan.benefits.length - 3} benefícios adicionais
                                          </li>
                                        )}
                                      </ul>
                                    </div>
                                  )}
                                </div>
                              </div>
                            </div>
                          )
                        })}
                      </div>
                    </div>
                  )}

                  {/* Planos de Pagamento Único */}
                  {oneTimePlans.length > 0 && (
                    <div>
                      <div className="flex items-center gap-2 mb-3">
                        <CreditCard className="h-4 w-4 text-purple-600" />
                        <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">
                          Planos de Pagamento Único
                        </h4>
                      </div>
                      <div className="space-y-2 sm:space-y-3">
                        {oneTimePlans.map((plan) => {
                          const isCurrentPlan = currentPlan?.id === plan.id
                          const isSelected = selectedPlanId === plan.id
                          const planInfo = getPlanPriceInfo(plan)
                          const PlanIcon = planInfo.icon

                          return (
                            <div
                              key={plan.id}
                              className={`
                                p-3 sm:p-4 rounded-lg border cursor-pointer transition-all
                                ${isCurrentPlan
                                  ? `${planInfo.borderColor} ${planInfo.bgColor} opacity-50 cursor-not-allowed`
                                  : isSelected
                                    ? 'border-green-300 bg-green-100 dark:border-green-600 dark:bg-green-900/20'
                                    : 'border-gray-200 hover:border-gray-300 dark:border-gray-700 dark:hover:border-gray-600'
                                }
                              `}
                              onClick={() => {
                                if (!isCurrentPlan) {
                                  setSelectedPlanId(plan.id)
                                }
                              }}
                            >
                              <div className="flex items-start justify-between">
                                <div className="flex-1">
                                  <div className="flex items-center gap-2 mb-2">
                                    <PlanIcon className={`h-4 w-4 ${planInfo.iconColor}`} />
                                    <h4 className="font-semibold text-gray-900 dark:text-gray-100">
                                      {plan.title}
                                    </h4>
                                    {isCurrentPlan && (
                                      <Badge variant="outline" className="text-xs">
                                        Atual
                                      </Badge>
                                    )}
                                    {isSelected && !isCurrentPlan && (
                                      <Check className="h-4 w-4 text-green-600 dark:text-green-400" />
                                    )}
                                  </div>

                                  <p className="text-lg font-bold text-gray-900 dark:text-gray-100 mb-2">
                                    R$ {planInfo.price.toFixed(2)}
                                    <span className="text-sm font-normal text-gray-600 dark:text-gray-400 ml-1">
                                      (pagamento único)
                                    </span>
                                  </p>

                                  {/* Modalidades */}
                                  {plan.modalities && plan.modalities.length > 0 && (
                                    <div className="mb-3">
                                      <p className="text-xs font-medium text-gray-700 dark:text-gray-300 mb-1 flex items-center gap-1">
                                        <Users className="h-3 w-3" />
                                        Modalidades
                                      </p>
                                      <div className="flex flex-wrap gap-1">
                                        {plan.modalities.map((modality, index) => (
                                          <Badge key={index} variant="secondary" className="text-xs">
                                            {modality}
                                          </Badge>
                                        ))}
                                      </div>
                                    </div>
                                  )}

                                  {/* Benefícios */}
                                  {plan.benefits && plan.benefits.length > 0 && (
                                    <div>
                                      <p className="text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
                                        Benefícios
                                      </p>
                                      <ul className="text-xs text-gray-600 dark:text-gray-400 space-y-1">
                                        {plan.benefits.slice(0, 3).map((benefit, index) => (
                                          <li key={index} className="flex items-center gap-1">
                                            <Check className="h-3 w-3 text-green-600 dark:text-green-400 flex-shrink-0" />
                                            {benefit}
                                          </li>
                                        ))}
                                        {plan.benefits.length > 3 && (
                                          <li className="text-gray-500 dark:text-gray-500">
                                            +{plan.benefits.length - 3} benefícios adicionais
                                          </li>
                                        )}
                                      </ul>
                                    </div>
                                  )}
                                </div>
                              </div>
                            </div>
                          )
                        })}
                      </div>
                    </div>
                  )}
                </div>
              </ScrollArea>
            )}
          </div>

          {/* Resumo da Alteração */}
          {selectedPlan && currentPlan && (() => {
            const currentPlanInfo = getPlanPriceInfo(currentPlan)
            const selectedPlanInfo = getPlanPriceInfo(selectedPlan)

            return (
              <>
                <Separator />
                <div className="p-3 sm:p-4 bg-gray-100 dark:bg-gray-700 rounded-lg">
                  <h4 className="font-medium text-gray-900 dark:text-gray-100 mb-3">
                    Resumo da Alteração
                  </h4>
                  <div className="flex flex-col sm:flex-row items-center justify-between gap-3 sm:gap-0 text-sm">
                    <div className="text-center flex-1">
                      <p className="font-medium text-gray-900 dark:text-gray-100">
                        {currentPlan.title}
                      </p>
                      <p className="text-gray-600 dark:text-gray-400">
                        R$ {currentPlanInfo.price.toFixed(2)}
                        {currentPlanInfo.type === 'recurring' && ` / ${currentPlanInfo.billing_period}`}
                        {currentPlanInfo.type === 'one-time' && ' (único)'}
                      </p>
                      <p className="text-xs text-gray-500 dark:text-gray-500">
                        {currentPlanInfo.type === 'one-time' ? 'Pagamento único' : 'Recorrente'}
                      </p>
                    </div>
                    <ArrowRight className="h-4 w-4 text-gray-400 rotate-90 sm:rotate-0" />
                    <div className="text-center flex-1">
                      <p className="font-medium text-gray-900 dark:text-gray-100">
                        {selectedPlan.title}
                      </p>
                      <p className="text-gray-600 dark:text-gray-400">
                        R$ {selectedPlanInfo.price.toFixed(2)}
                        {selectedPlanInfo.type === 'recurring' && ` / ${selectedPlanInfo.billing_period}`}
                        {selectedPlanInfo.type === 'one-time' && ' (único)'}
                      </p>
                      <p className="text-xs text-gray-500 dark:text-gray-500">
                        {selectedPlanInfo.type === 'one-time' ? 'Pagamento único' : 'Recorrente'}
                      </p>
                    </div>
                  </div>
                </div>
              </>
            )
          })()}
        </div>

        {/* Campo de motivo opcional */}
        {selectedPlanId && (
          <div className="space-y-2 pt-4 border-t">
            <Label htmlFor="reason" className="text-sm font-medium">
              Motivo da alteração (opcional)
            </Label>
            <Textarea
              id="reason"
              placeholder="Ex: Solicitação do aluno, mudança de necessidades..."
              value={reason}
              onChange={(e) => setReason(e.target.value)}
              disabled={loading}
              className="min-h-[80px] resize-none"
              maxLength={500}
            />
            {reason && (
              <p className="text-xs text-muted-foreground text-right">
                {reason.length}/500 caracteres
              </p>
            )}
          </div>
        )}

        <DialogFooter className="flex flex-col-reverse sm:flex-row gap-2 sm:gap-0 pt-4 sm:pt-6 border-t mt-auto flex-shrink-0">
          <Button
            variant="outline"
            onClick={handleClose}
            disabled={loading}
            className="w-full sm:w-auto"
          >
            Cancelar
          </Button>
          <Button
            onClick={handleConfirm}
            disabled={!selectedPlanId || loading}
            className="flex items-center justify-center gap-2 w-full sm:w-auto"
          >
            {loading && <Loader2 className="h-4 w-4 animate-spin" />}
            {currentPlan ? 'Alterar Plano' : 'Atribuir Plano'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
