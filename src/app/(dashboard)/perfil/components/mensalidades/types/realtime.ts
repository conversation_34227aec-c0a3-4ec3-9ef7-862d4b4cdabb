// Real-time payment update types

export interface PaymentData {
  historicoPagamentos: any[]
  proximosPagamentos: any[]
  pagamentoInfo: any
  valorMensalidade: number
}

export interface PaymentUpdateEvent {
  type: 'payment' | 'plan' | 'status'
  paymentId: string
  userId: string
  timestamp: string
  changes: any
}

export interface PollingState {
  interval: number
  isActive: boolean
  lastPoll: Date | null
  errorCount: number
  backoffMultiplier: number
}

export interface ErrorState {
  hasError: boolean
  errorType: 'network' | 'api' | 'timeout' | 'unknown'
  errorMessage: string
  retryCount: number
  nextRetryAt: Date | null
}

export interface VisibilityState {
  isTabVisible: boolean
  isWindowFocused: boolean
  isComponentMounted: boolean
}

export interface PollingMetrics {
  totalPolls: number
  successfulPolls: number
  failedPolls: number
  averageResponseTime: number
  dataChangesDetected: number
  lastPollingSession: {
    startTime: Date
    endTime: Date
    pollCount: number
  }
}

export interface RealtimeConfig {
  enabled: boolean
  pollingIntervals: {
    activeFocused: number
    activeUnfocused: number
    inactive: number
    background: number
  }
  maxRetryAttempts: number
  backoffMultiplier: number
  maxBackoffDelay: number
}