export type StatusPagamento = 'pago' | 'pendente' | 'atrasado' | 'awaiting_confirmation'

export type StatusMensalidade = 'em_dia' | 'atrasado' | 'pendente'

export interface Pagamento {
  id: string
  data: Date
  valor: number
  status: StatusPagamento
  metodoPagamento: string
  comprovante?: string
  tipo?: string
  tipoLabel?: string
  descricao?: string
}

export interface PagamentoInfo {
  totalPago: number
  proximoVencimento: Date | null
  diasParaVencimento: number
  metodoPreferido: string
  metodoPreferidoSlug?: string
  statusMensalidade: StatusMensalidade
}

export interface MensalidadesTabProps {
  userId: string
}

export interface PaymentStatusProps {
  pagamentoInfo: PagamentoInfo
  valorMensalidade: number
  loading: boolean
  currentPlan?: StudentPlan | null
  userId: string
  onUpdate?: () => void
}

export interface PaymentStatsProps {
  pagamentoInfo: PagamentoInfo
  historicoPagamentos: Pagamento[]
  loading: boolean
}

export interface StudentPlan {
  id: string
  membership_id: string
  title: string
  status: 'active' | 'paused' | 'canceled' | 'expired'
  price: number
  billing_period: string
  start_date: string
  end_date?: string
  next_billing_date?: string
  modalities: string[]
  pricing_config?: any // Configuração de preço completa do plano
}

export interface AvailablePlan {
  id: string
  title: string
  price: number
  billing_period: string
  modalities: string[]
  benefits: string[]
  status: 'active' | 'draft' | 'paused' | 'archived'
  pricing_config?: any // Configuração de preço completa do plano
}

export interface StudentPlanProps {
  userId: string
  loading?: boolean
  onPlanUpdate?: () => void
}

export interface PlanChangeModalProps {
  isOpen: boolean
  onClose: () => void
  currentPlan: StudentPlan | null
  availablePlans: AvailablePlan[]
  onPlanChange: (planId: string, reason?: string) => Promise<void>
  loading?: boolean
}

export interface StatCardProps {
  icon: React.ReactNode
  title: string
  value: string
  subtitle: string
  iconBgColor: string
}

export interface PaymentHistoryProps {
  historicoPagamentos: Pagamento[]
  loading: boolean
  onUpdate?: () => void
}

export interface UpcomingPaymentsProps {
  proximosPagamentos: Pagamento[]
  loading: boolean
  onUpdate?: () => void
}

export interface PaymentItemProps {
  pagamento: Pagamento
  showReceipt?: boolean
  onUpdate?: () => void
}

export interface PaymentInfoProps {
  pagamentoInfo: PagamentoInfo
}

export interface LoadingStateProps {
  height?: string
}

export interface EmptyStateProps {
  message: string
}

// Constantes para mapeamento de status
export const statusColor: Record<StatusPagamento, string> = {
  pago: 'bg-green-500',
  pendente: 'bg-yellow-500',
  atrasado: 'bg-red-500',
  awaiting_confirmation: 'bg-blue-500'
}

export const statusText: Record<StatusPagamento, string> = {
  pago: 'Pago',
  pendente: 'Pendente',
  atrasado: 'Atrasado',
  awaiting_confirmation: 'Aguardando Confirmação'
}

export const statusMensalidadeText: Record<StatusMensalidade, string> = {
  em_dia: 'Em dia',
  atrasado: 'Atrasado',
  pendente: 'Aguardando vencimento'
}

export const statusMensalidadeClassName: Record<StatusMensalidade, string> = {
  em_dia: 'text-green-600 border-green-600',
  atrasado: 'text-red-600 border-red-600',
  pendente: 'text-amber-600 border-amber-600'
}
