'use client'

import { useEffect, useState, useCallback, useRef } from 'react'
import { useQuery, useQueryClient } from '@tanstack/react-query'
import { useVisibilityChange } from './useVisibilityChange'
import { detectPaymentChanges } from '../utils/dataComparison'
import { PaymentData, PaymentUpdateEvent, PollingState, ErrorState } from '../types/realtime'
import { REALTIME_CONFIG } from '../config/realtime'

interface UseRealtimePaymentsConfig {
    userId: string
    enabled?: boolean
    onUpdate?: (type: 'payment' | 'plan' | 'status', data: any) => void
}

interface UseRealtimePaymentsReturn {
    // Existing payment data
    historicoPagamentos: any[]
    proximosPagamentos: any[]
    pagamentoInfo: any
    valorMensalidade: number

    // Real-time specific
    isPolling: boolean
    lastUpdate: Date | null
    connectionStatus: 'connected' | 'disconnected' | 'error'

    // Control methods
    pausePolling: () => void
    resumePolling: () => void
    forceRefresh: () => Promise<void>

    // Loading and error states
    loading: boolean
    error: string | null
}

const { POLLING_INTERVALS, MAX_RETRY_ATTEMPTS, BACKOFF_MULTIPLIER, MAX_BACKOFF_DELAY } = REALTIME_CONFIG

export function useRealtimePayments({
    userId,
    enabled = true,
    onUpdate
}: UseRealtimePaymentsConfig): UseRealtimePaymentsReturn {
    const queryClient = useQueryClient()
    const { isTabVisible, isWindowFocused } = useVisibilityChange()

    const [pollingState, setPollingState] = useState<PollingState>({
        interval: POLLING_INTERVALS.ACTIVE_FOCUSED,
        isActive: true,
        lastPoll: null,
        errorCount: 0,
        backoffMultiplier: 1
    })

    const [errorState, setErrorState] = useState<ErrorState>({
        hasError: false,
        errorType: 'unknown',
        errorMessage: '',
        retryCount: 0,
        nextRetryAt: null
    })

    const [lastUpdate, setLastUpdate] = useState<Date | null>(null)
    const previousDataRef = useRef<PaymentData | null>(null)
    const pollingTimeoutRef = useRef<NodeJS.Timeout | null>(null)

    // Calculate current polling interval based on visibility
    const calculatePollingInterval = useCallback((): number => {
        if (!isTabVisible) return POLLING_INTERVALS.BACKGROUND
        if (!isWindowFocused) return POLLING_INTERVALS.INACTIVE
        return POLLING_INTERVALS.ACTIVE_FOCUSED
    }, [isTabVisible, isWindowFocused])

    // Payment data query with polling
    const {
        data: paymentData,
        isLoading,
        error: queryError,
        refetch
    } = useQuery({
        queryKey: ['payments', userId, 'realtime'],
        queryFn: async (): Promise<PaymentData> => {
            // Use the existing payment API endpoints
            const timestamp = new Date().getTime()

            // Fetch payment info
            const infoResponse = await fetch(`/api/user/${userId}/payments/info?t=${timestamp}`, {
                cache: 'no-store'
            })
            const infoData = await infoResponse.json()

            // Fetch payment history
            const historyResponse = await fetch(`/api/user/${userId}/payments/history?t=${timestamp}`, {
                cache: 'no-store'
            })
            const historyData = await historyResponse.json()

            // Fetch upcoming payments
            const upcomingResponse = await fetch(`/api/user/${userId}/payments/upcoming?t=${timestamp}`, {
                cache: 'no-store'
            })
            const upcomingData = await upcomingResponse.json()

            return {
                historicoPagamentos: historyData || [],
                proximosPagamentos: upcomingData || [],
                pagamentoInfo: infoData || null,
                valorMensalidade: infoData?.subscription_amount || 0
            }
        },
        enabled: enabled && pollingState.isActive,
        refetchInterval: () => {
            const interval = calculatePollingInterval()
            return interval > 0 ? interval : false
        },
        refetchIntervalInBackground: false,
        refetchOnWindowFocus: true,
        staleTime: 1000, // Consider data stale after 1 second
        gcTime: 5 * 60 * 1000, // Keep in cache for 5 minutes
        retry: (failureCount, error) => {
            if (failureCount >= MAX_RETRY_ATTEMPTS) return false

            // Exponential backoff
            const delay = Math.min(
                1000 * Math.pow(BACKOFF_MULTIPLIER, failureCount),
                MAX_BACKOFF_DELAY
            )

            setTimeout(() => {
                setErrorState(prev => ({
                    ...prev,
                    retryCount: failureCount + 1,
                    nextRetryAt: new Date(Date.now() + delay)
                }))
            }, delay)

            return true
        }
    })

    // Handle successful data fetch and change detection
    useEffect(() => {
        if (paymentData && !queryError) {
            // Reset error state on successful fetch
            setErrorState({
                hasError: false,
                errorType: 'unknown',
                errorMessage: '',
                retryCount: 0,
                nextRetryAt: null
            })

            // Detect changes and trigger callbacks
            if (previousDataRef.current) {
                const changes = detectPaymentChanges(previousDataRef.current, paymentData)
                if (changes.length > 0) {
                    setLastUpdate(new Date())
                    changes.forEach(change => {
                        onUpdate?.(change.type as any, change)
                    })
                }
            }

            previousDataRef.current = paymentData
            setPollingState(prev => ({
                ...prev,
                lastPoll: new Date(),
                errorCount: 0,
                backoffMultiplier: 1
            }))
        }
    }, [paymentData, queryError, onUpdate])

    // Handle query errors
    useEffect(() => {
        if (queryError) {
            setErrorState({
                hasError: true,
                errorType: queryError instanceof Error ? 'api' : 'unknown',
                errorMessage: queryError instanceof Error ? queryError.message : 'Unknown error',
                retryCount: errorState.retryCount + 1,
                nextRetryAt: null
            })

            setPollingState(prev => ({
                ...prev,
                errorCount: prev.errorCount + 1,
                backoffMultiplier: Math.min(prev.backoffMultiplier * BACKOFF_MULTIPLIER, 8)
            }))
        }
    }, [queryError, errorState.retryCount])

    // Update polling interval when visibility changes
    useEffect(() => {
        const newInterval = calculatePollingInterval()
        setPollingState(prev => ({
            ...prev,
            interval: newInterval,
            isActive: newInterval > 0
        }))
    }, [calculatePollingInterval])

    // Control methods
    const pausePolling = useCallback(() => {
        setPollingState(prev => ({ ...prev, isActive: false }))
    }, [])

    const resumePolling = useCallback(() => {
        setPollingState(prev => ({ ...prev, isActive: true }))
    }, [])

    const forceRefresh = useCallback(async () => {
        setLastUpdate(new Date())
        await refetch()
    }, [refetch])

    // Determine connection status
    const connectionStatus = (() => {
        if (errorState.hasError) return 'error'
        if (!pollingState.isActive) return 'disconnected'
        return 'connected'
    })()

    // Extract payment data with fallbacks
    const historicoPagamentos = paymentData?.historicoPagamentos || []
    const proximosPagamentos = paymentData?.proximosPagamentos || []
    const pagamentoInfo = paymentData?.pagamentoInfo || null
    const valorMensalidade = paymentData?.valorMensalidade || 0

    return {
        // Payment data
        historicoPagamentos,
        proximosPagamentos,
        pagamentoInfo,
        valorMensalidade,

        // Real-time status
        isPolling: pollingState.isActive && !isLoading,
        lastUpdate,
        connectionStatus,

        // Control methods
        pausePolling,
        resumePolling,
        forceRefresh,

        // States
        loading: isLoading,
        error: errorState.hasError ? errorState.errorMessage : null
    }
}