'use client'

import { useEffect, useState, useCallback, useRef } from "react"
import { parseISO, differenceInDays } from "date-fns"
import { Pagamento, PagamentoInfo, StatusPagamento } from "../types/types"
import { formatPaymentMethodName } from '@/utils/payment-method-formatter'
import { useVisibilityChange } from './useVisibilityChange'
import { REALTIME_CONFIG } from '../config/realtime'

interface UsePaymentsReturn {
  historicoPagamentos: Pagamento[]
  proximosPagamentos: Pagamento[]
  pagamentoInfo: PagamentoInfo
  valorMensalidade: number
  loading: boolean
  error: string | null
  refetch: () => Promise<void>
  // Real-time specific
  isPolling: boolean
  lastUpdate: Date | null
  connectionStatus: 'connected' | 'disconnected' | 'error'
}

export function usePayments(userId: string): UsePaymentsReturn {
  const [historicoPagamentos, setHistoricoPagamentos] = useState<Pagamento[]>([])
  const [proximosPagamentos, setProximosPagamentos] = useState<Pagamento[]>([])
  const [pagamentoInfo, setPagamentoInfo] = useState<PagamentoInfo>({
    totalPago: 0,
    proximoVencimento: null,
    diasParaVencimento: 0,
    metodoPreferido: '',
    metodoPreferidoSlug: 'pix',
    statusMensalidade: 'em_dia'
  })
  const [valorMensalidade, setValorMensalidade] = useState(0)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  
  // Real-time polling state
  const [isPolling, setIsPolling] = useState(false)
  const [lastUpdate, setLastUpdate] = useState<Date | null>(null)
  const [connectionStatus, setConnectionStatus] = useState<'connected' | 'disconnected' | 'error'>('connected')
  
  const { isTabVisible, isWindowFocused } = useVisibilityChange()
  const pollingIntervalRef = useRef<NodeJS.Timeout | null>(null)
  const lastDataHashRef = useRef<string>('')

  const mapearStatusPagamento = (status: string): StatusPagamento => {
    const statusMap: Record<string, StatusPagamento> = {
      'paid': 'pago',
      'pending': 'pendente',
      'overdue': 'atrasado',
      'awaiting_confirmation': 'awaiting_confirmation'
    }

    return statusMap[status] || 'pendente'
  }

  // Create data hash for change detection
  const createDataHash = useCallback((data: any) => {
    return JSON.stringify({
      historyCount: data.historicoPagamentos?.length || 0,
      upcomingCount: data.proximosPagamentos?.length || 0,
      paymentStatus: data.pagamentoInfo?.statusMensalidade,
      totalPaid: data.pagamentoInfo?.totalPago,
      nextDue: data.pagamentoInfo?.proximoVencimento,
      amount: data.valorMensalidade
    })
  }, [])

  const carregarDadosPagamento = async (isPollingCall = false) => {
    try {
      if (!isPollingCall) {
        setLoading(true)
      }
      setError(null)
      setConnectionStatus('connected')

      // Adicionar timestamp para evitar cache
      const timestamp = new Date().getTime()

      // Carregar informações gerais de pagamento
      const infoResponse = await fetch(`/api/user/${userId}/payments/info?t=${timestamp}`, {
        cache: 'no-store'
      })
      const infoData = await infoResponse.json()
      
      if (infoData.error) {
        throw new Error(infoData.error)
      }
      
      const proximoVencimento = infoData.next_payment_due 
        ? parseISO(infoData.next_payment_due) 
        : null
        
      const diasParaVencimento = proximoVencimento 
        ? differenceInDays(proximoVencimento, new Date())
        : 0
        
      setPagamentoInfo({
        totalPago: infoData.total_paid || 0,
        proximoVencimento,
        diasParaVencimento,
        metodoPreferido: formatPaymentMethodName(infoData.preferred_method) || 'PIX',
        metodoPreferidoSlug: infoData.preferred_method_slug || 'pix',
        statusMensalidade: infoData.payment_status || 'em_dia'
      })
      
      setValorMensalidade(infoData.subscription_amount || 0)
      
      // Carregar histórico de pagamentos
      const historicoResponse = await fetch(`/api/user/${userId}/payments/history?t=${timestamp}`, {
        cache: 'no-store'
      })
      const historicoData = await historicoResponse.json()

      if (historicoData.error) {
        throw new Error(historicoData.error)
      }

      const pagamentosFormatados = historicoData.map((pagamento: any) => ({
        id: pagamento.id,
        data: parseISO(pagamento.paid_at || pagamento.created_at),
        valor: Number(pagamento.amount || 0),
        status: mapearStatusPagamento(pagamento.status),
        metodoPagamento: formatPaymentMethodName(pagamento.payment_method),
        comprovante: pagamento.receipt_url,
        tipo: pagamento.payment_type || 'manual',
        tipoLabel: pagamento.payment_type_label || 'Outros',
        descricao: pagamento.description || 'Pagamento'
      }))

      setHistoricoPagamentos(pagamentosFormatados)

      // Carregar próximos pagamentos
      const proximosResponse = await fetch(`/api/user/${userId}/payments/upcoming?t=${timestamp}`, {
        cache: 'no-store'
      })
      const proximosData = await proximosResponse.json()
      
      if (proximosData.error) {
        throw new Error(proximosData.error)
      }
      
      const proximosFormatados = proximosData.map((pagamento: any) => ({
        id: pagamento.id || `upcoming-${pagamento.due_date}`,
        data: parseISO(pagamento.due_date),
        valor: Number(pagamento.amount || 0),
        status: 'pendente' as StatusPagamento,
        metodoPagamento: formatPaymentMethodName(pagamento.payment_method),
        tipo: pagamento.payment_type || 'recurring',
        tipoLabel: pagamento.payment_type_label || 'Mensalidade',
        descricao: pagamento.description || 'Pagamento'
      }))
      
      setProximosPagamentos(proximosFormatados)

      // Check for data changes during polling
      if (isPollingCall) {
        const currentData = {
          historicoPagamentos: pagamentosFormatados,
          proximosPagamentos: proximosFormatados,
          pagamentoInfo: {
            totalPago: infoData.total_paid || 0,
            proximoVencimento,
            diasParaVencimento,
            metodoPreferido: formatPaymentMethodName(infoData.preferred_method) || 'PIX',
            metodoPreferidoSlug: infoData.preferred_method_slug || 'pix',
            statusMensalidade: infoData.payment_status || 'em_dia'
          },
          valorMensalidade: infoData.subscription_amount || 0
        }

        const currentHash = createDataHash(currentData)
        if (lastDataHashRef.current && lastDataHashRef.current !== currentHash) {
          setLastUpdate(new Date())
          console.log('Payment data updated via polling')
        }
        lastDataHashRef.current = currentHash
      }

      setConnectionStatus('connected')
    } catch (error) {
      console.error('Erro ao carregar dados de pagamento:', error)
      setError(error instanceof Error ? error.message : 'Erro desconhecido')
      setConnectionStatus('error')
      
      // Reset para valores padrão em caso de erro
      setPagamentoInfo({
        totalPago: 0,
        proximoVencimento: null,
        diasParaVencimento: 0,
        metodoPreferido: '',
        metodoPreferidoSlug: 'pix',
        statusMensalidade: 'em_dia'
      })
      setHistoricoPagamentos([])
      setProximosPagamentos([])
      setValorMensalidade(0)
    } finally {
      if (!isPollingCall) {
        setLoading(false)
      }
      setIsPolling(false)
    }
  }

  // Calculate polling interval based on visibility
  const getPollingInterval = useCallback(() => {
    if (!REALTIME_CONFIG.ENABLE_REAL_TIME) return 0 // Disabled
    if (!isTabVisible) return REALTIME_CONFIG.POLLING_INTERVALS.INACTIVE
    if (!isWindowFocused) return REALTIME_CONFIG.POLLING_INTERVALS.ACTIVE_UNFOCUSED
    return REALTIME_CONFIG.POLLING_INTERVALS.ACTIVE_FOCUSED
  }, [isTabVisible, isWindowFocused])

  // Start/stop polling based on visibility
  useEffect(() => {
    if (!userId) return

    const startPolling = () => {
      if (pollingIntervalRef.current) {
        clearInterval(pollingIntervalRef.current)
      }

      const interval = getPollingInterval()
      if (interval > 0) {
        setConnectionStatus('connected')
        pollingIntervalRef.current = setInterval(async () => {
          setIsPolling(true)
          await carregarDadosPagamento(true)
        }, interval)
      } else {
        setConnectionStatus('disconnected')
      }
    }

    // Start polling after initial load
    const timer = setTimeout(startPolling, 2000)

    return () => {
      clearTimeout(timer)
      if (pollingIntervalRef.current) {
        clearInterval(pollingIntervalRef.current)
      }
    }
  }, [userId, getPollingInterval])

  // Restart polling when visibility changes
  useEffect(() => {
    if (!userId) return

    if (pollingIntervalRef.current) {
      clearInterval(pollingIntervalRef.current)
    }

    const interval = getPollingInterval()
    if (interval > 0 && (isTabVisible || isWindowFocused)) {
      setConnectionStatus('connected')
      pollingIntervalRef.current = setInterval(async () => {
        setIsPolling(true)
        await carregarDadosPagamento(true)
      }, interval)
    } else {
      setConnectionStatus('disconnected')
    }
  }, [isTabVisible, isWindowFocused, getPollingInterval, userId])

  useEffect(() => {
    if (userId) {
      carregarDadosPagamento()
    }
  }, [userId])

  return {
    historicoPagamentos,
    proximosPagamentos,
    pagamentoInfo,
    valorMensalidade,
    loading,
    error,
    refetch: carregarDadosPagamento,
    // Real-time properties
    isPolling,
    lastUpdate,
    connectionStatus
  }
}
