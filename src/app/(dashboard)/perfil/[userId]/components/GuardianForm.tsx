'use client';

import { useState } from 'react';
import { useFormStatus } from 'react-dom';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { UserIcon, SaveIcon, XIcon } from 'lucide-react';
import { GUARDIAN_RELATIONSHIPS, GuardianValidationErrors } from '@/src/types/guardian';
import { updateGuardianInfoAction } from '@/app/(dashboard)/perfil/actions/guardian-actions';
import { SimplePhoneInput } from '@/components/shared/PhoneInput';
import { formatGuardianPhone } from '@/utils/guardian-utils';

interface GuardianFormData {
  guardian_name: string;
  guardian_email: string;
  guardian_phone: string;
  guardian_relationship: string;
  guardian_document: string;
}

interface GuardianFormProps {
  userId: string;
  initialData?: Partial<GuardianFormData>;
  onCancel: () => void;
  onSuccess?: () => void;
}

function SubmitButton() {
  const { pending } = useFormStatus();
  
  return (
    <Button type="submit" disabled={pending} className="flex-1">
      {pending ? (
        <>
          <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent mr-2" />
          Salvando...
        </>
      ) : (
        <>
          <SaveIcon className="h-4 w-4 mr-2" />
          Salvar Responsável
        </>
      )}
    </Button>
  );
}

export function GuardianForm({ userId, initialData, onCancel, onSuccess }: GuardianFormProps) {
  const [formData, setFormData] = useState<GuardianFormData>({
    guardian_name: initialData?.guardian_name || '',
    guardian_email: initialData?.guardian_email || '',
    guardian_phone: initialData?.guardian_phone || '',
    guardian_relationship: initialData?.guardian_relationship || '',
    guardian_document: initialData?.guardian_document || ''
  });
  
  const [errors, setErrors] = useState<GuardianValidationErrors>({});

  const handleInputChange = (field: keyof GuardianFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Limpar erro do campo quando o usuário começar a digitar
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  const formatCPF = (value: string) => {
    const numbers = value.replace(/\D/g, '');
    if (numbers.length <= 11) {
      return numbers.replace(/(\d{3})(\d{3})(\d{3})(\d{2})/, '$1.$2.$3-$4');
    }
    return value;
  };

  const handleCPFChange = (value: string) => {
    const formatted = formatCPF(value);
    handleInputChange('guardian_document', formatted);
  };
  
  const handlePhoneChange = (value: string) => {
    handleInputChange('guardian_phone', value);
  };

  async function handleSubmit(formDataObj: FormData) {
    setErrors({});
    
    const data = {
      userId,
      guardian_name: formData.guardian_name,
      guardian_email: formData.guardian_email,
      guardian_phone: formData.guardian_phone,
      guardian_relationship: formData.guardian_relationship,
      guardian_document: formData.guardian_document.replace(/\D/g, '')
    };

    const result = await updateGuardianInfoAction(data);
    
    if (!result.success) {
      setErrors(result.errors || {});
    } else {
      onSuccess?.();
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-gray-900 dark:text-gray-100">
          <UserIcon className="h-5 w-5" />
          Dados do Responsável Legal
        </CardTitle>
      </CardHeader>

      <CardContent>
        <form action={handleSubmit} className="space-y-4">
          {errors._form && (
            <Alert variant="destructive">
              <AlertDescription>{errors._form}</AlertDescription>
            </Alert>
          )}

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="guardian_name" className="text-gray-900 dark:text-gray-100">Nome Completo *</Label>
              <Input
                id="guardian_name"
                value={formData.guardian_name}
                onChange={(e) => handleInputChange('guardian_name', e.target.value)}
                placeholder="Digite o nome completo do responsável"
                className={errors.guardian_name ? 'border-red-500 dark:border-red-400' : ''}
              />
              {errors.guardian_name && (
                <p className="text-sm text-red-600 dark:text-red-400">{errors.guardian_name[0]}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="guardian_email" className="text-gray-900 dark:text-gray-100">E-mail *</Label>
              <Input
                id="guardian_email"
                type="email"
                value={formData.guardian_email}
                onChange={(e) => handleInputChange('guardian_email', e.target.value)}
                placeholder="<EMAIL>"
                className={errors.guardian_email ? 'border-red-500 dark:border-red-400' : ''}
              />
              {errors.guardian_email && (
                <p className="text-sm text-red-600 dark:text-red-400">{errors.guardian_email[0]}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="guardian_phone" className="text-gray-900 dark:text-gray-100">Telefone *</Label>
              <SimplePhoneInput
                value={formData.guardian_phone}
                onChange={handlePhoneChange}
                placeholder="(11) 99999-9999"
                defaultCountryCode="+55"
                className={errors.guardian_phone ? 'border-red-500 dark:border-red-400' : ''}
              />
              {errors.guardian_phone && (
                <p className="text-sm text-red-600 dark:text-red-400">{errors.guardian_phone[0]}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="guardian_relationship" className="text-gray-900 dark:text-gray-100">Relação com o Menor *</Label>
              <Select 
                value={formData.guardian_relationship}
                onValueChange={(value) => handleInputChange('guardian_relationship', value)}
              >
                <SelectTrigger className={errors.guardian_relationship ? 'border-red-500 dark:border-red-400' : ''}>
                  <SelectValue placeholder="Selecione a relação" />
                </SelectTrigger>
                <SelectContent>
                  {Object.entries(GUARDIAN_RELATIONSHIPS).map(([key, label]) => (
                    <SelectItem key={key} value={key}>
                      {label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.guardian_relationship && (
                <p className="text-sm text-red-600 dark:text-red-400">{errors.guardian_relationship[0]}</p>
              )}
            </div>

            <div className="space-y-2 md:col-span-2">
              <Label htmlFor="guardian_document" className="text-gray-900 dark:text-gray-100">CPF do Responsável *</Label>
              <Input
                id="guardian_document"
                value={formData.guardian_document}
                onChange={(e) => handleCPFChange(e.target.value)}
                placeholder="000.000.000-00"
                maxLength={14}
                className={errors.guardian_document ? 'border-red-500 dark:border-red-400' : ''}
              />
              {errors.guardian_document && (
                <p className="text-sm text-red-600 dark:text-red-400">{errors.guardian_document[0]}</p>
              )}
              <p className="text-xs text-muted-foreground">
                O CPF será usado para verificação da identidade do responsável
              </p>
            </div>
          </div>

          <div className="flex gap-3 pt-4">
            <SubmitButton />
            <Button 
              type="button" 
              variant="outline" 
              onClick={onCancel}
              className="flex-1"
            >
              <XIcon className="h-4 w-4 mr-2" />
              Cancelar
            </Button>
          </div>
        </form>

        <div className="mt-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
          <h4 className="font-medium text-blue-900 dark:text-blue-100 mb-2">Informações Importantes</h4>
          <ul className="text-sm text-blue-800 dark:text-blue-200 space-y-1">
            <li>• O responsável legal deve ter mais de 18 anos</li>
            <li>• Os dados serão usados para verificação de identidade</li>
            <li>• O responsável poderá gerenciar a conta até o estudante completar 18 anos</li>
            <li>• É possível alterar os dados do responsável a qualquer momento</li>
            <li>• O telefone deve incluir o código do país (DDI)</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  );
} 