'use server';

import { z } from 'zod';
import { createClient } from '@/services/supabase/server';
import { getCurrentUser } from '@/services/auth/actions/auth-actions';
import { listModalities } from '@/services/modalities';
import { listGraduationLevels } from '@/services/belts/levels';
import { getInstructorsForForm } from '@/app/(dashboard)/aulas/actions/form-data-actions';

// Schema para validação da graduação
const graduationSchema = z.object({
  studentId: z.string().uuid('ID de estudante inválido'),
  modalityId: z.string().uuid('ID de modalidade inválido'),
  beltLevelId: z.string().uuid('ID de nível de faixa inválido'),
  instructorId: z.string().uuid('ID de instrutor inválido'),
  awardedAt: z.string().min(1, 'Data e hora é obrigatória').transform((dateStr) => {
    // Tenta converter para Date e depois para ISO string
    const date = new Date(dateStr);
    if (isNaN(date.getTime())) {
      throw new Error('Data inválida');
    }
    return date.toISOString();
  })
});

// Schema para validação da atualização de graduação
const updateGraduationSchema = z.object({
  graduationId: z.string().uuid('ID de graduação inválido'),
  modalityId: z.string().uuid('ID de modalidade inválido'),
  beltLevelId: z.string().uuid('ID de nível de faixa inválido'),
  instructorId: z.string().uuid('ID de instrutor inválido'),
  awardedAt: z.string().min(1, 'Data e hora é obrigatória').transform((dateStr) => {
    // Tenta converter para Date e depois para ISO string
    const date = new Date(dateStr);
    if (isNaN(date.getTime())) {
      throw new Error('Data inválida');
    }
    return date.toISOString();
  })
});

export type GraduationFormData = z.infer<typeof graduationSchema>;
export type UpdateGraduationFormData = z.infer<typeof updateGraduationSchema>;

/**
 * Busca modalidades ativas para seleção
 */
export async function getModalitiesForGraduation() {
  try {
    const user = await getCurrentUser();
    if (!user) {
      return { success: false, errors: { _form: "Usuário não autenticado" } };
    }

    const tenantId = user.app_metadata?.tenant_id;
    if (!tenantId) {
      return { success: false, errors: { _form: "Tenant não identificado" } };
    }

    const modalities = await listModalities(tenantId);
    const activeModalities = modalities.filter(m => m.enabled);

    return { success: true, data: activeModalities };
  } catch (error) {
    console.error("Erro ao buscar modalidades:", error);
    return { success: false, errors: { _form: "Erro ao buscar modalidades" } };
  }
}

/**
 * Busca níveis de faixa para uma modalidade específica
 */
export async function getBeltLevelsForModality(modalitySlug: string) {
  try {
    const user = await getCurrentUser();
    if (!user) {
      return { success: false, errors: { _form: "Usuário não autenticado" } };
    }

    const tenantId = user.app_metadata?.tenant_id;
    if (!tenantId) {
      return { success: false, errors: { _form: "Tenant não identificado" } };
    }

    const levels = await listGraduationLevels(tenantId, modalitySlug);

    return { success: true, data: levels };
  } catch (error) {
    console.error("Erro ao buscar níveis de faixa:", error);
    return { success: false, errors: { _form: "Erro ao buscar níveis de faixa" } };
  }
}

/**
 * Busca instrutores disponíveis para graduação
 */
export async function getInstructorsForGraduation() {
  try {
    const result = await getInstructorsForForm();
    
    if (!result.success) {
      return result;
    }

    return { success: true, data: result.data };
  } catch (error) {
    console.error("Erro ao buscar instrutores:", error);
    return { success: false, errors: { _form: "Erro ao buscar instrutores" } };
  }
}

/**
 * Busca o student_id baseado no user_id
 */
async function getStudentId(userId: string, tenantId: string): Promise<string | null> {
  const supabase = await createClient();
  
  const { data: student, error } = await supabase
    .from('students')
    .select('id')
    .eq('user_id', userId)
    .eq('tenant_id', tenantId)
    .single();

  if (error || !student) {
    console.error('Erro ao buscar student_id:', error);
    return null;
  }

  return student.id;
}

/**
 * Gradua um estudante para uma nova faixa
 */
export async function graduateStudent(userId: string, formData: GraduationFormData) {
  try {
    // Validar dados de entrada
    const result = graduationSchema.safeParse(formData);
    if (!result.success) {
      return { 
        success: false, 
        errors: result.error.format() 
      };
    }

    const validatedData = result.data;

    // Verificar usuário atual
    const user = await getCurrentUser();
    if (!user) {
      return { success: false, errors: { _form: "Usuário não autenticado" } };
    }

    const tenantId = user.app_metadata?.tenant_id;
    if (!tenantId) {
      return { success: false, errors: { _form: "Tenant não identificado" } };
    }

    // Verificar se o usuário atual é admin
    if (user.app_metadata?.role !== 'admin') {
      return { success: false, errors: { _form: "Apenas administradores podem graduar estudantes" } };
    }

    // Buscar student_id baseado no userId
    const studentId = await getStudentId(userId, tenantId);
    if (!studentId) {
      return { success: false, errors: { _form: "Estudante não encontrado" } };
    }

    // Chamar função RPC para graduação
    const supabase = await createClient();
    
    const { data: graduationResult, error: rpcError } = await supabase
      .rpc('graduate_student_admin', {
        p_tenant_id: tenantId,
        p_student_id: studentId,
        p_belt_level_id: validatedData.beltLevelId,
        p_awarded_by: validatedData.instructorId,
        p_awarded_at: validatedData.awardedAt
      });

    if (rpcError) {
      console.error("Erro na RPC graduate_student_admin:", rpcError);
      return { 
        success: false, 
        errors: { _form: "Erro ao processar graduação" } 
      };
    }

    if (!graduationResult?.success) {
      console.error("Erro retornado pela RPC:", graduationResult?.error);
      return { 
        success: false, 
        errors: { _form: graduationResult?.error || "Erro desconhecido na graduação" } 
      };
    }

    return { 
      success: true, 
      data: graduationResult.data 
    };

  } catch (error) {
    console.error("Erro inesperado na graduação:", error);
    return { 
      success: false, 
      errors: { _form: "Erro interno do servidor" } 
    };
  }
}

/**
 * Atualiza uma graduação existente
 */
export async function updateStudentGraduation(formData: UpdateGraduationFormData) {
  try {
    // Validar dados de entrada
    const result = updateGraduationSchema.safeParse(formData);
    if (!result.success) {
      return { 
        success: false, 
        errors: result.error.format() 
      };
    }

    const validatedData = result.data;

    // Verificar usuário atual
    const user = await getCurrentUser();
    if (!user) {
      return { success: false, errors: { _form: "Usuário não autenticado" } };
    }

    const tenantId = user.app_metadata?.tenant_id;
    if (!tenantId) {
      return { success: false, errors: { _form: "Tenant não identificado" } };
    }

    // Verificar se o usuário atual é admin
    if (user.app_metadata?.role !== 'admin') {
      return { success: false, errors: { _form: "Apenas administradores podem editar graduações" } };
    }

    const supabase = await createClient();

    // Atualizar a graduação
    const { data: updatedGraduation, error } = await supabase
      .from('student_belts')
      .update({
        belt_level_id: validatedData.beltLevelId,
        awarded_by: validatedData.instructorId,
        awarded_at: validatedData.awardedAt,
        updated_at: new Date().toISOString()
      })
      .eq('id', validatedData.graduationId)
      .eq('tenant_id', tenantId)
      .select()
      .single();

    if (error) {
      console.error("Erro ao atualizar graduação:", error);
      return { 
        success: false, 
        errors: { _form: "Erro ao atualizar graduação" } 
      };
    }

    return { 
      success: true, 
      data: updatedGraduation 
    };

  } catch (error) {
    console.error("Erro inesperado ao atualizar graduação:", error);
    return { 
      success: false, 
      errors: { _form: "Erro interno do servidor" } 
    };
  }
}

/**
 * Deleta uma graduação existente e atualiza automaticamente o current_belt_id
 */
export async function deleteStudentGraduation(graduationId: string) {
  try {
    // Validar graduationId
    if (!graduationId || typeof graduationId !== 'string') {
      return { success: false, errors: { _form: "ID de graduação inválido" } };
    }

    // Verificar usuário atual
    const user = await getCurrentUser();
    if (!user) {
      return { success: false, errors: { _form: "Usuário não autenticado" } };
    }

    const tenantId = user.app_metadata?.tenant_id;
    if (!tenantId) {
      return { success: false, errors: { _form: "Tenant não identificado" } };
    }

    // Verificar se o usuário atual é admin
    if (user.app_metadata?.role !== 'admin') {
      return { success: false, errors: { _form: "Apenas administradores podem excluir graduações" } };
    }

    const supabase = await createClient();

    // Verificar se a graduação existe e buscar dados necessários
    const { data: existingGraduation, error: fetchError } = await supabase
      .from('student_belts')
      .select(`
        id,
        student_id,
        awarded_at,
        belt_level_id
      `)
      .eq('id', graduationId)
      .eq('tenant_id', tenantId)
      .single();

    if (fetchError || !existingGraduation) {
      return { 
        success: false, 
        errors: { _form: "Graduação não encontrada" } 
      };
    }

    // Buscar a graduação anterior mais recente (excluindo a que será deletada)
    const { data: previousGraduation, error: previousError } = await supabase
      .from('student_belts')
      .select('id')
      .eq('student_id', existingGraduation.student_id)
      .eq('tenant_id', tenantId)
      .neq('id', graduationId)
      .lt('awarded_at', existingGraduation.awarded_at)
      .order('awarded_at', { ascending: false })
      .limit(1)
      .maybeSingle();

    if (previousError) {
      console.error("Erro ao buscar graduação anterior:", previousError);
      return { 
        success: false, 
        errors: { _form: "Erro ao buscar graduação anterior" } 
      };
    }

    // Se não houver graduação anterior, buscar qualquer outra graduação (pode ser que estejamos deletando a mais antiga)
    let fallbackGraduation = null;
    if (!previousGraduation) {
      const { data: fallback, error: fallbackError } = await supabase
        .from('student_belts')
        .select('id')
        .eq('student_id', existingGraduation.student_id)
        .eq('tenant_id', tenantId)
        .neq('id', graduationId)
        .order('awarded_at', { ascending: true })
        .limit(1)
        .maybeSingle();

      if (!fallbackError && fallback) {
        fallbackGraduation = fallback;
      }
    }

    const newCurrentBeltId = previousGraduation?.id || fallbackGraduation?.id || null;

    // Iniciar transação: deletar a graduação e atualizar current_belt_id
    const { error: deleteError } = await supabase
      .from('student_belts')
      .delete()
      .eq('id', graduationId)
      .eq('tenant_id', tenantId);

    if (deleteError) {
      console.error("Erro ao deletar graduação:", deleteError);
      return { 
        success: false, 
        errors: { _form: "Erro ao excluir graduação" } 
      };
    }

    // Atualizar o current_belt_id do estudante
    const { error: updateError } = await supabase
      .from('students')
      .update({ 
        current_belt_id: newCurrentBeltId,
        updated_at: new Date().toISOString()
      })
      .eq('id', existingGraduation.student_id)
      .eq('tenant_id', tenantId);

    if (updateError) {
      console.error("Erro ao atualizar current_belt_id:", updateError);
      // Não retornar erro aqui pois a graduação já foi deletada
      // Log do problema para investigação posterior
    }

    console.log(`Graduação ${graduationId} deletada. Current_belt_id atualizado para: ${newCurrentBeltId || 'null'}`);

    return { 
      success: true, 
      data: { 
        id: graduationId,
        newCurrentBeltId: newCurrentBeltId,
        message: newCurrentBeltId 
          ? 'Graduação deletada e faixa atual atualizada' 
          : 'Graduação deletada - estudante sem faixas restantes'
      } 
    };

  } catch (error) {
    console.error("Erro inesperado ao deletar graduação:", error);
    return { 
      success: false, 
      errors: { _form: "Erro interno do servidor" } 
    };
  }
}

/**
 * Corrige estudantes com current_belt_id null definindo para a graduação mais recente
 * Esta função resolve problemas onde graduações foram deletadas sem atualizar o current_belt_id
 */
export async function fixStudentsWithNullCurrentBelt() {
  try {
    // Verificar usuário atual
    const user = await getCurrentUser();
    if (!user) {
      return { success: false, errors: { _form: "Usuário não autenticado" } };
    }

    const tenantId = user.app_metadata?.tenant_id;
    if (!tenantId) {
      return { success: false, errors: { _form: "Tenant não identificado" } };
    }

    // Verificar se o usuário atual é admin
    if (user.app_metadata?.role !== 'admin') {
      return { success: false, errors: { _form: "Apenas administradores podem executar esta ação" } };
    }

    const supabase = await createClient();

    // Buscar estudantes com current_belt_id null mas que têm graduações
    const { data: studentsWithNullBelt, error: studentsError } = await supabase
      .from('students')
      .select(`
        id,
        user_id,
        current_belt_id,
        users:users!students_user_id_fkey!inner(first_name, last_name)
      `)
      .eq('tenant_id', tenantId)
      .is('current_belt_id', null);

    if (studentsError) {
      console.error("Erro ao buscar estudantes:", studentsError);
      return { 
        success: false, 
        errors: { _form: "Erro ao buscar estudantes" } 
      };
    }

    if (!studentsWithNullBelt || studentsWithNullBelt.length === 0) {
      return { 
        success: true, 
        data: { 
          message: "Nenhum estudante com current_belt_id null encontrado",
          fixed: 0
        } 
      };
    }

    let fixedCount = 0;
    const results = [];

    // Para cada estudante, buscar sua graduação mais recente
    for (const student of studentsWithNullBelt) {
      const { data: latestGraduation, error: graduationError } = await supabase
        .from('student_belts')
        .select('id, awarded_at, belt_level_id')
        .eq('student_id', student.id)
        .eq('tenant_id', tenantId)
        .order('awarded_at', { ascending: false })
        .limit(1)
        .maybeSingle();

      if (graduationError) {
        console.error(`Erro ao buscar graduação do estudante ${student.id}:`, graduationError);
        results.push({
          student_id: student.id,
          student_name: `${(student.users as any)?.first_name} ${(student.users as any)?.last_name}`,
          status: 'error',
          error: graduationError.message
        });
        continue;
      }

      if (!latestGraduation) {
        results.push({
          student_id: student.id,
          student_name: `${(student.users as any)?.first_name} ${(student.users as any)?.last_name}`,
          status: 'no_graduations',
          message: 'Estudante não possui graduações'
        });
        continue;
      }

      // Atualizar o current_belt_id
      const { error: updateError } = await supabase
        .from('students')
        .update({ 
          current_belt_id: latestGraduation.id,
          updated_at: new Date().toISOString()
        })
        .eq('id', student.id)
        .eq('tenant_id', tenantId);

      if (updateError) {
        console.error(`Erro ao atualizar current_belt_id do estudante ${student.id}:`, updateError);
        results.push({
          student_id: student.id,
          student_name: `${(student.users as any)?.first_name} ${(student.users as any)?.last_name}`,
          status: 'error',
          error: updateError.message
        });
      } else {
        fixedCount++;
        results.push({
          student_id: student.id,
          student_name: `${(student.users as any)?.first_name} ${(student.users as any)?.last_name}`,
          status: 'fixed',
          new_current_belt_id: latestGraduation.id,
          awarded_at: latestGraduation.awarded_at
        });
      }
    }

    console.log(`Correção concluída. ${fixedCount} de ${studentsWithNullBelt.length} estudantes corrigidos.`);

    return { 
      success: true, 
      data: { 
        message: `${fixedCount} estudantes corrigidos com sucesso`,
        total_found: studentsWithNullBelt.length,
        fixed: fixedCount,
        results
      } 
    };

  } catch (error) {
    console.error("Erro inesperado ao corrigir estudantes:", error);
    return { 
      success: false, 
      errors: { _form: "Erro interno do servidor" } 
    };
  }
} 