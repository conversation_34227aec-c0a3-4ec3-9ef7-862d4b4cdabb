'use server';

import { revalidatePath } from 'next/cache';
import { guardianService } from '@/services/user/guardian-service';
import { 
  guardianWithCPFValidationSchema,
  updateGuardianSchema,
  setupManagedAccountSchema 
} from '@/services/user/schemas/guardian-schemas';
import { GuardianValidationErrors } from '@/src/types/guardian';

/**
 * Server Action para atualizar dados do responsável
 */
export async function updateGuardianInfoAction(data: unknown) {
  try {
    const result = updateGuardianSchema.safeParse(data);
    
    if (!result.success) {
      const errors: GuardianValidationErrors = {};
      
      result.error.errors.forEach(error => {
        const path = error.path[0] as keyof GuardianValidationErrors;
        if (path && path !== '_form') {
          if (!errors[path]) {
            errors[path] = [];
          }
          errors[path]!.push(error.message);
        }
      });
      
      return { success: false, errors };
    }

    const updateResult = await guardianService.updateGuardianInfo(result.data);
    
    if (!updateResult.success) {
      return { 
        success: false, 
        errors: { _form: updateResult.error || 'Erro ao atualizar dados do responsável' } 
      };
    }

    // Revalidar cache das páginas de perfil
    revalidatePath('/perfil');
    revalidatePath(`/perfil/${result.data.userId}`);
    
    return { success: true };
  } catch (error) {
    console.error('Erro na action updateGuardianInfo:', error);
    return { 
      success: false, 
      errors: { _form: 'Erro interno do servidor' } 
    };
  }
}

/**
 * Server Action para configurar conta administrada
 */
export async function setupManagedAccountAction(data: unknown) {
  try {
    const result = setupManagedAccountSchema.safeParse(data);
    
    if (!result.success) {
      const errors: GuardianValidationErrors = {};
      
      result.error.errors.forEach(error => {
        const path = error.path[0] as string;
        if (path.startsWith('guardianData.')) {
          const fieldName = path.replace('guardianData.', '') as keyof GuardianValidationErrors;
          if (fieldName && fieldName !== '_form') {
            if (!errors[fieldName]) {
              errors[fieldName] = [];
            }
            errors[fieldName]!.push(error.message);
          }
        }
      });
      
      return { success: false, errors };
    }

    const setupResult = await guardianService.setupManagedAccount(result.data);
    
    if (!setupResult.success) {
      return { 
        success: false, 
        errors: { _form: setupResult.error || 'Erro ao configurar conta administrada' } 
      };
    }

    // Revalidar cache das páginas relacionadas
    revalidatePath('/perfil');
    revalidatePath(`/perfil/${result.data.minorUserId}`);
    revalidatePath('/alunos');
    
    return { success: true };
  } catch (error) {
    console.error('Erro na action setupManagedAccount:', error);
    return { 
      success: false, 
      errors: { _form: 'Erro interno do servidor' } 
    };
  }
}

/**
 * Server Action para verificar se usuário tem responsável configurado
 */
export async function checkGuardianConfiguredAction(userId: string) {
  try {
    const hasGuardian = await guardianService.hasGuardianConfigured(userId);
    
    return { success: true, hasGuardian };
  } catch (error) {
    console.error('Erro na action checkGuardianConfigured:', error);
    return { 
      success: false, 
      error: 'Erro ao verificar configuração do responsável' 
    };
  }
}

/**
 * Server Action para remover dados do responsável
 */
export async function removeGuardianDataAction(userId: string) {
  try {
    const result = await guardianService.removeGuardianData(userId);
    
    if (!result.success) {
      return { 
        success: false, 
        error: result.error || 'Erro ao remover dados do responsável' 
      };
    }

    // Revalidar cache das páginas de perfil
    revalidatePath('/perfil');
    revalidatePath(`/perfil/${userId}`);
    
    return { success: true };
  } catch (error) {
    console.error('Erro na action removeGuardianData:', error);
    return { 
      success: false, 
      error: 'Erro interno do servidor' 
    };
  }
}

/**
 * Server Action para listar menores sem responsável configurado
 */
export async function getMinorsWithoutGuardianAction() {
  try {
    const minors = await guardianService.getMinorsWithoutGuardian();
    
    return { success: true, data: minors };
  } catch (error) {
    console.error('Erro na action getMinorsWithoutGuardian:', error);
    return { 
      success: false, 
      error: 'Erro ao buscar menores sem responsável' 
    };
  }
}

/**
 * Server Action para listar menores com responsável configurado
 */
export async function getMinorsWithGuardianAction() {
  try {
    const minors = await guardianService.getMinorsWithGuardian();
    
    return { success: true, data: minors };
  } catch (error) {
    console.error('Erro na action getMinorsWithGuardian:', error);
    return { 
      success: false, 
      error: 'Erro ao buscar menores com responsável' 
    };
  }
}

/**
 * Server Action para obter informações completas do usuário com dados de responsável
 */
export async function getUserWithGuardianInfoAction(userId: string) {
  try {
    const user = await guardianService.getUserWithGuardianInfo(userId);
    
    if (!user) {
      return { 
        success: false, 
        error: 'Usuário não encontrado' 
      };
    }

    return { success: true, data: user };
  } catch (error) {
    console.error('Erro na action getUserWithGuardianInfo:', error);
    return { 
      success: false, 
      error: 'Erro ao buscar dados do usuário' 
    };
  }
}

/**
 * Server Action para buscar o status de menor de idade diretamente do banco de dados
 */
export async function getMinorStatusAction(userId: string) {
  try {
    const isMinor = await guardianService.getMinorStatus(userId);
    
    return { success: true, data: { isMinor } };
  } catch (error) {
    console.error('Erro na action getMinorStatus:', error);
    return { 
      success: false, 
      error: 'Erro ao verificar status de menor de idade' 
    };
  }
}

/**
 * Server Action para verificar se um usuário pode agir como responsável de outro
 */
export async function canActAsGuardianAction(guardianUserId: string, minorUserId: string) {
  try {
    const canAct = await guardianService.canActAsGuardian(guardianUserId, minorUserId);
    
    return { success: true, data: { canAct } };
  } catch (error) {
    console.error('Erro na action canActAsGuardian:', error);
    return { 
      success: false, 
      error: 'Erro ao verificar permissão de responsável' 
    };
  }
} 