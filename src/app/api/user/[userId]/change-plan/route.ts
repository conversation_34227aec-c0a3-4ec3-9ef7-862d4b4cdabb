import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/services/supabase/server'
import { getCurrentUser } from '@/services/auth/actions/auth-actions'
import {
  getStudentActiveMemberships,
  updateMembershipStatus,
  createMembership
} from '@/app/(dashboard)/academia/actions/membership-actions'

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ userId: string }> }
) {
  try {
    const supabase = await createClient()
    const user = await getCurrentUser()

    if (!user) {
      return NextResponse.json(
        { error: 'Usuário não autenticado' },
        { status: 401 }
      )
    }

    const tenantId = user.app_metadata?.tenant_id

    if (!tenantId) {
      return NextResponse.json(
        { error: 'Tenant não identificado' },
        { status: 400 }
      )
    }

    const { userId } = await params
    const body = await request.json()
    const { planId, reason } = body

    if (!planId) {
      return NextResponse.json(
        { error: 'ID do plano é obrigatório' },
        { status: 400 }
      )
    }

    // Buscar o student_id baseado no user_id
    const { data: studentData, error: studentError } = await supabase
      .from('students')
      .select('id')
      .eq('user_id', userId)
      .eq('tenant_id', tenantId)
      .single()

    if (studentError || !studentData) {
      return NextResponse.json(
        { error: 'Aluno não encontrado' },
        { status: 404 }
      )
    }

    // Verificar se o plano existe e está ativo
    const { data: planData, error: planError } = await supabase
      .from('plans')
      .select('id, title, status')
      .eq('id', planId)
      .eq('tenant_id', tenantId)
      .eq('status', 'active')
      .single()

    if (planError || !planData) {
      return NextResponse.json(
        { error: 'Plano não encontrado ou não está ativo' },
        { status: 404 }
      )
    }

    // Verificar se já existe uma membership ativa usando server action
    const activeMembershipsResult = await getStudentActiveMemberships({
      alunoId: studentData.id
    }, tenantId)

    if (!activeMembershipsResult.success) {
      return NextResponse.json(
        { error: activeMembershipsResult.errors?._form || 'Erro ao verificar matrículas existentes' },
        { status: 500 }
      )
    }

    const existingMemberships = activeMembershipsResult.data as any[]
    const existingMembership = existingMemberships && existingMemberships.length > 0 ? existingMemberships[0] : null

    if (existingMembership) {
      // Se já tem uma membership ativa, verificar se é o mesmo plano
      if (existingMembership.plan.id === planId) {
        return NextResponse.json(
          { error: 'O aluno já está matriculado neste plano' },
          { status: 400 }
        )
      }

      // Cancelar a membership atual usando server action
      const cancelResult = await updateMembershipStatus({
        membershipId: existingMembership.id,
        novoStatus: 'canceled',
        motivo: reason || 'Troca de plano'
      }, tenantId)

      if (!cancelResult.success) {
        return NextResponse.json(
          { error: cancelResult.errors?._form || 'Erro ao cancelar plano atual' },
          { status: 500 }
        )
      }
    }

    // Criar nova membership usando server action
    const createResult = await createMembership({
      alunoId: studentData.id,
      planoId: planId,
      dataInicio: new Date().toISOString().split('T')[0], // Data atual no formato YYYY-MM-DD
      metadata: reason ? { reason } : {}
    }, tenantId)

    if (!createResult.success) {
      return NextResponse.json(
        { error: createResult.errors?._form || 'Erro ao criar nova matrícula' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      message: existingMembership
        ? `Plano alterado para ${planData.title} com sucesso`
        : `Plano ${planData.title} atribuído com sucesso`,
      membership: createResult.data
    })
  } catch (error) {
    console.error('Erro ao alterar plano:', error)
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
