import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/services/supabase/server'
import { getCurrentUser } from '@/services/auth/actions/auth-actions'
import { getStudentActiveMemberships } from '@/app/(dashboard)/academia/actions/membership-actions'

export async function GET(
  _request: NextRequest,
  { params }: { params: Promise<{ userId: string }> }
) {
  try {
    const supabase = await createClient()
    const user = await getCurrentUser()

    if (!user) {
      return NextResponse.json(
        { error: 'Usuário não autenticado' },
        { status: 401 }
      )
    }

    const tenantId = user.app_metadata?.tenant_id

    if (!tenantId) {
      return NextResponse.json(
        { error: 'Tenant não identificado' },
        { status: 400 }
      )
    }

    const { userId } = await params

    // Buscar o student_id baseado no user_id
    const { data: studentData, error: studentError } = await supabase
      .from('students')
      .select('id')
      .eq('user_id', userId)
      .eq('tenant_id', tenantId)
      .single()

    if (studentError || !studentData) {
      return NextResponse.json(
        { error: 'Aluno não encontrado' },
        { status: 404 }
      )
    }

    // Usar server action para buscar memberships ativas
    const result = await getStudentActiveMemberships({
      alunoId: studentData.id
    }, tenantId)

    if (!result.success) {
      return NextResponse.json(
        { error: result.errors?._form || 'Erro ao buscar matrículas' },
        { status: 500 }
      )
    }

    const memberships = result.data as any[]

    if (!memberships || memberships.length === 0) {
      return NextResponse.json(
        { error: 'Nenhum plano ativo encontrado' },
        { status: 404 }
      )
    }

    // Pegar a primeira membership ativa (assumindo que só há uma por vez)
    const membership = memberships[0]
    const plan = membership.plan
    const pricingConfig = plan.pricing_config || {}
    const accessConfig = plan.access_config || {}

    // Buscar nomes das modalidades se existirem IDs
    let modalityNames: string[] = []
    if (accessConfig.modalities && Array.isArray(accessConfig.modalities)) {
      const { data: modalitiesData } = await supabase
        .from('modalities')
        .select('name')
        .in('id', accessConfig.modalities)
        .eq('tenant_id', tenantId)

      modalityNames = modalitiesData?.map(m => m.name) || []
    }

    // Determinar o período de cobrança baseado no tipo de plano
    let billingPeriod = 'Mensal'
    const planType = pricingConfig.type || pricingConfig.tipo || 'recurring'

    if (planType === 'one-time') {
      billingPeriod = 'Pagamento único'
    } else if (pricingConfig.frequency && pricingConfig.frequency_number) {
      const frequency = pricingConfig.frequency
      const number = pricingConfig.frequency_number

      if (frequency === 'month') {
        billingPeriod = number === 1 ? 'Mensal' : `${number} meses`
      } else if (frequency === 'year') {
        billingPeriod = number === 1 ? 'Anual' : `${number} anos`
      } else if (frequency === 'week') {
        billingPeriod = number === 1 ? 'Semanal' : `${number} semanas`
      } else if (frequency === 'day') {
        billingPeriod = number === 1 ? 'Diário' : `${number} dias`
      }
    }

    const currentPlan = {
      id: plan.id,
      membership_id: membership.id,
      title: plan.title,
      status: membership.status,
      price: parseFloat(pricingConfig.amount || '0'),
      billing_period: billingPeriod,
      start_date: membership.start_date,
      end_date: membership.end_date,
      next_billing_date: membership.next_billing_date,
      modalities: modalityNames,
      pricing_config: pricingConfig // Incluir o pricing_config completo
    }

    return NextResponse.json(currentPlan)
  } catch (error) {
    console.error('Erro ao buscar plano atual:', error)
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
