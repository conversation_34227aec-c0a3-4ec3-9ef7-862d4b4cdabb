'use client'

import React, { useEffect } from 'react'
import { useForm, Controller } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { cn } from '@/lib/utils'
import { usePlanForm } from './PlanFormContext'
import { configPorAulaSchema } from '@/schemas/plan-schemas'

type PerSessionFormValues = z.infer<typeof configPorAulaSchema>

interface PlanFormPerSessionSectionProps {
  defaultValues?: Partial<PerSessionFormValues>
}

export function PlanFormPerSessionSection({
  defaultValues
}: PlanFormPerSessionSectionProps) {
  const { formData, updateSection } = usePlanForm()
  
  // Obter dados do pricing quando o tipo for per-session
  const perSessionData = formData.pricing?.tipo === 'per-session' ? formData.pricing : null

  const {
    register,
    formState: { errors },
    control,
    watch
  } = useForm<PerSessionFormValues>({
    resolver: zodResolver(configPorAulaSchema),
    defaultValues: {
      tipo: 'per-session',
      custo: perSessionData?.custo ? Number(perSessionData.custo) : defaultValues?.custo,
      taxaInscricao: perSessionData?.taxaInscricao ? Number(perSessionData.taxaInscricao) : defaultValues?.taxaInscricao,
      taxaAtraso: perSessionData?.taxaAtraso ? Number(perSessionData.taxaAtraso) : defaultValues?.taxaAtraso,
      diasAtraso: perSessionData?.diasAtraso ? Number(perSessionData.diasAtraso) : (defaultValues?.diasAtraso ?? 5)
    }
  })
  
  const formatToCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(value)
  }

  const handleCurrencyChange = (e: React.ChangeEvent<HTMLInputElement>, fieldOnChange: (value: number) => void) => {
    const value = e.target.value.replace(/\D/g, '')
    fieldOnChange(Number(value) / 100)
  }

  // Sincronizar mudanças com o context apenas se o tipo de pricing for per-session
  useEffect(() => {
    const subscription = watch((value) => {
      // Só atualizar se o tipo de pricing for per-session
      if (formData.pricing?.tipo === 'per-session') {
        // Atualizar apenas a seção pricing
        updateSection('pricing', value as PerSessionFormValues)
      }
    })
    return () => subscription.unsubscribe()
  }, [watch, updateSection, formData.pricing?.tipo])

  return (
    <div className="space-y-6">

      {/* Seção de Preço */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Preço</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Custo por aula */}
          <div className="space-y-2">
            <Label htmlFor="custo" className="text-sm font-medium">
              CUSTO
            </Label>
            <Controller
              name="custo"
              control={control}
              render={({ field }) => (
                <div className="relative">
                  <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground">
                    R$
                  </span>
                  <Input
                    id="custo"
                    placeholder="0,00"
                    type="text"
                    value={field.value ? formatToCurrency(field.value) : ''}
                    onChange={(e) => handleCurrencyChange(e, field.onChange)}
                    className={cn(
                      'pl-8',
                      errors.custo && 'border-red-500 focus:border-red-500'
                    )}
                  />
                </div>
              )}
            />
            {errors.custo && (
              <p className="text-sm text-red-600">{errors.custo.message}</p>
            )}
            <p className="text-xs text-muted-foreground">
              Valor cobrado por cada aula frequentada
            </p>
          </div>
        </div>
      </div>

      {/* Seção de Taxas */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Taxas</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Taxa de Inscrição */}
          <div className="space-y-2">
            <Label htmlFor="taxaInscricao" className="text-sm font-medium">
              TAXA DE INSCRIÇÃO
            </Label>
            <Controller
              name="taxaInscricao"
              control={control}
              render={({ field }) => (
                <div className="relative">
                  <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground">
                    R$
                  </span>
                  <Input
                    id="taxaInscricao"
                    placeholder="0,00"
                    type="text"
                    value={field.value ? formatToCurrency(field.value) : ''}
                    onChange={(e) => handleCurrencyChange(e, field.onChange)}
                    className="pl-8"
                  />
                </div>
              )}
            />
            <p className="text-xs text-muted-foreground">
              Taxa única cobrada na inscrição do plano
            </p>
          </div>

          {/* Taxa de Atraso */}
          <div className="space-y-2">
            <Label htmlFor="taxaAtraso" className="text-sm font-medium">
              COBRAR TAXA DE ATRASO
            </Label>
            <div className="space-y-2">
              <Controller
                name="taxaAtraso"
                control={control}
                render={({ field }) => (
                  <div className="relative">
                    <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground">
                      R$
                    </span>
                    <Input
                      id="taxaAtraso"
                      placeholder="0,00"
                      type="text"
                      value={field.value ? formatToCurrency(field.value) : ''}
                      onChange={(e) => handleCurrencyChange(e, field.onChange)}
                      className="pl-8"
                    />
                  </div>
                )}
              />
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <span>quando</span>
                <Input
                  placeholder="5"
                  type="number"
                  min="1"
                  {...register('diasAtraso', { valueAsNumber: true })}
                  className="w-16 text-center"
                />
                <span>dias em atraso</span>
              </div>
            </div>
            <p className="text-xs text-muted-foreground">
              Taxa aplicada quando o pagamento estiver em atraso
            </p>
          </div>
        </div>
      </div>

      {/* Informações Adicionais */}
      <div className="p-4 bg-amber-50 dark:bg-amber-950/20 rounded-lg border-l-4 border-amber-500 dark:border-amber-400">
        <h4 className="font-medium text-amber-800 dark:text-amber-200 mb-2">Como funciona o pagamento por aula:</h4>
        <ul className="text-sm text-amber-700 dark:text-amber-300 space-y-1">
          <li>• O valor é cobrado automaticamente após cada aula frequentada</li>
          <li>• A cobrança é processada no final do dia da aula</li>
          <li>• Aulas canceladas ou não comparecidas não são cobradas</li>
          <li>• O histórico de pagamentos pode ser consultado no financeiro</li>
        </ul>
      </div>
    </div>
  )
} 