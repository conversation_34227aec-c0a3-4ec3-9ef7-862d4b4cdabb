'use client'

import React, { useEffect } from 'react'
import { useF<PERSON>, Controller } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { configuracaoDuracaoSchema as durationSchema } from '@/schemas/plan-schemas'
import { Input } from '@/components/ui/input'
import DatePicker from '@/components/ui/calendar-01'
import { Label } from '@/components/ui/label'
import { Button } from '@/components/ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Checkbox } from '@/components/ui/checkbox'
import { TooltipProvider, TooltipRoot, TooltipTrigger, TooltipContent } from '@/components/ui/tooltip'
import { Calendar, Clock, CalendarDays, Plus, X, Info } from 'lucide-react'
import { cn } from '@/lib/utils'
import { usePlanForm } from './PlanFormContext'
import { parseLocalDate } from '@/utils/format'
import { startOfToday } from 'date-fns'

type DurationFormValues = z.infer<typeof durationSchema>

interface PlanFormDurationSectionProps {
  defaultValues?: Partial<DurationFormValues>
}

const unidadesTempoLabels = {
  days: 'Dias',
  weeks: 'Semanas',
  months: 'Meses',
  years: 'Anos'
}

export function PlanFormDurationSection({
  defaultValues
}: PlanFormDurationSectionProps) {
  const { formData, updateSection, submissionErrors } = usePlanForm()

  // Função para gerar a mensagem do tooltip baseada na configuração de pricing
  const getDurationTooltipMessage = () => {
    const pricingData = formData.pricing as any

    if (!pricingData || pricingData.tipo !== 'recurring') {
      return 'Configure a duração da assinatura de acordo com suas necessidades.'
    }

    const valor = Number(pricingData.valor) || 0
    const numeroFrequencia = Number(pricingData.numeroFrequencia) || 1
    const frequencia = pricingData.frequencia || 'month'

    const frequenciaLabels: Record<string, string> = {
      day: numeroFrequencia === 1 ? 'dia' : 'dias',
      week: numeroFrequencia === 1 ? 'semana' : 'semanas',
      month: numeroFrequencia === 1 ? 'mês' : 'meses',
      year: numeroFrequencia === 1 ? 'ano' : 'anos'
    }

    const frequenciaLabel = frequenciaLabels[frequencia] || frequencia
    const periodoCobranca = numeroFrequencia > 1 ? `${numeroFrequencia} ${frequenciaLabel}` : frequenciaLabel

    return `Configure de acordo: Se o plano cobra R$ ${valor.toFixed(2).replace('.', ',')} a cada ${periodoCobranca}, a duração deve ser configurada como ${numeroFrequencia} ${frequenciaLabels[frequencia]} para que a assinatura dure exatamente o período de cobrança.`
  }

  const durationData = formData.duration as any

  const dv = (defaultValues ?? {}) as any

  const defaultDurationValues = {
    tipo: durationData?.tipo || dv?.tipo || 'ongoing',
    duracao: durationData?.duracao ?? dv?.duracao ?? 1,
    unidadeTempo: durationData?.unidadeTempo ?? dv?.unidadeTempo ?? 'months',
    dataInicio: durationData?.dataInicio ?? dv?.dataInicio ?? '',
    dataFim: durationData?.dataFim ?? dv?.dataFim ?? '',
    periodosAdicionais: durationData?.periodosAdicionais ?? dv?.periodosAdicionais ?? [],
    adicionarDuracaoRenovacao: durationData?.adicionarDuracaoRenovacao ?? dv?.adicionarDuracaoRenovacao ?? false,
    cobrarTaxaRenovacao: durationData?.cobrarTaxaRenovacao ?? dv?.cobrarTaxaRenovacao ?? false,
    opcaoRenovacao: durationData?.opcaoRenovacao ?? dv?.opcaoRenovacao ?? 'auto-renew',
    taxaCancelamento: durationData?.taxaCancelamento ?? dv?.taxaCancelamento ?? 0,
    mostrarPeriodosAtivos: durationData?.mostrarPeriodosAtivos ?? dv?.mostrarPeriodosAtivos ?? false,
    agendarPrimeiroPagamento: durationData?.agendarPrimeiroPagamento ?? dv?.agendarPrimeiroPagamento ?? false,
  } as unknown as DurationFormValues

  useEffect(() => {
    // Só atualiza se não houver dados de duração já carregados ou se for apenas o tipo padrão
    if (!durationData || Object.keys(durationData).length <= 1) {
      updateSection('duration', defaultDurationValues)
    }
  }, [])

  const {
    register,
    formState: { errors },
    watch,
    setValue,
    getValues,
    control,
  } = useForm<DurationFormValues>({
    resolver: zodResolver(durationSchema),
    defaultValues: defaultDurationValues,
  })

  const selectedType = watch('tipo')
  const periodosAdicionais = watch('periodosAdicionais') || []
  const adicionarDuracaoRenovacao = watch('adicionarDuracaoRenovacao')
  const cobrarTaxaRenovacao = watch('cobrarTaxaRenovacao')
  const mostrarPeriodosAtivos = watch('mostrarPeriodosAtivos')
  const agendarPrimeiroPagamento = watch('agendarPrimeiroPagamento')

  // ADICIONADO: variáveis de erro para dataInicio e dataFim
  const dataInicioError = (errors as any)?.dataInicio?.message || submissionErrors?.duration?.dataInicio?._errors?.[0]
  const dataFimError   = (errors as any)?.dataFim?.message   || submissionErrors?.duration?.dataFim?._errors?.[0]

  // Sincronizar mudanças com o context
  useEffect(() => {
    const subscription = watch((value) => {
      updateSection('duration', value as DurationFormValues)
    })
    return () => subscription.unsubscribe()
  }, [watch, updateSection])



  const adicionarPeriodo = () => {
    const periodos = getValues('periodosAdicionais') ?? []
    setValue('periodosAdicionais', [...periodos, { inicio: '', fim: '' }])
  }

  const removerPeriodo = (index: number) => {
    const periodos = getValues('periodosAdicionais') ?? []
    setValue('periodosAdicionais', periodos.filter((_, i) => i !== index))
  }

  const atualizarPeriodo = (index: number, campo: 'inicio' | 'fim', valor: string) => {
    const periodos = getValues('periodosAdicionais') ?? []
    periodos[index][campo] = valor
    setValue('periodosAdicionais', [...periodos])
  }

  const durationError = submissionErrors?.duration?.tipo?._errors?.[0]

  return (
    <div className="space-y-6">
      {/* Seleção do Tipo de Duração */}
      <div className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {/* Contínuo */}
          <div 
            className={cn(
              'p-6 border-2 rounded-lg cursor-pointer transition-all hover:border-teal-300 dark:hover:border-teal-600',
              selectedType === 'ongoing' 
                ? 'border-teal-300 dark:border-teal-600 bg-teal-50 dark:bg-teal-900/20' 
                : 'border-border'
            )}
            onClick={() => setValue('tipo', 'ongoing')}
          >
            <div className="flex flex-col items-center text-center space-y-3">
              <div className={cn(
                'p-3 rounded-lg',
                selectedType === 'ongoing' 
                  ? 'bg-teal-100 dark:bg-teal-900/30 text-teal-600 dark:text-teal-400' 
                  : 'bg-muted text-muted-foreground'
              )}>
                <Clock className="h-6 w-6" />
              </div>
              <div>
                <h3 className="font-medium">Contínuo</h3>
                <p className="text-sm text-muted-foreground mt-1">Assinatura sem data de término</p>
              </div>
            </div>
          </div>

          {/* Duração Limitada */}
          <div 
            className={cn(
              'p-6 border-2 rounded-lg cursor-pointer transition-all hover:border-teal-300 dark:hover:border-teal-600',
              selectedType === 'limited' 
                ? 'border-teal-300 dark:border-teal-600 bg-teal-50 dark:bg-teal-900/20' 
                : 'border-border'
            )}
            onClick={() => setValue('tipo', 'limited')}
          >
            <div className="flex flex-col items-center text-center space-y-3">
              <div className={cn(
                'p-3 rounded-lg',
                selectedType === 'limited' 
                  ? 'bg-teal-100 dark:bg-teal-900/30 text-teal-600 dark:text-teal-400' 
                  : 'bg-muted text-muted-foreground'
              )}>
                <Calendar className="h-6 w-6" />
              </div>
              <div>
                <h3 className="font-medium">Duração Limitada</h3>
                <p className="text-sm text-muted-foreground mt-1">Assinatura com prazo determinado</p>
              </div>
            </div>
          </div>

          {/* Datas Específicas */}
          <div 
            className={cn(
              'p-6 border-2 rounded-lg cursor-pointer transition-all hover:border-teal-300 dark:hover:border-teal-600',
              selectedType === 'specific' 
                ? 'border-teal-300 dark:border-teal-600 bg-teal-50 dark:bg-teal-900/20' 
                : 'border-border'
            )}
            onClick={() => setValue('tipo', 'specific')}
          >
            <div className="flex flex-col items-center text-center space-y-3">
              <div className={cn(
                'p-3 rounded-lg',
                selectedType === 'specific' 
                  ? 'bg-teal-100 dark:bg-teal-900/30 text-teal-600 dark:text-teal-400' 
                  : 'bg-muted text-muted-foreground'
              )}>
                <CalendarDays className="h-6 w-6" />
              </div>
              <div>
                <h3 className="font-medium">Datas Específicas</h3>
                <p className="text-sm text-muted-foreground mt-1">Assinatura entre datas específicas</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Configurações da Duração Limitada */}
      {selectedType === 'limited' && (
        <div className="space-y-6 p-6 bg-muted/50 rounded-lg">
          <div>
            <div className="flex items-center gap-2 mb-3">
              <Label className="text-sm font-medium">DURAÇÃO DA ASSINATURA</Label>
              <TooltipProvider>
                <TooltipRoot>
                  <TooltipTrigger asChild>
                    <Info className="h-4 w-4 text-muted-foreground hover:text-foreground cursor-help" />
                  </TooltipTrigger>
                  <TooltipContent side="top" className="max-w-xs">
                    <p className="text-sm">{getDurationTooltipMessage()}</p>
                  </TooltipContent>
                </TooltipRoot>
              </TooltipProvider>
            </div>
            <div className="flex gap-3 items-center">
              <Input
                type="number"
                min="1"
                placeholder="1"
                className="w-20"
                {...register('duracao', { valueAsNumber: true })}
              />
              <Select
                value={watch('unidadeTempo')}
                onValueChange={(value) => setValue('unidadeTempo', value as any)}
              >
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {Object.entries(unidadesTempoLabels).map(([value, label]) => (
                    <SelectItem key={value} value={value}>{label}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <Checkbox 
                id="adicionarDuracaoRenovacao"
                checked={adicionarDuracaoRenovacao}
                onCheckedChange={(checked) => setValue('adicionarDuracaoRenovacao', checked as boolean)}
              />
              <Label htmlFor="adicionarDuracaoRenovacao" className="text-sm">
                Adicionar duração restante à data de expiração ao renovar assinaturas ativas
              </Label>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox 
                id="cobrarTaxaRenovacao"
                checked={cobrarTaxaRenovacao}
                onCheckedChange={(checked) => setValue('cobrarTaxaRenovacao', checked as boolean)}
              />
              <Label htmlFor="cobrarTaxaRenovacao" className="text-sm">
                Cobrar taxa de inscrição na renovação
              </Label>
            </div>
          </div>

          <div className="space-y-3">
            <Label className="text-sm font-medium">AO FINAL DA ASSINATURA</Label>
            <Select 
              value={watch('opcaoRenovacao')} 
              onValueChange={(value) => setValue('opcaoRenovacao', value as any)}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="auto-renew">Renovar automaticamente duração original</SelectItem>
                <SelectItem value="manual">Renovação manual</SelectItem>
              </SelectContent>
            </Select>
            <p className="text-sm text-muted-foreground">
              O período de renovação automática renovará a assinatura para o período do ciclo de pagamento. 
              Por exemplo, para um cronograma de pagamento mensal, a assinatura será renovada por 1 mês a cada vez 
              (ou seja, assinatura mês a mês).
            </p>
          </div>

          <div className="space-y-3">
            <Label htmlFor="taxaCancelamento" className="text-sm font-medium">TAXA DE CANCELAMENTO</Label>
            <div className="flex items-center gap-2">
              <span className="text-sm">R$</span>
              <Input
                id="taxaCancelamento"
                type="number"
                min="0"
                step="0.01"
                placeholder="0,00"
                className="w-32"
                {...register('taxaCancelamento', { valueAsNumber: true })}
              />
            </div>
            <p className="text-sm text-muted-foreground">
              Cobrar uma taxa se a assinatura for cancelada antes do final do prazo
            </p>
          </div>
        </div>
      )}

      {/* Configurações de Datas Específicas */}
      {selectedType === 'specific' && (
        <div className="space-y-6 p-6 bg-muted/50 rounded-lg">
          <div>
            <Label className="text-sm font-medium mb-3 block">DURAÇÃO</Label>
            <p className="text-sm text-muted-foreground mb-4">
              A assinatura funciona entre duas datas específicas. Você pode configurar múltiplos períodos de inscrição.
            </p>
            
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-1">
                  <Label className="text-sm font-medium">INÍCIO</Label>
                  <Controller
                    name="dataInicio"
                    control={control}
                    render={({ field }) => (
                      <DatePicker
                        date={parseLocalDate(field.value) ?? undefined}
                        onDateChange={(d) => field.onChange(d ? d.toISOString().substring(0, 10) : '')}
                        placeholder="Selecionar data"
                        className={cn(dataInicioError && 'border-red-500 focus:border-red-500')}
                        minDate={startOfToday()}
                      />
                    )}
                  />
                  {dataInicioError && (
                    <p className="text-sm text-red-600 mt-1">{dataInicioError}</p>
                  )}
                </div>
                <div className="space-y-1">
                  <Label className="text-sm font-medium">FIM</Label>
                  <Controller
                    name="dataFim"
                    control={control}
                    render={({ field }) => (
                      <DatePicker
                        date={parseLocalDate(field.value) ?? undefined}
                        onDateChange={(d) => field.onChange(d ? d.toISOString().substring(0, 10) : '')}
                        placeholder="Selecionar data"
                        className={cn(dataFimError && 'border-red-500 focus:border-red-500')}
                        minDate={parseLocalDate(watch('dataInicio')) || undefined}
                      />
                    )}
                  />
                  {dataFimError && (
                    <p className="text-sm text-red-600 mt-1">{dataFimError}</p>
                  )}
                </div>
              </div>

              {/* Períodos Adicionais */}
              {periodosAdicionais.map((periodo, index) => {
                const errorInicio = (errors as any)?.periodosAdicionais?.[index]?.inicio?.message || submissionErrors?.duration?.periodosAdicionais?.[index]?.inicio?._errors?.[0]
                const errorFim    = (errors as any)?.periodosAdicionais?.[index]?.fim?.message    || submissionErrors?.duration?.periodosAdicionais?.[index]?.fim?._errors?.[0]
                return (
                  <div key={index} className="p-4 border rounded-lg">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-1">
                        <Label className="text-sm font-medium">INÍCIO</Label>
                        <DatePicker
                          date={parseLocalDate(periodo.inicio) ?? undefined}
                          onDateChange={(d) => atualizarPeriodo(index, 'inicio', d ? d.toISOString().substring(0,10) : '')}
                          placeholder="Selecionar data"
                          className={cn(errorInicio && 'border-red-500 focus:border-red-500')}
                          minDate={(() => {
                            const prevEndStr = index === 0 ? watch('dataFim') : watch(`periodosAdicionais.${index - 1}.fim` as const) as unknown as string;
                            return parseLocalDate(prevEndStr) ?? startOfToday();
                          })()}
                        />
                        {errorInicio && <p className="text-sm text-red-600 mt-1">{errorInicio}</p>}
                      </div>
                      <div className="flex gap-2">
                        <div className="flex-1 space-y-1">
                          <Label className="text-sm font-medium">FIM</Label>
                          <DatePicker
                            date={parseLocalDate(periodo.fim) ?? undefined}
                            onDateChange={(d) => atualizarPeriodo(index, 'fim', d ? d.toISOString().substring(0,10) : '')}
                            placeholder="Selecionar data"
                            className={cn(errorFim && 'border-red-500 focus:border-red-500')}
                            minDate={parseLocalDate(periodo.inicio) ?? startOfToday()}
                          />
                          {errorFim && <p className="text-sm text-red-600 mt-1">{errorFim}</p>}
                        </div>
                        <div className="flex items-start pt-6">
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={() => removerPeriodo(index)}
                            className="text-red-600 hover:text-red-700"
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  </div>
                )
              })}

              <Button
                type="button"
                variant="outline"
                onClick={adicionarPeriodo}
                className="w-full text-teal-600 border-teal-300 hover:bg-teal-50 dark:text-teal-400 dark:border-teal-600 dark:hover:bg-teal-900/20"
              >
                <Plus className="h-4 w-4 mr-2" />
                Adicionar Período de Inscrição
              </Button>
            </div>
          </div>

          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <Checkbox 
                id="mostrarPeriodosAtivos"
                checked={mostrarPeriodosAtivos}
                onCheckedChange={(checked) => setValue('mostrarPeriodosAtivos', checked as boolean)}
              />
              <Label htmlFor="mostrarPeriodosAtivos" className="text-sm">
                Mostrar períodos de inscrição contínuos no formulário de inscrição
              </Label>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox 
                id="agendarPrimeiroPagamento"
                checked={agendarPrimeiroPagamento}
                onCheckedChange={(checked) => setValue('agendarPrimeiroPagamento', checked as boolean)}
              />
              <Label htmlFor="agendarPrimeiroPagamento" className="text-sm">
                Agendar primeiro pagamento para data de início da assinatura
              </Label>
            </div>
          </div>

          <div className="space-y-3">
            <Label htmlFor="taxaCancelamento" className="text-sm font-medium">TAXA DE CANCELAMENTO</Label>
            <div className="flex items-center gap-2">
              <span className="text-sm">R$</span>
              <Input
                id="taxaCancelamento"
                type="number"
                min="0"
                step="0.01"
                placeholder="0,00"
                className="w-32"
                {...register('taxaCancelamento', { valueAsNumber: true })}
              />
            </div>
            <p className="text-sm text-muted-foreground">
              Cobrar uma taxa se a assinatura for cancelada antes do final do prazo
            </p>
          </div>
        </div>
      )}

      {durationError && (
        <p className="text-sm text-red-600 mt-2">{durationError}</p>
      )}

      {errors.tipo && (
        <p className="text-sm text-red-600">{errors.tipo.message}</p>
      )}
    </div>
  )
} 