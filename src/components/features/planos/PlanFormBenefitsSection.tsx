'use client'
// @ts-nocheck

import React, { useEffect } from 'react'
import { useForm, useFieldArray } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { beneficiosPlanoSchema } from '@/schemas/plan-schemas'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { Plus, X } from 'lucide-react'
import { cn } from '@/lib/utils'
import { usePlanForm } from './PlanFormContext'

// Tipos derivados do schema
type BenefitsFormValues = z.infer<typeof beneficiosPlanoSchema>

interface PlanFormBenefitsSectionProps {
  defaultValues?: BenefitsFormValues
}

export function PlanFormBenefitsSection({ defaultValues }: PlanFormBenefitsSectionProps) {
  const { formData, updateSection, submissionErrors } = usePlanForm()

  const {
    register,
    control,
    watch,
    formState: { errors },
    trigger,
  } = useForm<{ benefits: BenefitsFormValues }>({
    mode: 'onChange',
    reValidateMode: 'onChange',
    resolver: zodResolver(z.object({ benefits: beneficiosPlanoSchema })),
    defaultValues: {
      benefits: (formData.benefits && formData.benefits.length > 0
        ? formData.benefits
        : defaultValues) || [''],
    },
  })

  // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
  const { fields, append, remove } = useFieldArray<any>({
    control,
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    name: 'benefits' as never,
  })

  // Sincronizar mudanças com o contexto
  useEffect(() => {
    const subscription = watch((value) => {
      updateSection('benefits', value.benefits as BenefitsFormValues)
    })
    return () => subscription.unsubscribe()
  }, [watch, updateSection])

  const handleAddBenefit = () => {
    append('' as any)
    // Revalida imediatamente para exibir possíveis erros do novo campo
    setTimeout(() => {
      trigger()
    }, 0)
  }
  const handleRemoveBenefit = (index: number) => remove(index)

  // Erro geral do array (mínimo de 1 item)
  const benefitsError = submissionErrors?.benefits?._errors?.[0]

  const mapError = (msg?: string) => {
    if (!msg) return undefined
    if (msg === 'Required' || msg === 'String must contain at least 2 character(s)') {
      return 'Adicione pelo menos 1 benefício.'
    }
    return msg
  }

  // Nenhum submit interno necessário; atualização ocorre via watch

  return (
    <div className="space-y-6">
      <div className="space-y-2">
        <h3 className="text-lg font-semibold">Benefícios</h3>
        <p className="text-sm text-muted-foreground">Liste os benefícios que este plano oferece aos alunos.</p>
      </div>

      <div className="space-y-4">
        {fields.map((field, index) => (
          <div key={field.id} className="flex items-center gap-3">
            <div className="flex-1 space-y-1">
              <Input
                placeholder="Ex: Acesso ilimitado às modalidades"
                {...register(`benefits.${index}` as const)}
                className={cn('w-full', (errors.benefits?.[index] || benefitsError) && 'border-red-500 focus:border-red-500')}
              />
              {/* Erro por índice */}
              {(() => {
                const fieldErr: any = errors.benefits?.[index]
                const rawMsg =
                  (fieldErr?.message as string | undefined) ||
                  (Array.isArray(fieldErr?._errors)
                    ? (fieldErr?._errors?.[0] as string | undefined)
                    : undefined)

                const formatted = mapError(rawMsg)
                return formatted ? (
                  <p className="text-sm text-red-600">{formatted}</p>
                ) : null
              })()}
            </div>
            {fields.length > 1 && (
              <Button
                type="button"
                size="icon"
                variant="ghost"
                onClick={() => handleRemoveBenefit(index)}
              >
                <X className="h-4 w-4" />
              </Button>
            )}
          </div>
        ))}

        {/* Erro vindo de submissionErrors (validação final do formulário) */}
        {mapError(benefitsError) && (
          <p className="text-sm text-red-600">{mapError(benefitsError)}</p>
        )}

        {/* Erro de validação raiz (array) capturado pelo react-hook-form */}
        {(() => {
          const rootErr: any = errors.benefits
          const rootMsg: string | undefined =
            typeof rootErr?.message === 'string'
              ? rootErr.message
              : Array.isArray(rootErr?._errors)
                ? rootErr._errors?.[0]
                : undefined

          const formatted = mapError(rootMsg)
          return formatted ? (
            <p className="text-sm text-red-600">{formatted}</p>
          ) : null
        })()}

        <Button type="button" variant="outline" onClick={handleAddBenefit} className="flex items-center gap-2">
          <Plus className="h-4 w-4" />
          Adicionar Benefício
        </Button>
      </div>
    </div>
  )
} 