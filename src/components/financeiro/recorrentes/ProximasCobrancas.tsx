'use client';

import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Clock } from 'lucide-react';
import { useEffect, useState } from 'react';
import { getNextCharges, type NextCharge } from '@/app/(dashboard)/financeiro/recorrentes/actions';
import { formatDateBrazil } from '@/utils/format';

interface Cobranca {
  id: string;
  nomeAluno: string;
  tipoPlano: string;
  proximaCobranca: string;
  valor: number;
  metodoPagamento: string;
  diasAteVencimento?: number;
}

interface CobrancaItemProps {
  cobranca: Cobranca;
}

function CobrancaItem({ cobranca }: CobrancaItemProps) {
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL',
    }).format(value);
  };

  const formatDate = (dateString: string) => {
    return formatDateBrazil(dateString);
  };

  const getDaysUntilDueText = (days?: number) => {
    if (days === undefined) return '';
    if (days < 0) return `Vencido há ${Math.abs(days)} dia${Math.abs(days) > 1 ? 's' : ''}`;
    if (days === 0) return 'Vence hoje';
    if (days === 1) return 'Vence amanhã';
    return `Vence em ${days} dias`;
  };

  const getDaysUntilDueColor = (days?: number) => {
    if (days === undefined) return 'text-muted-foreground';
    if (days < 0) return 'text-red-600 font-semibold';
    if (days === 0) return 'text-orange-600 font-semibold';
    if (days <= 3) return 'text-yellow-600 font-medium';
    return 'text-muted-foreground';
  };

  return (
    <div className="flex items-center justify-between p-3 border-0 shadow-sm bg-gradient-to-r from-white to-gray-50/30 dark:from-gray-800/50 dark:to-gray-700/30 rounded-lg hover:shadow-md transition-all duration-200">
      <div className="flex-1">
        <p className="font-medium">{cobranca.nomeAluno}</p>
        <p className="text-sm text-muted-foreground">{cobranca.tipoPlano}</p>
        <div className="flex items-center gap-2 mt-1">
          <p className="text-sm text-muted-foreground">
            {formatDate(cobranca.proximaCobranca)}
          </p>
          {cobranca.diasAteVencimento !== undefined && (
            <p className={`text-xs font-medium ${getDaysUntilDueColor(cobranca.diasAteVencimento)}`}>
              {getDaysUntilDueText(cobranca.diasAteVencimento)}
            </p>
          )}
        </div>
      </div>
      <div className="text-right">
        <p className="font-medium">{formatCurrency(cobranca.valor)}</p>
      </div>
    </div>
  );
}

interface ProximasCobrancasProps {
  cobrancas?: Cobranca[];
}

export function ProximasCobrancas({ cobrancas }: ProximasCobrancasProps) {
  const [charges, setCharges] = useState<Cobranca[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function loadCharges() {
      if (cobrancas) {
        setCharges(cobrancas);
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        setError(null);

        const result = await getNextCharges(4);

        if (result.success && result.data) {
          const formattedCharges: Cobranca[] = result.data.charges.map((charge: NextCharge) => ({
            id: charge.id,
            nomeAluno: charge.student_name,
            tipoPlano: charge.plan_title,
            proximaCobranca: charge.due_date,
            valor: charge.amount,
            metodoPagamento: charge.payment_method,
            diasAteVencimento: charge.days_until_due
          }));

          setCharges(formattedCharges);
        } else {
          setError(result.errors?._form || 'Erro ao carregar cobranças');
        }
      } catch (err) {
        console.error('Erro ao carregar próximas cobranças:', err);
        setError('Erro interno ao carregar cobranças');
      } finally {
        setLoading(false);
      }
    }

    loadCharges();
  }, [cobrancas]);

  return (
    <Card className="border-0 shadow-lg bg-gradient-to-br from-white to-gray-50/50 dark:from-gray-900 dark:to-gray-800/50 overflow-hidden group hover:shadow-xl transition-all duration-300">
      <CardHeader className="pb-4 bg-gradient-to-r from-blue-50/50 to-transparent dark:from-blue-900/20 dark:to-transparent">
        <CardTitle className="text-lg font-semibold text-gray-700 dark:text-gray-300 flex items-center gap-2">
          <div className="p-1 bg-blue-100 dark:bg-blue-900/30 rounded-full">
            <Clock className="w-4 h-4 text-blue-600 dark:text-blue-400" />
          </div>
          Próximas Cobranças
        </CardTitle>
      </CardHeader>
      <CardContent className="pt-6">
        {loading ? (
          <div className="space-y-3">
            {[1, 2, 3].map((i) => (
              <div key={i} className="flex items-center justify-between p-3 border-0 shadow-sm bg-gradient-to-r from-white to-gray-50/30 dark:from-gray-800/50 dark:to-gray-700/30 rounded-lg">
                <div className="flex-1">
                  <div className="h-4 bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200 dark:from-gray-700 dark:via-gray-600 dark:to-gray-700 animate-pulse rounded w-3/4 mb-2 relative overflow-hidden">
                    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-shimmer"></div>
                  </div>
                  <div className="h-3 bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200 dark:from-gray-700 dark:via-gray-600 dark:to-gray-700 animate-pulse rounded w-1/2 mb-1 relative overflow-hidden">
                    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-shimmer"></div>
                  </div>
                  <div className="h-3 bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200 dark:from-gray-700 dark:via-gray-600 dark:to-gray-700 animate-pulse rounded w-1/3 relative overflow-hidden">
                    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-shimmer"></div>
                  </div>
                </div>
                <div className="text-right">
                  <div className="h-4 bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200 dark:from-gray-700 dark:via-gray-600 dark:to-gray-700 animate-pulse rounded w-16 mb-2 relative overflow-hidden">
                    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-shimmer"></div>
                  </div>
                  <div className="h-3 bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200 dark:from-gray-700 dark:via-gray-600 dark:to-gray-700 animate-pulse rounded w-12 relative overflow-hidden">
                    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-shimmer"></div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : error ? (
          <div className="text-center py-6">
            <p className="text-red-600 text-sm">{error}</p>
          </div>
        ) : charges.length === 0 ? (
          <div className="text-center py-6">
            <p className="text-muted-foreground text-sm">Nenhuma cobrança pendente encontrada</p>
          </div>
        ) : (
          <div className="space-y-3">
            {charges.map((cobranca) => (
              <CobrancaItem key={cobranca.id} cobranca={cobranca} />
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
