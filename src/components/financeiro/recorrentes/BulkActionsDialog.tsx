'use client';

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { AlertTriangle, Pause, Play, RotateCcw, X, Users } from "lucide-react";
import { User } from "@/app/(dashboard)/alunos/components/selectusers/types";

interface BulkActionsDialogProps {
  isOpen: boolean;
  onClose: () => void;
  selectedUsers: User[];
  onBulkAction: (action: string, userIds: string[]) => Promise<void>;
}

export function BulkActionsDialog({
  isOpen,
  onClose,
  selectedUsers,
  onBulkAction
}: BulkActionsDialogProps) {
  const [selectedAction, setSelectedAction] = useState<string>("");
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [isExecuting, setIsExecuting] = useState(false);

  const selectedCount = selectedUsers.length;

  const actionLabels: Record<string, { label: string; icon: React.ReactNode; description: string; variant?: 'default' | 'destructive' }> = {
    'pause-all': {
      label: 'Pausar Assinaturas',
      icon: <Pause className="h-4 w-4" />,
      description: 'Pausar todas as assinaturas selecionadas temporariamente'
    },
    'resume-all': {
      label: 'Retomar Assinaturas',
      icon: <Play className="h-4 w-4" />,
      description: 'Reativar todas as assinaturas pausadas selecionadas'
    },
    'retry-payments': {
      label: 'Tentar Cobrança Novamente',
      icon: <RotateCcw className="h-4 w-4" />,
      description: 'Tentar nova cobrança para pagamentos falhados'
    },
    'cancel-all': {
      label: 'Cancelar Assinaturas',
      icon: <X className="h-4 w-4" />,
      description: 'Cancelar permanentemente as assinaturas selecionadas',
      variant: 'destructive' as const
    }
  };

  const handleActionSelect = (action: string) => {
    setSelectedAction(action);
    setShowConfirmation(true);
  };

  const handleConfirmAction = async () => {
    if (!selectedAction) return;

    setIsExecuting(true);
    try {
      const userIds = selectedUsers.map(user => user.id);
      await onBulkAction(selectedAction, userIds);
      
      // Fechar diálogos após sucesso
      setShowConfirmation(false);
      setSelectedAction("");
      onClose();
    } catch (error) {
      console.error('Erro ao executar ação em massa:', error);
    } finally {
      setIsExecuting(false);
    }
  };

  const handleCancelAction = () => {
    setShowConfirmation(false);
    setSelectedAction("");
  };

  const currentActionInfo = selectedAction ? actionLabels[selectedAction] : null;

  return (
    <>
      {/* Dialog principal de seleção de ação */}
      <Dialog open={isOpen && !showConfirmation} onOpenChange={onClose}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              Ações em Massa - Assinaturas
            </DialogTitle>
            <DialogDescription>
              Selecione uma ação para aplicar a todas as assinaturas selecionadas
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4">
            <div className="p-3 bg-blue-50 border border-blue-200 dark:bg-blue-900/30 dark:border-blue-600 rounded-md">
              <p className="text-sm text-blue-800 dark:text-blue-300">
                <strong>{selectedCount}</strong> assinatura{selectedCount > 1 ? 's' : ''} selecionada{selectedCount > 1 ? 's' : ''}
              </p>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Escolha uma ação:</label>
              <Select onValueChange={handleActionSelect}>
                <SelectTrigger>
                  <SelectValue placeholder="Selecione uma ação..." />
                </SelectTrigger>
                <SelectContent>
                  {Object.entries(actionLabels).map(([key, info]) => (
                    <SelectItem 
                      key={key} 
                      value={key}
                      className={info.variant === 'destructive' ? 'text-red-600 focus:text-red-600 focus:bg-red-50' : ''}
                    >
                      <div className="flex items-center gap-2">
                        {info.icon}
                        <span>{info.label}</span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Lista dos usuários selecionados */}
            <div className="space-y-2">
              <label className="text-sm font-medium">Assinaturas selecionadas:</label>
              <div className="max-h-32 overflow-y-auto border rounded-md p-2 space-y-1">
                {selectedUsers.map((user) => (
                  <div key={user.id} className="flex items-center gap-2 text-sm">
                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                    <span className="font-medium">{user.name}</span>
                    <span className="text-muted-foreground">({user.email})</span>
                  </div>
                ))}
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={onClose}>
              Cancelar
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Dialog de confirmação */}
      <Dialog open={showConfirmation} onOpenChange={setShowConfirmation}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <div className="flex items-center gap-2">
              {currentActionInfo?.variant === 'destructive' ? (
                <AlertTriangle className="h-5 w-5 text-red-500" />
              ) : (
                currentActionInfo?.icon
              )}
              <DialogTitle className={currentActionInfo?.variant === 'destructive' ? 'text-red-600 dark:text-red-400' : ''}>
                Confirmar Ação
              </DialogTitle>
            </div>
            <DialogDescription>
              {currentActionInfo?.description}
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4">
            <Alert className={
              currentActionInfo?.variant === 'destructive' 
                ? 'border-red-200 bg-red-50 dark:border-red-600 dark:bg-red-900/30'
                : 'border-blue-200 bg-blue-50 dark:border-blue-600 dark:bg-blue-900/30'
            }>
              {currentActionInfo?.variant === 'destructive' ? (
                <AlertTriangle className="h-4 w-4 text-red-500" />
              ) : (
                <Users className="h-4 w-4 text-blue-500" />
              )}
              <AlertDescription className={
                currentActionInfo?.variant === 'destructive'
                  ? 'text-red-700 dark:text-red-300'
                  : 'text-blue-700 dark:text-blue-300'
              }>
                Você está prestes a <strong>{currentActionInfo?.label.toLowerCase()}</strong> para{' '}
                <strong>{selectedCount}</strong> assinatura{selectedCount > 1 ? 's' : ''}.
              </AlertDescription>
            </Alert>

            {currentActionInfo?.variant === 'destructive' && (
              <div className="p-3 bg-yellow-50 border border-yellow-200 dark:bg-yellow-900/30 dark:border-yellow-600 rounded-md">
                <p className="text-sm text-yellow-800 dark:text-yellow-300">
                  <strong>Atenção:</strong> Esta ação pode ser irreversível. 
                  Considere pausar as assinaturas ao invés de cancelá-las.
                </p>
              </div>
            )}

            <div className="space-y-2">
              <p className="text-sm font-medium">Assinaturas afetadas:</p>
              <div className="max-h-24 overflow-y-auto text-sm space-y-1">
                {selectedUsers.map((user) => (
                  <div key={user.id} className="flex items-center gap-2">
                    <div className="w-1.5 h-1.5 bg-gray-400 rounded-full"></div>
                    <span>{user.name}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>

          <DialogFooter className="gap-2">
            <Button 
              variant="outline" 
              onClick={handleCancelAction}
              disabled={isExecuting}
              className="flex-1"
            >
              Cancelar
            </Button>
            <Button 
              variant={currentActionInfo?.variant === 'destructive' ? 'destructive' : 'default'}
              onClick={handleConfirmAction}
              disabled={isExecuting}
              className="flex-1"
            >
              {isExecuting ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Executando...
                </>
              ) : (
                <>
                  {currentActionInfo?.icon}
                  <span className="ml-2">Confirmar</span>
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}