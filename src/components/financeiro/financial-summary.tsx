/**
 * Componente de Resumo Financeiro
 * Integra com o sistema de cobranças para exibir métricas reais do mês atual
 */

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { getMonthlyPaymentMetrics } from '@/services/billing'

/**
 * Formata valor monetário para exibição
 */
function formatCurrency(value: number): string {
  return new Intl.NumberFormat('pt-BR', {
    style: 'currency',
    currency: 'BRL'
  }).format(value)
}

/**
 * Formata número para exibição
 */
function formatNumber(value: number): string {
  return new Intl.NumberFormat('pt-BR').format(value)
}

/**
 * Componente servidor que busca e exibe métricas financeiras do mês atual
 */
export async function FinancialSummary() {
  // Buscar métricas de pagamentos do mês atual
  const metricsResult = await getMonthlyPaymentMetrics()
  
  // Valores padrão caso haja erro ou não existam dados
  const defaultMetrics = {
    totalRevenue: 0,
    paidPayments: 0,
    pendingPayments: 0,
    overduePayments: 0
  }

  const metrics = metricsResult.success ? metricsResult.data : defaultMetrics

  // Obter nome do mês atual para exibição
  const currentMonth = new Date().toLocaleDateString('pt-BR', { 
    month: 'long', 
    year: 'numeric' 
  })

  return (
    <Card>
      <CardHeader>
        <CardTitle>Resumo Financeiro - {currentMonth}</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid gap-4 md:grid-cols-4">
          {/* Receita do Mês */}
          <div className="text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
            <p className="text-2xl font-bold text-blue-600 dark:text-blue-400">
              {formatCurrency(metrics.totalRevenue || 0)}
            </p>
            <p className="text-sm text-gray-600 dark:text-gray-400">Receita do Mês</p>
          </div>

          {/* Pagamentos Confirmados */}
          <div className="text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
            <p className="text-2xl font-bold text-green-600 dark:text-green-400">
              {formatNumber(metrics.paidPayments || 0)}
            </p>
            <p className="text-sm text-gray-600 dark:text-gray-400">Pagamentos Confirmados</p>
          </div>

          {/* Pagamentos Pendentes */}
          <div className="text-center p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
            <p className="text-2xl font-bold text-yellow-600 dark:text-yellow-400">
              {formatNumber(metrics.pendingPayments || 0)}
            </p>
            <p className="text-sm text-gray-600 dark:text-gray-400">Pagamentos Pendentes</p>
          </div>

          {/* Pagamentos em Atraso */}
          <div className="text-center p-4 bg-red-50 dark:bg-red-900/20 rounded-lg">
            <p className="text-2xl font-bold text-red-600 dark:text-red-400">
              {formatNumber(metrics.overduePayments || 0)}
            </p>
            <p className="text-sm text-gray-600 dark:text-gray-400">Pagamentos em Atraso</p>
          </div>
        </div>

        {/* Informações adicionais se houver erro */}
        {!metricsResult.success && (
          <div className="mt-4 p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
            <p className="text-sm text-yellow-800 dark:text-yellow-200">
              Não foi possível carregar as métricas financeiras do mês atual. Exibindo valores padrão.
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  )
}