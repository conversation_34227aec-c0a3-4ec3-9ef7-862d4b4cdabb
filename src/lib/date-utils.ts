/**
 * Utilitários para manipulação de datas e timezone
 * Otimizado para timezone de Brasília (America/Sao_Paulo)
 */

const BRASILIA_TIMEZONE = 'America/Sao_Paulo';
const BRASILIA_LOCALE = 'pt-BR';

/**
 * Obtém a data/hora atual no timezone de Brasília
 */
export function getCurrentTimeBrasilia(): Date {
  return new Date();
}

/**
 * Formata data/hora no timezone de Brasília
 */
export function formatDateTimeBrasilia(
  dateString: string | Date, 
  options: Intl.DateTimeFormatOptions = {}
): string {
  const date = typeof dateString === 'string' ? new Date(dateString) : dateString;
  
  return date.toLocaleString(BRASILIA_LOCALE, {
    timeZone: BRASILIA_TIMEZONE,
    ...options
  });
}

/**
 * Obtém Date no timezone de Brasília para comparações
 */
export function getDateInBrasilia(dateString: string | Date): Date {
  const date = typeof dateString === 'string' ? new Date(dateString) : dateString;
  
  // Criar uma nova data baseada na string formatada no timezone de Brasília
  const brasiliaString = date.toLocaleString('en-CA', { timeZone: BRASILIA_TIMEZONE });
  return new Date(brasiliaString);
}

/**
 * Converte data para timestamp para comparação
 */
export function getTimestampForComparison(dateString: string | Date): number {
  const date = typeof dateString === 'string' ? new Date(dateString) : dateString;
  return date.getTime();
}

/**
 * Determina o status da aula baseado nos timestamps
 */
export function getClassStatus(
  startTime: string | Date,
  endTime: string | Date,
  currentStatus?: string
): 'scheduled' | 'completed' | 'ongoing' | 'cancelled' | 'rescheduled' {
  // Se a aula foi cancelada ou reagendada, manter o status do banco
  if (currentStatus === 'cancelled' || currentStatus === 'rescheduled') {
    return currentStatus as 'cancelled' | 'rescheduled';
  }
  
  const now = Date.now();
  const startTimestamp = getTimestampForComparison(startTime);
  const endTimestamp = getTimestampForComparison(endTime);
  
  if (now < startTimestamp) return 'scheduled';
  if (now > endTimestamp) return 'completed';
  return 'ongoing';
}

/**
 * Verifica se ainda está dentro do limite de 24h após o término da aula
 */
export function isWithin24HoursAfterEnd(endTime: string | Date): boolean {
  const now = Date.now();
  const endTimestamp = getTimestampForComparison(endTime);
  const twentyFourHoursInMs = 24 * 60 * 60 * 1000;
  const twentyFourHoursAfterEnd = endTimestamp + twentyFourHoursInMs;
  
  return now <= twentyFourHoursAfterEnd;
}

/**
 * Verifica se a aula está ativa (permite check-in)
 */
export function isClassActive(
  startTime: string | Date,
  endTime: string | Date,
  currentStatus?: string
): boolean {
  const status = getClassStatus(startTime, endTime, currentStatus);
  const isWithinTimeLimit = isWithin24HoursAfterEnd(endTime);
  
  return (status === 'ongoing' || status === 'scheduled') || 
         (status === 'completed' && isWithinTimeLimit);
}

/**
 * Formata horário para exibição (HH:MM)
 */
export function formatTime(dateString: string | Date): string {
  return formatDateTimeBrasilia(dateString, {
    hour: '2-digit',
    minute: '2-digit'
  });
}

/**
 * Formata data completa para exibição
 */
export function formatFullDate(dateString: string | Date): string {
  return formatDateTimeBrasilia(dateString, {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
}

/**
 * Formata data e horário para exibição
 */
export function formatDateTime(dateString: string | Date): string {
  return formatDateTimeBrasilia(dateString, {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
}
