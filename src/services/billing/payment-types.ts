/**
 * Tipos TypeScript para o Sistema de Cobranças
 * Baseado no documento: docs/planejamento-sistema-cobrancas.md
 */

// Enum para tipos de pagamento (conforme documento)
export type PaymentType =
  | 'recurring'        // Mensalidades recorrentes
  | 'signup_fee'       // Taxa de inscrição
  | 'graduation_fee'   // Taxa de graduação
  | 'late_fee'         // Multa por atraso
  | 'cancellation_fee' // Taxa de cancelamento
  | 'manual'           // Cobrança manual
  | 'product'          // Produtos/serviços extras

// Status de pagamento
export type PaymentStatus =
  | 'pending'              // Pendente
  | 'paid'                 // Pago
  | 'overdue'              // Vencido
  | 'canceled'             // Cancelado
  | 'awaiting_confirmation' // Aguardando confirmação do admin

// Estrutura expandida da tabela payments (conforme FASE 1 do documento)
export interface Payment {
  id: string
  tenant_id: string
  student_id: string
  amount: number
  currency: string
  status: PaymentStatus
  payment_method?: string
  paid_at?: string
  created_at: string
  updated_at?: string
  membership_id?: string
  
  // Novos campos (FASE 1)
  payment_type: PaymentType
  description?: string
  due_date?: string
  metadata: Record<string, any>
  reference_id?: string
  billing_cycle?: string
  attempt_count: number
  last_attempt_at?: string
}

// Dados para criar cobrança manual
export interface CreateManualPaymentData {
  alunoId: string
  valor: number
  descricao: string
  dataVencimento?: string
  metadata?: Record<string, any>
}

// Dados para criar taxa de graduação
export interface CreateGraduationFeeData {
  alunoId: string
  beltLevelId: string
  graduationId: string
}

// Dados para criar taxa de inscrição
export interface CreateSignupFeeData {
  membershipId: string
}

// Dados para criar taxa de cancelamento
export interface CreateCancellationFeeData {
  membershipId: string
  motivo?: string
}

// Dados para atualizar status de pagamento
export interface UpdatePaymentStatusData {
  paymentId: string
  novoStatus: PaymentStatus
  motivo?: string
}

// Dados para atualizar detalhes de pagamento (valores, datas, método, descrição)
export interface UpdatePaymentData {
  paymentId: string
  amount?: number
  status?: PaymentStatus
  payment_method?: string | null
  paid_at?: string | null
  due_date?: string
  description?: string
}

// Dados para buscar pagamentos por estudante
export interface GetPaymentsByStudentData {
  studentId: string
  limit?: number
  offset?: number
}

// Dados para buscar pagamentos por membership
export interface GetPaymentsByMembershipData {
  membershipId: string
  limit?: number
  offset?: number
}

// Dados para buscar todos os pagamentos
export interface GetAllPaymentsData {
  tenantId?: string
  limit?: number
  offset?: number
  status?: PaymentStatus
  paymentType?: PaymentType
  studentId?: string
  startDate?: string
  endDate?: string
  searchText?: string
}

// Métricas de pagamentos
export interface PaymentMetrics {
  totalPayments: number
  paidPayments: number
  pendingPayments: number
  failedPayments: number // Mantido para compatibilidade (agora representa overdue)
  totalRevenue: number
  averagePayment: number
  overduePayments: number
  monthlyRevenue: number
}

// Resultado das funções RPC
export interface PaymentRPCResult {
  success: boolean
  payment_id?: string
  message?: string
  error?: string
  data?: any
  hasMore?: boolean
}

// Dados para processar pagamentos em atraso
export interface ProcessOverduePaymentsData {
  tenantId?: string
}

// Resposta padrão das actions
export interface ActionResult<T = any> {
  success: boolean
  data?: T
  errors?: Record<string, string>
  hasMore?: boolean
}

// Configuração de cobrança recorrente
export interface RecurringBillingConfig {
  membershipId: string
  amount: number
  frequency: 'daily' | 'weekly' | 'monthly' | 'yearly'
  nextBillingDate: string
}

// Histórico de tentativas de pagamento
export interface PaymentAttempt {
  id: string
  payment_id: string
  attempt_number: number
  attempted_at: string
  status: PaymentStatus
  error_message?: string
  gateway_response?: Record<string, any>
}

// Configuração de multa por atraso
export interface LateFeeConfig {
  enabled: boolean
  amount?: number
  percentage?: number
  grace_days: number
  max_fee?: number
}

// Dados para relatórios de pagamentos
export interface PaymentReportData {
  startDate: string
  endDate: string
  paymentType?: PaymentType
  status?: PaymentStatus
  studentId?: string
  membershipId?: string
}

// Resumo de pagamentos por período
export interface PaymentSummary {
  period: string
  totalAmount: number
  paymentCount: number
  averageAmount: number
  paymentsByType: Record<PaymentType, number>
  paymentsByStatus: Record<PaymentStatus, number>
}
