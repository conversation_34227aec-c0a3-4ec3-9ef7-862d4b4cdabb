/**
 * Utilitário para formatar nomes de métodos de pagamento
 * Mapeia slugs dos métodos de pagamento para nomes legíveis
 */

import { FaPix } from 'react-icons/fa6'
import { FaRegMoneyBill1 } from 'react-icons/fa6'
import { CreditCard } from 'lucide-react'
import { ElementType } from 'react'

// Mapeamento dos slugs para nomes legíveis baseado na tabela payment_methods
const PAYMENT_METHOD_NAMES: Record<string, string> = {
  'pix': 'PIX',
  'cash': 'Dinheiro',
  'credit_card': 'Cartão de Crédito',
  'debit_card': 'Cartão de Débito',
  'bank_transfer': 'Transferência Bancária',
  'boleto': 'Boleto Bancário',
}

// Mapeamento dos slugs para ícones baseado na tabela payment_methods
const PAYMENT_METHOD_ICONS: Record<string, ElementType> = {
  'pix': FaPix,
  'cash': FaRegMoneyBill1,
  'credit_card': CreditCard,
  'debit_card': CreditCard,
  'bank_transfer': CreditCard,
  'boleto': CreditCard,
}

/**
 * Formata o nome do método de pagamento baseado no slug
 * @param paymentMethodSlug - Slug do método de pagamento (ex: 'pix', 'cash')
 * @returns Nome formatado do método de pagamento
 */
export function formatPaymentMethodName(paymentMethodSlug?: string | null): string {
  if (!paymentMethodSlug) {
    return 'Não especificado'
  }

  // Normalizar o slug para lowercase
  const normalizedSlug = paymentMethodSlug.toLowerCase().trim()
  
  // Retornar o nome mapeado ou o slug original se não encontrado
  return PAYMENT_METHOD_NAMES[normalizedSlug] || paymentMethodSlug
}

/**
 * Verifica se um método de pagamento é válido
 * @param paymentMethodSlug - Slug do método de pagamento
 * @returns true se o método é válido, false caso contrário
 */
export function isValidPaymentMethod(paymentMethodSlug?: string | null): boolean {
  if (!paymentMethodSlug) return false
  
  const normalizedSlug = paymentMethodSlug.toLowerCase().trim()
  return normalizedSlug in PAYMENT_METHOD_NAMES
}

/**
 * Obtém todos os métodos de pagamento disponíveis
 * @returns Array com todos os métodos de pagamento mapeados
 */
export function getAllPaymentMethods(): Array<{ slug: string; name: string }> {
  return Object.entries(PAYMENT_METHOD_NAMES).map(([slug, name]) => ({
    slug,
    name
  }))
}

/**
 * Obtém o ícone do método de pagamento baseado no slug
 * @param paymentMethodSlug - Slug do método de pagamento (ex: 'pix', 'cash')
 * @returns Componente do ícone ou undefined se não encontrado
 */
export function getPaymentMethodIconBySlug(paymentMethodSlug?: string | null): ElementType | undefined {
  if (!paymentMethodSlug) {
    return undefined
  }

  // Normalizar o slug para lowercase
  const normalizedSlug = paymentMethodSlug.toLowerCase().trim()

  // Retornar o ícone mapeado
  return PAYMENT_METHOD_ICONS[normalizedSlug]
}
