/**
 * Utilities for generating stable IDs that don't cause hydration issues
 */

let counter = 0;

/**
 * Generates a stable ID that won't cause hydration mismatches
 * Uses a counter instead of Date.now() or Math.random()
 */
export function generateStableId(prefix: string = 'id'): string {
  counter += 1;
  return `${prefix}-${counter}`;
}

/**
 * Generates a UUID-like string using crypto.randomUUID() when available
 * Falls back to a stable counter-based approach
 */
export function generateUUID(): string {
  if (typeof crypto !== 'undefined' && crypto.randomUUID) {
    return crypto.randomUUID();
  }
  
  // Fallback for environments without crypto.randomUUID
  return generateStableId('uuid');
}

/**
 * Resets the counter (useful for testing)
 */
export function resetCounter(): void {
  counter = 0;
}